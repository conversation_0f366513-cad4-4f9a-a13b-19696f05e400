# Arabic to English Numeral Conversion

## Overview

This feature automatically converts Arabic-Indic numerals (٠-٩) to ASCII numerals (0-9) in all number input fields throughout the application. This ensures consistent data format regardless of the user's keyboard language setting.

## Implementation

### Core Components

1. **TextHelpers.convertArabicNumeralsToEnglish()** - Utility function for converting Arabic numerals
2. **ArabicToEnglishNumeralFormatter** - Custom TextInputFormatter for automatic conversion
3. **Input Formatters Barrel File** - Centralized export for easy importing

### Affected Input Fields

The following input fields now automatically convert Arabic numerals to English numerals:

1. **Mobile Number Input** (`signup_login_view.dart`)
   - Format: 05xxxxxxxx (10 digits)
   - Includes digit-only filtering and length limiting

2. **OTP Input** (`mobile_otp_view.dart` and `otp_input_widget.dart`)
   - Format: 4-digit OTP codes
   - Individual digit input fields

3. **Birth Year Input** (`personal_info_view.dart`)
   - Format: 4-digit year (e.g., 1990)
   - Includes age validation (18-90 years old)

4. **Phone Input Widget** (`phone_input_widget.dart`)
   - International phone number input
   - Used in various authentication screens

### Usage

#### Basic Usage
```dart
TextField(
  inputFormatters: [
    const ArabicToEnglishNumeralFormatter(),
    FilteringTextInputFormatter.digitsOnly,
  ],
  keyboardType: TextInputType.number,
)
```

#### With Length Limiting
```dart
TextField(
  inputFormatters: [
    const ArabicToEnglishNumeralFormatter(),
    FilteringTextInputFormatter.digitsOnly,
    LengthLimitingTextInputFormatter(10), // For mobile numbers
  ],
  keyboardType: TextInputType.phone,
)
```

#### Import
```dart
import 'package:towasl/shared/widgets/input_formatters/input_formatters.dart';
```

## Technical Details

### Conversion Mapping
```
Arabic → English
٠ → 0
١ → 1
٢ → 2
٣ → 3
٤ → 4
٥ → 5
٦ → 6
٧ → 7
٨ → 8
٩ → 9
```

### Formatter Behavior
- **Real-time conversion**: Converts numerals as the user types
- **Cursor preservation**: Maintains cursor position after conversion
- **Selection handling**: Preserves text selection during conversion
- **Performance optimized**: Only processes text when Arabic numerals are detected

### File Structure
```
lib/
├── shared/
│   ├── helpers/
│   │   └── text_helpers.dart                    # Core conversion function
│   └── widgets/
│       └── input_formatters/
│           ├── arabic_to_english_numeral_formatter.dart  # Custom formatter
│           └── input_formatters.dart            # Barrel file
└── features/
    ├── authentication/
    │   ├── presentation/
    │   │   ├── views/
    │   │   │   ├── signup_login_view.dart       # Mobile input
    │   │   │   └── mobile_otp_view.dart         # OTP input
    │   │   └── widgets/
    │   │       ├── otp_input_widget.dart        # OTP widget
    │   │       └── phone_input_widget.dart      # Phone widget
    └── personal_info/
        └── presentation/
            └── views/
                └── personal_info_view.dart      # Birth year input
```

## Testing

### Unit Tests
- **TextHelpers Tests**: Verify conversion function works correctly
- **Formatter Tests**: Ensure input formatter handles all scenarios
- **Coverage**: All conversion scenarios and edge cases

### Test Files
- `test/shared/helpers/text_helpers_test.dart`
- `test/shared/widgets/input_formatters/arabic_to_english_numeral_formatter_test.dart`

### Running Tests
```bash
flutter test test/shared/helpers/text_helpers_test.dart
flutter test test/shared/widgets/input_formatters/arabic_to_english_numeral_formatter_test.dart
```

## Benefits

1. **User Experience**: Seamless input regardless of keyboard language
2. **Data Consistency**: All numeric data stored in English format
3. **Validation Compatibility**: Works with existing validation logic
4. **Backend Integration**: Ensures consistent data format for API calls
5. **Accessibility**: Supports users with Arabic keyboard settings

## Future Enhancements

1. **Additional Number Inputs**: Apply to any new numeric input fields
2. **Configuration**: Make conversion optional via app settings
3. **Localization**: Support for other numeral systems if needed
4. **Analytics**: Track usage patterns of Arabic vs English input

## Maintenance

- **Adding New Fields**: Include the formatter in any new numeric input fields
- **Testing**: Ensure all numeric inputs are tested with Arabic numerals
- **Documentation**: Update this document when adding new input fields
