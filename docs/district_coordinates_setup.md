# District Coordinates Setup

This document explains how to set up district coordinates in Firestore and how the app handles them.

## Overview

The app now supports latitude and longitude coordinates for districts stored in Firestore. When users capture their location via GPS or manually select a district, the app will use the district's coordinates if available.

## Firestore Structure

Districts are stored in the `app_settings/supported_cities` document with the following structure:

```json
{
  "SA": {
    "riyadh": {
      "name_ar": "الرياض",
      "name_en": "Riyadh",
      "districts": {
        "olaya": {
          "name_ar": "العليا",
          "name_en": "Olaya",
          "lat": 24.7136,
          "lng": 46.6753
        },
        "malaz": {
          "name_ar": "الملز",
          "name_en": "Malaz",
          "lat": 24.6877,
          "lng": 46.7219
        },
        "arqa": {
          "name_ar": "عرقة",
          "name_en": "Arqa",
          "lat": 24.6805,
          "lng": 46.5800
        }
      }
    },
    "jeddah": {
      "name_ar": "جدة",
      "name_en": "Jeddah",
      "districts": {
        "corniche": {
          "name_ar": "الكورنيش",
          "name_en": "Corniche",
          "lat": 21.5169,
          "lng": 39.1748
        }
      }
    }
  }
}
```

## Required Fields

For each district, the following fields are required:
- `name_ar`: Arabic name of the district
- `name_en`: English name of the district

Optional coordinate fields:
- `lat`: Latitude coordinate (double)
- `lng`: Longitude coordinate (double)

## How It Works

### 1. GPS Location Capture
When a user captures their location via GPS:
1. The app gets GPS coordinates and address information
2. It attempts to match the GPS-detected district name with districts in Firestore
3. If a match is found and the district has coordinates, the app updates the location to use the district's coordinates
4. If no match is found or the district has no coordinates, the app keeps the GPS coordinates

### 2. Manual District Selection
When a user manually selects a district:
1. If the selected district has coordinates, the app updates the location to use those coordinates
2. If the district has no coordinates, the app keeps the current GPS coordinates and only updates the district name

### 3. Location Storage
When the location is saved:
- The current coordinates (either GPS or district coordinates) are stored in `user_location.lat` and `user_location.lng`
- The district name is stored in `user_location.district`
- All location data is saved to the user's document in Firestore

## Code Changes Made

### 1. District Model Updates
- Added `latitude` and `longitude` fields to the `District` class
- Added `hasValidCoordinates` getter to check if coordinates are available
- Added `coordinatesString` getter for debugging

### 2. District Service Updates
- Updated `getDistrictsForCity` to parse lat/lng from Firestore
- Added `findDistrictByName` method to search for districts by name
- Added support for both exact and partial name matching

### 3. Location Provider Updates
- Updated `selectDistrict` to use district coordinates when available
- Added `_matchGpsDistrictWithFirestore` method to automatically match GPS districts
- Enhanced logging to show coordinate updates

## Benefits

1. **Accurate Positioning**: Districts now have precise coordinates instead of relying on GPS accuracy
2. **Consistent Location Data**: All users selecting the same district will have the same coordinates
3. **Backward Compatibility**: Districts without coordinates still work with GPS coordinates
4. **Automatic Matching**: GPS-captured districts are automatically matched with Firestore districts

## Setup Instructions

1. **Update Firestore Document**: Add lat/lng coordinates to your districts in `app_settings/supported_cities`
2. **Test GPS Matching**: Capture location via GPS and verify district matching works
3. **Test Manual Selection**: Manually select districts and verify coordinates are updated
4. **Verify Storage**: Check that user locations are saved with correct coordinates

## Troubleshooting

### District Not Found
- Check that the district name in Firestore matches the GPS-detected name
- The matching is case-insensitive and supports partial matches
- Check debug logs for district matching attempts

### Coordinates Not Updated
- Verify that the district has valid lat/lng fields in Firestore
- Check that coordinates are not 0.0 (which is considered invalid)
- Review debug logs for coordinate update messages

### GPS vs District Coordinates
- GPS coordinates are used when no district match is found
- District coordinates override GPS coordinates when available
- Users can still manually select districts even if GPS matching fails

## Future Enhancements

- Add coordinate validation to ensure they're within expected geographic bounds
- Implement caching for district data to improve performance
- Add support for multiple coordinate systems if needed
- Consider adding district boundaries for more precise location detection
