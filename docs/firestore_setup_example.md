# Firestore Setup Example for OTP Sender

This document provides examples of how to set up the Firestore document for OTP sender configuration.

## Required Firestore Structure

### Collection: `app_settings`
### Document: `other`

## Example Document Data

```json
{
  "otp_sender": "ASHKAL"
}
```

## Alternative Sender Names Examples

```json
{
  "otp_sender": "MyCompany"
}
```

```json
{
  "otp_sender": "SecureAuth"
}
```

```json
{
  "otp_sender": "تواصل"
}
```

## Firebase Console Steps

1. **Open Firebase Console**
   - Go to https://console.firebase.google.com
   - Select your project

2. **Navigate to Firestore**
   - Click on "Firestore Database" in the left sidebar
   - Make sure you're in the correct environment (production/staging)

3. **Create/Edit Collection**
   - If `app_settings` collection doesn't exist, create it
   - Click "Start collection" and enter `app_settings` as collection ID

4. **Create/Edit Document**
   - If `other` document doesn't exist, create it
   - Click "Add document" and enter `other` as document ID
   - If it exists, click on the document to edit

5. **Add/Update Field**
   - Field name: `otp_sender`
   - Field type: `string`
   - Field value: Your desired sender name (e.g., "ASHKAL")

6. **Save Changes**
   - Click "Save" to apply changes
   - Changes are effective immediately

## Testing the Configuration

After setting up the Firestore document:

1. **Open the app**
2. **Navigate to OTP verification screen**
   - Go through the login flow
   - Enter a mobile number
   - Proceed to OTP verification

3. **Verify the sender name**
   - Check that the message shows: "We sent a 4-digit code from [YOUR_SENDER_NAME] to"
   - In Arabic: "تم إرسال رمز مكون من 4 أرقام من [YOUR_SENDER_NAME] إلى"

## Troubleshooting

### Sender name not updating
- Check Firestore rules allow read access
- Verify the document path: `app_settings/other`
- Verify the field name: `otp_sender`
- Check network connectivity

### App shows default "ASHKAL"
- This is expected behavior when:
  - Firestore document doesn't exist
  - Field doesn't exist
  - Network error occurs
  - Field value is empty

### Firestore Rules Example

Make sure your Firestore rules allow reading the configuration:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to app settings
    match /app_settings/{document} {
      allow read: if true;
    }
    
    // Your other rules...
  }
}
```

## Security Considerations

- The `otp_sender` field should be readable by all users
- Consider using Firestore security rules to restrict write access
- Monitor changes to prevent unauthorized modifications

## Multiple Environment Setup

For different environments (dev, staging, production):

### Development
```json
{
  "otp_sender": "ASHKAL-DEV"
}
```

### Staging
```json
{
  "otp_sender": "ASHKAL-STAGING"
}
```

### Production
```json
{
  "otp_sender": "ASHKAL"
}
```

## Monitoring

Consider setting up monitoring for:
- Configuration changes
- Failed reads from Firestore
- Fallback usage statistics

## Best Practices

1. **Keep sender names short** - They appear in SMS messages
2. **Use recognizable names** - Users should identify the sender
3. **Test changes** - Always verify in a test environment first
4. **Document changes** - Keep track of when and why changes were made
5. **Have rollback plan** - Know how to quickly revert if needed
