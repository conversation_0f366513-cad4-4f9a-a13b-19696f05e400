# OTP Sender Configuration

This document explains how the OTP sender name is dynamically configured from Firestore.

## Overview

The OTP sender name (previously hardcoded as "ASH<PERSON>L") is now dynamically retrieved from Firestore, allowing for easy configuration without app updates.

## Firestore Structure

The OTP sender configuration is stored in Firestore at:

```
Collection: app_settings
Document: other
Field: otp_sender
```

### Example Document Structure

```json
{
  "otp_sender": "ASHKAL",
  "other_settings": "..."
}
```

## Implementation Details

### Service Layer

- **OtpSenderService**: Interface for OTP sender configuration operations
- **OtpSenderServiceImpl**: Implementation that fetches from Firestore
- Located in: `lib/shared/services/otp_sender_service.dart`

### Provider Layer

- **otpSenderServiceProvider**: Riverpod provider for the service
- **otpSenderProvider**: Provider that fetches the current sender name
- **OtpSenderNotifier**: Notifier with refresh capabilities
- Located in: `lib/core/providers/otp_sender_provider.dart`

### UI Integration

The OTP verification screen (`mobile_otp_view.dart`) now uses the dynamic sender name:

```dart
// Before (hardcoded)
Text(AppLocalizations.of(context).weSentCodeTo)

// After (dynamic)
Consumer(
  builder: (context, ref, child) {
    final otpSenderAsync = ref.watch(otpSenderProvider);
    return otpSenderAsync.when(
      data: (sender) => Text(
        AppLocalizations.of(context).weSentCodeToWithSender(sender),
      ),
      loading: () => Text(AppLocalizations.of(context).weSentCodeTo),
      error: (error, stack) => Text(AppLocalizations.of(context).weSentCodeTo),
    );
  },
)
```

### Localization

New localization methods added:

- **Arabic**: `weSentCodeToWithSender(String sender)` → `'تم إرسال رمز مكون من 4 أرقام من $sender إلى'`
- **English**: `weSentCodeToWithSender(String sender)` → `'We sent a 4-digit code from $sender to'`

## Configuration Steps

1. **Access Firestore Console**
   - Go to Firebase Console → Firestore Database

2. **Navigate to Collection**
   - Collection: `app_settings`
   - Document: `other`

3. **Update Field**
   - Field: `otp_sender`
   - Value: Your desired sender name (e.g., "ASHKAL", "MyCompany", etc.)

4. **Save Changes**
   - Changes take effect immediately
   - No app restart required

## Fallback Behavior

- **Default Value**: "ASHKAL" (if Firestore is unavailable)
- **Loading State**: Shows hardcoded text while fetching
- **Error State**: Falls back to hardcoded text
- **Empty Value**: Uses default "ASHKAL"

## Testing

To test the configuration:

1. Change the `otp_sender` field in Firestore
2. Open the OTP verification screen
3. Verify the new sender name appears in the message

## Benefits

- **No App Updates**: Change sender name without releasing new app version
- **Real-time Updates**: Changes reflect immediately
- **Fallback Safety**: App continues working even if Firestore is unavailable
- **Localization Support**: Works with both Arabic and English languages
- **Type Safety**: Strongly typed with proper error handling

## Architecture

```
UI Layer (mobile_otp_view.dart)
    ↓
Provider Layer (otp_sender_provider.dart)
    ↓
Service Layer (otp_sender_service.dart)
    ↓
Firebase Service (firebase_service.dart)
    ↓
Firestore Database
```

## Future Enhancements

- Cache sender name locally for offline support
- Add admin panel for easier configuration
- Support for multiple sender names based on region/language
- Analytics tracking for configuration changes
