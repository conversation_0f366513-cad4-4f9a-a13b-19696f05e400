<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>881282683534-pflup69v74rfjj1a2s3f1664s8al0o4v.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.881282683534-pflup69v74rfjj1a2s3f1664s8al0o4v</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>881282683534-ecehq1q47he347s9prtm4osip7okc732.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBlI5cDP5Ng2Zh5IIjC1_OJXIx6HLfQ1Ps</string>
	<key>GCM_SENDER_ID</key>
	<string>881282683534</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.app.towasl</string>
	<key>PROJECT_ID</key>
	<string>towasl</string>
	<key>STORAGE_BUCKET</key>
	<string>towasl.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:881282683534:ios:76d52a83ddb30a86636fdf</string>
</dict>
</plist>