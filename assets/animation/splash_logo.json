{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.4.5", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 50.0000020365418, "w": 1920, "h": 1080, "nm": "تواصل", "ddd": 0, "assets": [{"id": "image_0", "w": 295, "h": 226, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_1", "w": 294, "h": 294, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_2", "w": 179, "h": 253, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_3", "w": 294, "h": 294, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_4", "w": 295, "h": 224, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_5", "w": 294, "h": 294, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_6", "w": 10, "h": 130, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAACCCAYAAAB/0VkIAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAAUklEQVRYhe3KMRGAMBAAwXsmgjKDACymo40UJCCBGhOfgobqFdzWG+9+DpJOJbgbSSc4ypiwleHHaDQajUaj0Wg0Go1Go9FoNBo/DZgZXFWK5Fnb8Av6gtWrwAAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_7", "w": 76, "h": 102, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_8", "w": 79, "h": 100, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_9", "w": 141, "h": 98, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_10", "w": 97, "h": 100, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_11", "w": 104, "h": 133, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGgAAACFCAYAAAC3xr39AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAABwUlEQVR4nO3UsW1UURRF0ffGUwCR6xgJ57gdMndiZ07pBLsBCzpwbBIKgHkUgIQGS19/B2vFNzjSlu58+/j4MNY4DYq+HMcapzHHp72X8Lc1x9Nh7xH8m0BxAsUJFCdQnEBxAsUJFHccV+e7uQ4f9h7y387rtOa8v/D6+5zjbtM9G/j9a7zOvUe814+bx9u1xteLjtd4vn75fLvtom14cXECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1CcQHECxQkUJ1Dcce8B77Xm+ec4H54vOp7j28ZzNvMHgP8gLS3fFb0AAAAASUVORK5CYII=", "e": 1}, {"id": "image_12", "w": 667, "h": 188, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Null 2", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic: Rotation')(1), 200);\n    freq = $bm_div(effect('Elastic: Rotation')(2), 30);\n    decay = $bm_div(effect('Elastic: Rotation')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: Rotation", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "1", "parent": 1, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.992, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "2", "parent": 1, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.992, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 5.00000020365417, "op": 305.000012422905, "st": 5.00000020365417, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "3", "parent": 1, "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.992, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 10.0000004073083, "op": 310.000012626559, "st": 10.0000004073083, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "4", "parent": 1, "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.992, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 15.0000006109625, "op": 315.000012830213, "st": 15.0000006109625, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "5", "parent": 1, "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.992, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 20.0000008146167, "op": 320.000013033867, "st": 20.0000008146167, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "6", "parent": 1, "refId": "comp_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 1.992, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 25.0000010182709, "op": 325.000013237521, "st": 25.0000010182709, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 2", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 2.00000008146167, "op": 302.000012300712, "st": 2.00000008146167, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 2", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.984313726425, 0.854901969433, 0.549019634724, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 1", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [510.25, 181.961, 0], "ix": 2}, "a": {"a": 0, "k": [77.25, -137.039, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [116, -4.5]], "o": [[0, 0], [-91.98, 3.568]], "v": [[158, -96], [-2.5, -173.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0, 0.501960963829, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 35, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.357], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 13.0000005295009, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 10", "tt": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [512.748, 175.248, 0], "ix": 2}, "a": {"a": 0, "k": [147.251, 112.751, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 3", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 3.00000012219251, "op": 303.000012341443, "st": 3.00000012219251, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 3", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.984313726425, 0.854901969433, 0.549019634724, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 4", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [339.992, 175.067, 0], "ix": 2}, "a": {"a": 0, "k": [-93.008, -143.933, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[70.5, 4], [0, 0], [-49.341, -14.68], [0, 0]], "o": [[-65.412, -3.711], [0, 0], [60.5, 18], [0, 0]], "v": [[-90, -208.5], [-157, -143], [-109, -82.5], [-29.5, -126.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.501960754395, 0, 0.501960754395, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 37, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.335], "y": [1]}, "o": {"x": [0.313], "y": [0]}, "t": 0, "s": [0]}, {"t": 14.0000005702317, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 11", "tt": 1, "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [340, 175, 0], "ix": 2}, "a": {"a": 0, "k": [147, 147, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 7", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 3.00000012219251, "op": 303.000012341443, "st": 3.00000012219251, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 7", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.945098042488, 0.470588237047, 0.733333349228, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_6", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 3", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [280.847, 318.5, 0], "ix": 2}, "a": {"a": 0, "k": [-152.153, -0.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-30, -69]], "o": [[0, 0], [30, 69]], "v": [[-145.5, -66.5], [-145, 64]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.501960754395, 0, 0.501960754395, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 35, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.357], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 13.0000005295009, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 8", "tt": 1, "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [273.5, 317.5, 0], "ix": 2}, "a": {"a": 0, "k": [89.5, 126.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_7", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 6", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 3.00000012219251, "op": 303.000012341443, "st": 3.00000012219251, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 6", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.945098042488, 0.470588237047, 0.733333349228, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_8", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [339.992, 455.067, 0], "ix": 2}, "a": {"a": 0, "k": [-93.008, -143.933, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[70.5, 4], [0, 0], [-49.341, -14.68], [0, 0]], "o": [[-65.412, -3.711], [0, 0], [60.5, 18], [0, 0]], "v": [[-90, -208.5], [-157, -143], [-109, -82.5], [-29.5, -126.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.501960754395, 0, 0.501960754395, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 37, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "st", "c": {"a": 0, "k": [0.501960754395, 0, 0.501960754395, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 37, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.335], "y": [1]}, "o": {"x": [0.313], "y": [0]}, "t": 0, "s": [0]}, {"t": 14.0000005702317, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 12", "tt": 1, "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [340, 456, 0], "ix": 2}, "a": {"a": 0, "k": [147, 147, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_9", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 5", "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 3.00000012219251, "op": 303.000012341443, "st": 3.00000012219251, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 5", "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.611764729023, 0.54509806633, 0.929411768913, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_10", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 135, "ix": 10}, "p": {"a": 0, "k": [513.25, 454.961, 0], "ix": 2}, "a": {"a": 0, "k": [77.25, -137.039, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [119.375, -10.589]], "o": [[0, 0], [-91.689, 8.133]], "v": [[159.768, -102.364], [-3.207, -165.722]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0, 0.501960963829, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 35, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.357], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 13.0000005295009, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 9", "tt": 1, "refId": "image_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [512.5, 461, 0], "ix": 2}, "a": {"a": 0, "k": [147.5, 112, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_11", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 4", "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 3.00000012219251, "op": 303.000012341443, "st": 3.00000012219251, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 4", "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [433, 319, 0], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.611764729023, 0.54509806633, 0.929411768913, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "w": 866, "h": 638, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_12", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "S<PERSON>pe Layer 5", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [598.992, 317.067, 0], "ix": 2}, "a": {"a": 0, "k": [-93.008, -143.933, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[70.5, 4], [0, 0], [-49.341, -14.68], [0, 0]], "o": [[-65.412, -3.711], [0, 0], [60.5, 18], [0, 0]], "v": [[-90, -208.5], [-157, -143], [-109, -82.5], [-29.5, -126.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.501960754395, 0, 0.501960754395, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 37, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "st", "c": {"a": 0, "k": [0.501960754395, 0, 0.501960754395, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 37, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.335], "y": [1]}, "o": {"x": [0.313], "y": [0]}, "t": 0, "s": [0]}, {"t": 14.0000005702317, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 13", "tt": 1, "refId": "image_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [599.5, 315.5, 0], "ix": 2}, "a": {"a": 0, "k": [147, 147, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_13", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 1", "refId": "comp_14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [1010, 122.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 10.0000004073083, "s": [414, 122.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [418, 126.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 836, "h": 253, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_14", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "Layer 14", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [828.677, 183.625, 0], "ix": 2}, "a": {"a": 0, "k": [4.888, 64.875, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Layer 15", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [760.602, 202.5, 0], "ix": 2}, "a": {"a": 0, "k": [37.981, 50.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "Layer 16", "refId": "image_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [667.954, 201.499, 0], "ix": 2}, "a": {"a": 0, "k": [39.009, 49.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "Layer 17", "refId": "image_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [548.128, 202.5, 0], "ix": 2}, "a": {"a": 0, "k": [70.122, 48.705, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "Layer 18", "refId": "image_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [413.665, 201.5, 0], "ix": 2}, "a": {"a": 0, "k": [48.2, 49.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "Layer 19", "refId": "image_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [314.742, 183.143, 0], "ix": 2}, "a": {"a": 0, "k": [51.86, 66.393, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}, {"id": "comp_15", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "Layer 15", "refId": "image_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-357.201, 126.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 10.0000004073083, "s": [436.799, 126.8, 0]}], "ix": 2}, "a": {"a": 0, "k": [333.45, 93.914, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "cercel", "refId": "comp_0", "sr": 0.78, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25.74, "s": [923, 533, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 36.6600014931924, "s": [1339, 533, 0]}], "ix": 2}, "a": {"a": 0, "k": [433, 319, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.959, 0.959, 0.333], "y": [0, 0, 0]}, "t": 17.16, "s": [158, 158, 100]}, {"t": 25.7400010484117, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "w": 866, "h": 638, "ip": 0, "op": 234.000009531015, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "tow<PERSON>l", "refId": "comp_13", "sr": 0.78, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [605, 597, 0], "ix": 2}, "a": {"a": 0, "k": [418, 126.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 836, "h": 253, "ip": 26.0000010590017, "op": 260.000010590017, "st": 26.0000010590017, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "تواصل ", "refId": "comp_15", "sr": 0.78, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [708, 466, 0], "ix": 2}, "a": {"a": 0, "k": [448, 148, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 896, "h": 296, "ip": 26.0000010590017, "op": 260.000010590017, "st": 26.0000010590017, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 1, "nm": "Pale Yellow Solid 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "sw": 1920, "sh": 1080, "sc": "#fbfbf9", "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}], "markers": []}