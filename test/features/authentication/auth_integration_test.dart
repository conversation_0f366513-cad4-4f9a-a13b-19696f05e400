/// Authentication Integration Test
///
/// Tests the actual authentication flow to verify mobile number search works correctly
library auth_integration_test;

import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:towasl/features/authentication/data/datasources/auth_remote_datasource.dart';
import 'package:towasl/features/authentication/domain/services/user_id_generator_service.dart';
import 'package:towasl/shared/services/firebase_service.dart';

void main() {
  group('Authentication Integration Tests', () {
    test('should generate correct mobile search formats', () {
      // Create a test instance to access the private method indirectly
      final authDataSource = TestAuthRemoteDataSource();

      // Test case 1: Local format (0564254457)
      final formats1 =
          authDataSource.testGenerateMobileSearchFormats('0564254457');

      expect(formats1, contains('0564254457')); // Original format
      expect(formats1, contains('64254457')); // Core format (without 05)
      expect(formats1, contains('966564254457')); // With country code
      expect(formats1, contains('+966564254457')); // International format

      if (kDebugMode) {
        print('Test 1 - Local format (0564254457):');
        print('Generated formats: $formats1');
      }

      // Test case 2: International format (+9660564254457)
      final formats2 =
          authDataSource.testGenerateMobileSearchFormats('+9660564254457');

      expect(formats2, contains('+9660564254457')); // Original
      expect(formats2, contains('9660564254457')); // Without +
      expect(formats2, contains('0564254457')); // Local format
      expect(formats2, contains('64254457')); // Core format

      if (kDebugMode) {
        print('Test 2 - International format (+9660564254457):');
        print('Generated formats: $formats2');
      }

      // Test case 3: Core format (64254457)
      final formats3 =
          authDataSource.testGenerateMobileSearchFormats('64254457');

      expect(formats3, contains('64254457')); // Original
      expect(formats3, contains('+96664254457')); // International

      if (kDebugMode) {
        print('Test 3 - Core format (64254457):');
        print('Generated formats: $formats3');
      }
    });
  });
}

/// Test class that extends AuthRemoteDataSourceImpl to access private methods
class TestAuthRemoteDataSource extends AuthRemoteDataSourceImpl {
  TestAuthRemoteDataSource()
      : super(
          MockFirebaseService(),
          MockUserIdGeneratorService(),
        );

  /// Public method to test the private _generateMobileSearchFormats method
  List<String> testGenerateMobileSearchFormats(String mobile) {
    final formats = <String>[];

    // Add the original format
    formats.add(mobile);

    // Clean the mobile number (remove non-digits)
    final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanMobile.isNotEmpty) {
      // Add clean format
      formats.add(cleanMobile);

      // Handle Saudi mobile number format (05xxxxxxxx -> xxxxxxxx)
      // This is the key fix: when user enters 05xxxxxxxx, we need to search for xxxxxxxx
      if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
        final coreNumber = cleanMobile.substring(2); // Remove '05' prefix
        formats.add(coreNumber); // Add core number (e.g., "64254457")
      }

      // If it starts with 966 (Saudi country code), also try without it
      if (cleanMobile.startsWith('966') && cleanMobile.length > 3) {
        final withoutCountryCode = cleanMobile.substring(3);
        formats.add(withoutCountryCode);
        formats.add('0$withoutCountryCode'); // Add leading 0

        // For Saudi numbers with country code (96605xxxxxxxx), extract core number
        if (withoutCountryCode.startsWith('05') &&
            withoutCountryCode.length == 10) {
          final coreNumber =
              withoutCountryCode.substring(2); // Remove '05' prefix
          formats.add(coreNumber); // Add core number
        }
      }

      // If it starts with 0, also try with country code and core number
      if (cleanMobile.startsWith('0') && cleanMobile.length > 1) {
        final withoutLeadingZero = cleanMobile.substring(1);
        formats.add(withoutLeadingZero);
        formats.add('966$withoutLeadingZero');
        formats.add('+966$withoutLeadingZero');

        // For Saudi numbers starting with 05, also add the core number
        if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
          final coreNumber = cleanMobile.substring(2); // Remove '05' prefix
          formats.add(coreNumber); // This is the stored format
        }
      }

      // Add international format variations
      if (!cleanMobile.startsWith('+')) {
        if (cleanMobile.startsWith('966')) {
          formats.add('+$cleanMobile');
        } else if (cleanMobile.startsWith('0')) {
          final withoutLeadingZero = cleanMobile.substring(1);
          formats.add('+966$withoutLeadingZero');
        } else {
          formats.add('+966$cleanMobile');
        }
      }
    }

    // Remove duplicates and return
    return formats.toSet().toList();
  }
}

/// Mock Firebase Service for testing
class MockFirebaseService implements FirebaseService {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

/// Mock User ID Generator Service for testing
class MockUserIdGeneratorService implements UserIdGeneratorService {
  @override
  Future<String> generateUniqueUserId(
      Future<bool> Function(String) checkExistence) async {
    return 'test_user_123';
  }
}
