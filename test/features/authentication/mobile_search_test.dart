/// Mobile Number Search Test
///
/// Tests the mobile number search functionality to ensure that
/// different mobile number formats can find existing users correctly
library mobile_search_test;

import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Mobile Number Format Tests', () {
    test('should generate correct search formats for Saudi mobile numbers', () {
      // Test the mobile number format generation logic

      // Test case 1: Local format (**********)
      const input1 = '**********';
      final formats1 = _generateMobileSearchFormats(input1);

      expect(formats1, contains('**********')); // Original format
      expect(formats1, contains('64254457')); // Core format (without 05)
      expect(formats1, contains('966564254457')); // With country code
      expect(formats1, contains('+966564254457')); // International format

      // Test case 2: International format (+966**********)
      const input2 = '+966**********';
      final formats2 = _generateMobileSearchFormats(input2);

      expect(formats2, contains('+966**********')); // Original
      expect(formats2, contains('966**********')); // Without +
      expect(formats2, contains('**********')); // Local format
      expect(formats2, contains('64254457')); // Core format

      // Test case 3: Core format (64254457)
      const input3 = '64254457';
      final formats3 = _generateMobileSearchFormats(input3);

      expect(formats3, contains('64254457')); // Original
      expect(formats3, contains('+96664254457')); // International
    });

    test('should normalize mobile numbers correctly', () {
      // Test the normalization logic from auth flow provider

      // Test case 1: Local format should become core format
      expect(_normalizeMobileNumber('**********'), equals('64254457'));

      // Test case 2: International format should become core format
      expect(_normalizeMobileNumber('+966**********'), equals('64254457'));
      expect(_normalizeMobileNumber('966**********'), equals('64254457'));

      // Test case 3: Core format should remain unchanged
      expect(_normalizeMobileNumber('64254457'), equals('64254457'));

      // Test case 4: Handle edge cases
      expect(_normalizeMobileNumber('05-64-25-44-57'), equals('64254457'));
      expect(_normalizeMobileNumber('+966 05 64 25 44 57'), equals('64254457'));
    });
  });
}

/// Helper function to test mobile search formats generation
/// This replicates the logic from AuthRemoteDataSourceImpl._generateMobileSearchFormats
List<String> _generateMobileSearchFormats(String mobile) {
  final formats = <String>[];

  // Add the original format
  formats.add(mobile);

  // Clean the mobile number (remove non-digits)
  final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

  if (cleanMobile.isNotEmpty) {
    // Add clean format
    formats.add(cleanMobile);

    // Handle Saudi mobile number format (05xxxxxxxx -> 5xxxxxxxx)
    // This is the key fix: when user enters 05xxxxxxxx, we need to search for 5xxxxxxxx
    if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
      final coreNumber = cleanMobile.substring(2); // Remove '05' prefix
      formats.add(coreNumber); // Add core number (e.g., "64254457")
    }

    // If it starts with 966 (Saudi country code), also try without it
    if (cleanMobile.startsWith('966') && cleanMobile.length > 3) {
      final withoutCountryCode = cleanMobile.substring(3);
      formats.add(withoutCountryCode);
      formats.add('0$withoutCountryCode'); // Add leading 0

      // For Saudi numbers with country code (96605xxxxxxxx), extract core number
      if (withoutCountryCode.startsWith('05') &&
          withoutCountryCode.length == 10) {
        final coreNumber =
            withoutCountryCode.substring(2); // Remove '05' prefix
        formats.add(coreNumber); // Add core number
      }
    }

    // If it starts with 0, also try with country code and core number
    if (cleanMobile.startsWith('0') && cleanMobile.length > 1) {
      final withoutLeadingZero = cleanMobile.substring(1);
      formats.add(withoutLeadingZero);
      formats.add('966$withoutLeadingZero');
      formats.add('+966$withoutLeadingZero');

      // For Saudi numbers starting with 05, also add the core number
      if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
        final coreNumber = cleanMobile.substring(2); // Remove '05' prefix
        formats.add(coreNumber); // This is the stored format
      }
    }

    // Add international format variations
    if (!cleanMobile.startsWith('+')) {
      if (cleanMobile.startsWith('966')) {
        formats.add('+$cleanMobile');
      } else if (cleanMobile.startsWith('0')) {
        final withoutLeadingZero = cleanMobile.substring(1);
        formats.add('+966$withoutLeadingZero');
      } else {
        formats.add('+966$cleanMobile');
      }
    }
  }

  // Remove duplicates and return
  return formats.toSet().toList();
}

/// Helper function to test mobile number normalization
/// This replicates the logic from AuthFlowProvider._normalizeMobileNumber
String _normalizeMobileNumber(String mobile) {
  // Remove all non-digit characters
  final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

  // Handle different formats
  if (cleanMobile.startsWith('966') && cleanMobile.length >= 12) {
    // International format: 966********** -> 564254457
    // Remove '966' country code first, then check if it starts with '05'
    final withoutCountryCode = cleanMobile.substring(3); // Remove '966'
    if (withoutCountryCode.startsWith('05') &&
        withoutCountryCode.length == 10) {
      return withoutCountryCode.substring(2); // Remove '05' prefix -> 564254457
    }
    return withoutCountryCode; // Fallback
  } else if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
    // Local format: ********** -> 564254457
    return cleanMobile.substring(2); // Remove '05' prefix
  } else if (cleanMobile.length == 8) {
    // Already in core format: 564254457
    return cleanMobile;
  }

  // If none of the above, return as-is (fallback)
  return cleanMobile;
}
