/// User Scenario Test
///
/// Tests the exact scenario described by the user:
/// - Firestore has: mobile: "64254457", countryCode: "+966"
/// - User enters: "**********"
/// - System should find the existing user
library user_scenario_test;

import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('User Scenario Tests', () {
    test(
        'should find user when stored mobile is 564254457 and user enters **********',
        () {
      // Simulate the exact scenario
      const storedMobile = '564254457'; // What's in Firestore (9 digits)
      const userInput = '**********'; // What user enters
      const storedCountryCode = '+966'; // Country code in Firestore

      // Generate search formats for user input
      final searchFormats = _generateMobileSearchFormats(userInput);

      if (kDebugMode) {
        print('User enters: $userInput');
        print(
            'Stored in Firestore: mobile="$storedMobile", countryCode="$storedCountryCode"');
        print('Generated search formats: $searchFormats');
      }

      // Verify that one of the search formats matches the stored mobile
      expect(searchFormats, contains(storedMobile),
          reason:
              'Search formats should include the stored mobile number format');

      // Also test the normalization for new user creation
      final normalizedMobile = _normalizeMobileNumber(userInput);
      if (kDebugMode) {
        print('Normalized mobile for storage: $normalizedMobile');
      }

      expect(normalizedMobile, equals(storedMobile),
          reason: 'Normalized mobile should match the stored format');
    });

    test('should handle various input formats correctly', () {
      const storedMobile = '564254457';

      final testCases = [
        '**********', // Local format
        '+966**********', // International format
        '966**********', // International without +
        '05-64-25-44-57', // With dashes
        '+966 05 64 25 44 57', // With spaces
      ];

      for (final input in testCases) {
        final searchFormats = _generateMobileSearchFormats(input);
        final normalizedMobile = _normalizeMobileNumber(input);

        if (kDebugMode) {
          print('\nInput: $input');
          print('Search formats: $searchFormats');
          print('Normalized: $normalizedMobile');
        }

        expect(searchFormats, contains(storedMobile),
            reason:
                'Input "$input" should generate search format that matches stored mobile');
        expect(normalizedMobile, equals(storedMobile),
            reason: 'Input "$input" should normalize to stored mobile format');
      }
    });
  });
}

/// Generate mobile search formats (same logic as in AuthRemoteDataSource)
List<String> _generateMobileSearchFormats(String mobile) {
  final formats = <String>[];

  // Add the original format
  formats.add(mobile);

  // Clean the mobile number (remove non-digits)
  final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

  if (cleanMobile.isNotEmpty) {
    // Add clean format
    formats.add(cleanMobile);

    // Handle Saudi mobile number format (05xxxxxxxx -> xxxxxxxx)
    if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
      final coreNumber = cleanMobile.substring(1); // Remove '0' prefix
      formats.add(coreNumber); // Add core number (e.g., "564254457")
    }

    // If it starts with 966 (Saudi country code), also try without it
    if (cleanMobile.startsWith('966') && cleanMobile.length > 3) {
      final withoutCountryCode = cleanMobile.substring(3);
      formats.add(withoutCountryCode);
      formats.add('0$withoutCountryCode'); // Add leading 0

      // For Saudi numbers with country code (96605xxxxxxxx), extract core number
      if (withoutCountryCode.startsWith('05') &&
          withoutCountryCode.length == 10) {
        final coreNumber = withoutCountryCode.substring(1); // Remove '0' prefix
        formats.add(coreNumber); // Add core number
      }
    }

    // If it starts with 0, also try with country code and core number
    if (cleanMobile.startsWith('0') && cleanMobile.length > 1) {
      final withoutLeadingZero = cleanMobile.substring(1);
      formats.add(withoutLeadingZero);
      formats.add('966$withoutLeadingZero');
      formats.add('+966$withoutLeadingZero');

      // For Saudi numbers starting with 05, also add the core number
      if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
        final coreNumber = cleanMobile.substring(1); // Remove '0' prefix
        formats.add(coreNumber); // This is the stored format
      }
    }

    // Add international format variations
    if (!cleanMobile.startsWith('+')) {
      if (cleanMobile.startsWith('966')) {
        formats.add('+$cleanMobile');
      } else if (cleanMobile.startsWith('0')) {
        final withoutLeadingZero = cleanMobile.substring(1);
        formats.add('+966$withoutLeadingZero');
      } else {
        formats.add('+966$cleanMobile');
      }
    }
  }

  // Remove duplicates and return
  return formats.toSet().toList();
}

/// Normalize mobile number (same logic as in AuthFlowProvider)
String _normalizeMobileNumber(String mobile) {
  // Remove all non-digit characters
  final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

  // Handle different formats
  if (cleanMobile.startsWith('966') && cleanMobile.length >= 12) {
    // International format: 966********** -> 564254457
    // Remove '966' country code first, then check if it starts with '05'
    final withoutCountryCode = cleanMobile.substring(3); // Remove '966'
    if (withoutCountryCode.startsWith('05') &&
        withoutCountryCode.length == 10) {
      return withoutCountryCode.substring(1); // Remove '0' prefix -> 564254457
    }
    return withoutCountryCode; // Fallback
  } else if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
    // Local format: ********** -> 564254457
    return cleanMobile.substring(1); // Remove '0' prefix only
  } else if (cleanMobile.length == 9 && cleanMobile.startsWith('5')) {
    // Already in core format: 564254457
    return cleanMobile;
  }

  // If none of the above, return as-is (fallback)
  return cleanMobile;
}
