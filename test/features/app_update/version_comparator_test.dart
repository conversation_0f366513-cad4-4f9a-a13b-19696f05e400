/// Version Comparator Tests
///
/// Unit tests for the version comparison utility
library version_comparator_test;

import 'package:flutter_test/flutter_test.dart';
import 'package:towasl/shared/utils/version_comparator.dart';

void main() {
  group('VersionComparator', () {
    group('compareVersions', () {
      test('should return 0 for equal versions', () {
        expect(VersionComparator.compareVersions('1.0.0', '1.0.0'), equals(0));
        expect(VersionComparator.compareVersions('2.1.3', '2.1.3'), equals(0));
        expect(VersionComparator.compareVersions('10.0.0', '10.0.0'), equals(0));
      });

      test('should return 1 when first version is greater', () {
        expect(VersionComparator.compareVersions('1.1.0', '1.0.0'), equals(1));
        expect(VersionComparator.compareVersions('2.0.0', '1.9.9'), equals(1));
        expect(VersionComparator.compareVersions('1.0.1', '1.0.0'), equals(1));
      });

      test('should return -1 when first version is less', () {
        expect(VersionComparator.compareVersions('1.0.0', '1.1.0'), equals(-1));
        expect(VersionComparator.compareVersions('1.9.9', '2.0.0'), equals(-1));
        expect(VersionComparator.compareVersions('1.0.0', '1.0.1'), equals(-1));
      });

      test('should handle different version lengths', () {
        expect(VersionComparator.compareVersions('1.0', '1.0.0'), equals(0));
        expect(VersionComparator.compareVersions('1.1', '1.0.0'), equals(1));
        expect(VersionComparator.compareVersions('1.0.0', '1.1'), equals(-1));
      });

      test('should handle empty versions', () {
        expect(VersionComparator.compareVersions('', ''), equals(0));
        expect(VersionComparator.compareVersions('1.0.0', ''), equals(1));
        expect(VersionComparator.compareVersions('', '1.0.0'), equals(-1));
      });
    });

    group('helper methods', () {
      test('isGreaterThan should work correctly', () {
        expect(VersionComparator.isGreaterThan('1.1.0', '1.0.0'), isTrue);
        expect(VersionComparator.isGreaterThan('1.0.0', '1.1.0'), isFalse);
        expect(VersionComparator.isGreaterThan('1.0.0', '1.0.0'), isFalse);
      });

      test('isLessThan should work correctly', () {
        expect(VersionComparator.isLessThan('1.0.0', '1.1.0'), isTrue);
        expect(VersionComparator.isLessThan('1.1.0', '1.0.0'), isFalse);
        expect(VersionComparator.isLessThan('1.0.0', '1.0.0'), isFalse);
      });

      test('isEqual should work correctly', () {
        expect(VersionComparator.isEqual('1.0.0', '1.0.0'), isTrue);
        expect(VersionComparator.isEqual('1.0.0', '1.1.0'), isFalse);
      });

      test('isGreaterThanOrEqual should work correctly', () {
        expect(VersionComparator.isGreaterThanOrEqual('1.1.0', '1.0.0'), isTrue);
        expect(VersionComparator.isGreaterThanOrEqual('1.0.0', '1.0.0'), isTrue);
        expect(VersionComparator.isGreaterThanOrEqual('1.0.0', '1.1.0'), isFalse);
      });

      test('isLessThanOrEqual should work correctly', () {
        expect(VersionComparator.isLessThanOrEqual('1.0.0', '1.1.0'), isTrue);
        expect(VersionComparator.isLessThanOrEqual('1.0.0', '1.0.0'), isTrue);
        expect(VersionComparator.isLessThanOrEqual('1.1.0', '1.0.0'), isFalse);
      });
    });

    group('isValidVersion', () {
      test('should validate correct version formats', () {
        expect(VersionComparator.isValidVersion('1.0.0'), isTrue);
        expect(VersionComparator.isValidVersion('10.20.30'), isTrue);
        expect(VersionComparator.isValidVersion('1.0'), isTrue);
        expect(VersionComparator.isValidVersion('1'), isTrue);
      });

      test('should reject invalid version formats', () {
        expect(VersionComparator.isValidVersion(''), isFalse);
        expect(VersionComparator.isValidVersion('1.0.0-beta'), isFalse);
        expect(VersionComparator.isValidVersion('v1.0.0'), isFalse);
        expect(VersionComparator.isValidVersion('1.0.0.0'), isTrue); // This should be valid
        expect(VersionComparator.isValidVersion('1.a.0'), isFalse);
      });
    });
  });
}
