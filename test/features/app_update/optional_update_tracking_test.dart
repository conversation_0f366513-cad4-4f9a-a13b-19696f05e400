/// Optional Update Tracking Tests
///
/// Unit tests for the optional update tracking functionality
library optional_update_tracking_test;

import 'package:flutter_test/flutter_test.dart';
import 'package:get_storage/get_storage.dart';
import 'package:towasl/shared/services/storage_service.dart';

void main() {
  group('Optional Update Tracking', () {
    late StorageService storageService;
    late GetStorage storage;

    setUp(() async {
      // Initialize GetStorage for testing
      GetStorage.init();
      storage = GetStorage();
      storageService = StorageServiceImpl(storage);
      
      // Clear any existing data
      await storage.erase();
    });

    tearDown(() async {
      // Clean up after each test
      await storage.erase();
    });

    group('setOptionalUpdateShown', () {
      test('should mark version as shown', () async {
        const version = '1.1.0';
        
        // Initially should not be shown
        expect(storageService.hasOptionalUpdateBeenShown(version), isFalse);
        
        // Mark as shown
        await storageService.setOptionalUpdateShown(version);
        
        // Should now be marked as shown
        expect(storageService.hasOptionalUpdateBeenShown(version), isTrue);
      });

      test('should handle multiple versions independently', () async {
        const version1 = '1.1.0';
        const version2 = '1.2.0';
        
        // Mark only version1 as shown
        await storageService.setOptionalUpdateShown(version1);
        
        // Check status
        expect(storageService.hasOptionalUpdateBeenShown(version1), isTrue);
        expect(storageService.hasOptionalUpdateBeenShown(version2), isFalse);
        
        // Mark version2 as shown
        await storageService.setOptionalUpdateShown(version2);
        
        // Both should now be shown
        expect(storageService.hasOptionalUpdateBeenShown(version1), isTrue);
        expect(storageService.hasOptionalUpdateBeenShown(version2), isTrue);
      });
    });

    group('hasOptionalUpdateBeenShown', () {
      test('should return false for untracked versions', () {
        const version = '1.0.0';
        expect(storageService.hasOptionalUpdateBeenShown(version), isFalse);
      });

      test('should return true for tracked versions', () async {
        const version = '1.1.0';
        
        await storageService.setOptionalUpdateShown(version);
        expect(storageService.hasOptionalUpdateBeenShown(version), isTrue);
      });

      test('should handle empty version string', () {
        expect(storageService.hasOptionalUpdateBeenShown(''), isFalse);
      });
    });

    group('clearOptionalUpdateShown', () {
      test('should clear all tracked versions', () async {
        const version1 = '1.1.0';
        const version2 = '1.2.0';
        const version3 = '2.0.0';
        
        // Mark multiple versions as shown
        await storageService.setOptionalUpdateShown(version1);
        await storageService.setOptionalUpdateShown(version2);
        await storageService.setOptionalUpdateShown(version3);
        
        // Verify they are marked
        expect(storageService.hasOptionalUpdateBeenShown(version1), isTrue);
        expect(storageService.hasOptionalUpdateBeenShown(version2), isTrue);
        expect(storageService.hasOptionalUpdateBeenShown(version3), isTrue);
        
        // Clear all
        await storageService.clearOptionalUpdateShown();
        
        // All should now be false
        expect(storageService.hasOptionalUpdateBeenShown(version1), isFalse);
        expect(storageService.hasOptionalUpdateBeenShown(version2), isFalse);
        expect(storageService.hasOptionalUpdateBeenShown(version3), isFalse);
      });

      test('should not affect other storage keys', () async {
        const version = '1.1.0';
        const testKey = 'test_key';
        const testValue = 'test_value';
        
        // Set some test data
        await storageService.write(testKey, testValue);
        await storageService.setOptionalUpdateShown(version);
        
        // Verify both are set
        expect(storageService.read<String>(testKey), equals(testValue));
        expect(storageService.hasOptionalUpdateBeenShown(version), isTrue);
        
        // Clear optional update flags
        await storageService.clearOptionalUpdateShown();
        
        // Optional update should be cleared, but other data should remain
        expect(storageService.hasOptionalUpdateBeenShown(version), isFalse);
        expect(storageService.read<String>(testKey), equals(testValue));
      });

      test('should handle empty storage gracefully', () async {
        // Should not throw when clearing empty storage
        expect(() => storageService.clearOptionalUpdateShown(), returnsNormally);
        await storageService.clearOptionalUpdateShown();
      });
    });

    group('session behavior simulation', () {
      test('should simulate app session behavior', () async {
        const version = '1.1.0';
        
        // Simulate first app launch - no update shown
        expect(storageService.hasOptionalUpdateBeenShown(version), isFalse);
        
        // User sees optional update dialog
        await storageService.setOptionalUpdateShown(version);
        
        // Later in same session - should not show again
        expect(storageService.hasOptionalUpdateBeenShown(version), isTrue);
        
        // Simulate app restart - clear flags
        await storageService.clearOptionalUpdateShown();
        
        // New session - can show again
        expect(storageService.hasOptionalUpdateBeenShown(version), isFalse);
      });
    });
  });
}
