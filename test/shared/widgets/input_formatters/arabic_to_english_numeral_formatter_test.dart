import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:towasl/shared/widgets/input_formatters/arabic_to_english_numeral_formatter.dart';

void main() {
  group('ArabicToEnglishNumeralFormatter', () {
    late ArabicToEnglishNumeralFormatter formatter;

    setUp(() {
      formatter = const ArabicToEnglishNumeralFormatter();
    });

    test('should convert Arabic numerals to English numerals', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '٠٥٦٤٢٥٤٤٥٧');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('0564254457'));
      expect(
          result.selection.baseOffset, equals(newValue.selection.baseOffset));
      expect(result.selection.extentOffset,
          equals(newValue.selection.extentOffset));
    });

    test('should handle single Arabic digit input', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '٥');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('5'));
    });

    test('should handle OTP input with Arabic numerals', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '١٢٣٤');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('1234'));
    });

    test('should handle birth year input with Arabic numerals', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '١٩٩٠');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('1990'));
    });

    test('should handle mixed Arabic and English numerals', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '١2٣4');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('1234'));
    });

    test('should not modify text with only English numerals', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '1234567890');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result, equals(newValue));
    });

    test('should handle empty input', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '');

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result, equals(newValue));
    });

    test('should preserve cursor position when converting', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(
        text: '٠٥٦٤',
        selection: TextSelection.collapsed(offset: 2),
      );

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('0564'));
      expect(result.selection.baseOffset, equals(2));
      expect(result.selection.extentOffset, equals(2));
    });

    test('should handle text selection when converting', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(
        text: '٠٥٦٤',
        selection: TextSelection(baseOffset: 1, extentOffset: 3),
      );

      final result = formatter.formatEditUpdate(oldValue, newValue);

      expect(result.text, equals('0564'));
      expect(result.selection.baseOffset, equals(1));
      expect(result.selection.extentOffset, equals(3));
    });
  });
}
