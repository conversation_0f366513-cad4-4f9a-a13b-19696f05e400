import 'package:flutter_test/flutter_test.dart';
import 'package:towasl/shared/services/version_service.dart';

void main() {
  group('VersionService', () {
    late VersionService versionService;

    setUp(() {
      versionService = VersionService();
    });

    test('should return empty strings when not initialized', () {
      expect(versionService.appVersion, isEmpty);
      expect(versionService.fullVersion, isEmpty);
      expect(versionService.appName, isEmpty);
      expect(versionService.packageName, isEmpty);
      expect(versionService.isInitialized, isFalse);
    });

    test('should indicate not initialized state correctly', () {
      expect(versionService.isInitialized, isFalse);
    });

    // Note: Testing actual initialization requires platform-specific setup
    // which is complex in unit tests. Integration tests would be better
    // for testing the actual package_info_plus functionality.
  });
}
