import 'package:flutter_test/flutter_test.dart';
import 'package:towasl/shared/helpers/text_helpers.dart';

void main() {
  group('TextHelpers', () {
    group('convertArabicNumeralsToEnglish', () {
      test('should convert Arabic numerals to English numerals', () {
        // Test individual digits
        expect(TextHelpers.convertArabicNumeralsToEnglish('٠'), equals('0'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('١'), equals('1'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٢'), equals('2'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٣'), equals('3'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٤'), equals('4'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٥'), equals('5'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٦'), equals('6'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٧'), equals('7'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٨'), equals('8'));
        expect(TextHelpers.convertArabicNumeralsToEnglish('٩'), equals('9'));
      });

      test('should convert Arabic phone numbers to English', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish('٠٥٦٤٢٥٤٤٥٧'),
          equals('0564254457'),
        );
      });

      test('should convert Arabic OTP codes to English', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish('١٢٣٤'),
          equals('1234'),
        );
      });

      test('should convert Arabic birth years to English', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish('١٩٩٠'),
          equals('1990'),
        );
      });

      test('should handle mixed Arabic and English numerals', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish('١2٣4'),
          equals('1234'),
        );
      });

      test('should handle text with no Arabic numerals', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish('1234567890'),
          equals('1234567890'),
        );
      });

      test('should handle empty string', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish(''),
          equals(''),
        );
      });

      test('should handle text with Arabic numerals and other characters', () {
        expect(
          TextHelpers.convertArabicNumeralsToEnglish('رقم الهاتف: ٠٥٦٤٢٥٤٤٥٧'),
          equals('رقم الهاتف: 0564254457'),
        );
      });
    });
  });
}
