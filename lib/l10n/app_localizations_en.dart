/// English Localizations
///
/// Provides English translations for the Towasl application
/// Replaces GetX English translations with Flutter's standard localization
library app_localizations_en;

import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([super.locale = 'en']);

  // Common strings
  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get next => 'Next';

  @override
  String get go => 'Verify';

  @override
  String get sec => 'seconds';

  // Authentication
  @override
  String get enterMobileNumber => 'Enter Mobile Number';

  @override
  String get mobileNumber => 'Mobile Number';

  @override
  String get toReSendOtpCode => 'To resend verification code';

  @override
  String get incorrectOtpError => 'Incorrect verification code';

  @override
  String get avoidBlockage => 'Please try again after the timer ends';

  @override
  String get otpResentSuccessfully => 'OTP resent successfully';

  @override
  String get verificationCode => 'Verification Code';

  @override
  String get pleaseEnterCompleteOtp =>
      'Please enter the complete verification code';

  @override
  String get pleaseEnterMobileNumber => 'Please enter a mobile number';

  @override
  String get pleaseEnterValidMobileNumber =>
      'Please enter a valid mobile number';

  @override
  String get loginFirst => 'Please login first to continue';

  // Interests
  @override
  String get selectYourInterests => 'Select Your Interests';

  // Location and Privacy
  @override
  String get fetchingLocation => 'Fetching and saving location...';

  @override
  String get privacyFirstDesc =>
      'We do not share your home location with anyone. The purpose is to help show the nearest activities to you.';

  @override
  String get needAllowLocation =>
      'You need to allow location access to use the app';

  @override
  String get allowLocation => 'Please allow location access first';

  // Home
  @override
  String get noRecordFound => 'No records found';

  // Validation
  @override
  String get required => 'Required';

  @override
  String get invalidYear => 'Invalid year';

  @override
  String get incorrect => 'Incorrect';

  @override
  String get mobileFormat => 'Please enter a valid Saudi mobile number';

  @override
  String get invalidEmailFormat => 'Invalid email format';

  @override
  String get nameTooShort => 'Name too short';

  @override
  String get passwordTooShort => 'Password too short';

  // Welcome
  @override
  String get welcomeToTowasl => 'Welcome to Towasl!';

  @override
  String get welcomeDescription =>
      'Connect with like-minded people in your area and discover meaningful relationships.';

  @override
  String get getStarted => 'Get Started';

  // Location
  @override
  String get homeDistrict => 'Home District';

  @override
  String get selectDistrict => 'Select District';

  @override
  String get districtSelection => 'District Selection';

  @override
  String get pleaseSelectDistrict => 'Please select district';

  @override
  String get noDistrictsAvailable => 'No districts available';

  @override
  String get searchDistricts => 'Search districts';

  @override
  String get noDistrictsFound => 'No districts found';

  @override
  String get pleaseEnableGpsAndAllowLocation =>
      'Please enable GPS and allow location access. This helps us provide better service.';

  @override
  String get unknownCity => 'Unknown City';

  @override
  String get unknownCountry => 'Unknown Country';

  @override
  String get gpsLocationRequired => 'GPS Location Required';

  @override
  String get gettingLocation => 'Getting Location...';

  @override
  String get getLocation => 'Get Location';

  @override
  String get locationPermissionRequired => 'Location permission is required.';

  @override
  String get continueText => 'Continue';

  // Authentication
  @override
  String get welcomeToTowasI => 'Welcome to Towasl';

  @override
  String get enterMobileNumberToGetStarted =>
      'Enter your mobile number to get started';

  @override
  String get iAcceptThe => 'I accept the ';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get and => ' and ';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get couldNotLaunch => 'Could not launch';

  // Interests
  @override
  String get yourInterests => 'Your Interests';

  @override
  String get selectAtLeastThreeInterests => 'Select at least 3 interests.';

  @override
  String get loadingInterests => 'Loading interests...';

  @override
  String get noInterestsAvailable => 'No interests available';

  // Personal Info
  @override
  String get personalInformation => 'Personal Information';

  @override
  String get tellUsAboutYourself => 'Tell us about yourself';

  @override
  String get personalInfoDescription =>
      'This information helps us create a better profile for you and find more compatible matches.';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get enterYourBirthYear => 'Enter your birth year (e.g., 1995)';

  @override
  String get pleaseEnterYourBirthYear => 'Please enter your birth year';

  @override
  String get pleaseEnterValidBirthYear => 'Please enter a valid birth year';

  @override
  String get gender => 'Gender';

  @override
  String get selectYourGender => 'Select your gender';

  @override
  String get nationality => 'Nationality';

  @override
  String get selectYourNationality => 'Select your nationality';

  // Home
  @override
  String get towasl => 'Towasl';

  @override
  String get loading => 'Loading...';

  @override
  String get regionNotSupported => 'Region Not Supported';

  @override
  String get regionNotSupportedDescription =>
      'The region is not supported now, we will let you know once it is supported.';

  // Profile
  @override
  String get profile => 'Profile';

  @override
  String get myProfile => 'My Profile';

  @override
  String get interests => 'Interests';

  @override
  String get personalInfo => 'Personal Info';

  @override
  String get location => 'Location';

  @override
  String get signOut => 'Sign Out';

  @override
  String get signOutConfirmation => 'Are you sure you want to sign out?';

  @override
  String get noInterestsSelected => 'No interests selected';

  @override
  String interestsCount(int count) => '$count interests selected';

  @override
  String get notSet => 'Not set';

  @override
  String get yearsOld => 'years old';

  @override
  String get loggingOut => 'Logging out...';

  // App Update
  @override
  String get updateRequired => 'Update Required';

  @override
  String get updateAvailable => 'Update Available';

  @override
  String get mandatoryUpdateDescription =>
      'App update is required to continue. Current version is no longer supported.';

  @override
  String get optionalUpdateDescription =>
      'A new version of the app is available with improvements and new features.';

  @override
  String get currentVersion => 'Current Version:';

  @override
  String get newVersion => 'New Version:';

  @override
  String get updateApp => 'Update App';

  @override
  String get skipNow => 'Skip Now';

  @override
  String get cannotOpenAppStore => 'Cannot open app store';

  @override
  String get updateLinkNotAvailable => 'Update link not available';

  @override
  String get errorOpeningAppStore => 'Error occurred while opening app store';

  // OTP Verification
  @override
  String get verifyMobileNumber => 'Verify Mobile Number';

  @override
  String get enterVerificationCode => 'Enter Verification Code';

  @override
  String get weSentCodeTo => 'We sent a 4-digit code from ASHKAL to';

  @override
  String weSentCodeToWithSender(String sender) =>
      'We sent a 4-digit code from $sender to';

  @override
  String get resendCodeIn => 'Resend code in';

  @override
  String get didntReceiveCode => "Didn't receive the code? ";

  @override
  String get resend => 'Resend';

  @override
  String get verify => 'Verify';

  // Toast Messages
  @override
  String get otpSentSuccessfully => 'OTP sent successfully!';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get anErrorOccurred => 'An error occurred. Please try again.';

  @override
  String get otpVerifiedSuccessfully => 'OTP verified successfully!';

  @override
  String get otpVerificationFailed => 'OTP verification failed';

  @override
  String get pleaseWaitBeforeRequestingOtp =>
      'Please wait before requesting a new OTP';

  @override
  String get failedToResendOtp => 'Failed to resend OTP';

  @override
  String get numberBlockedCantLogin => "Number blocked, Can't login";

  @override
  String get profileSavedSuccessfully => 'Profile saved successfully!';

  @override
  String get failedToSaveProfile => 'Failed to save profile';

  @override
  String get anErrorOccurredWhileSavingPersonalInfo =>
      'An error occurred while saving personal info';

  @override
  String get accountDeletedSuccessfully => 'Account deleted successfully';

  @override
  String get failedToDeleteAccount => 'Failed to delete account';

  @override
  String get anErrorOccurredWhileSavingLocation =>
      'An error occurred while saving location';

  @override
  String get openingAppSettings => 'Opening app settings...';

  @override
  String get interestsUpdatedSuccessfully => 'Interests updated successfully';

  // Gender Options (for display)
  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  // Nationality Options (for display - common ones)
  @override
  String get saudi => '🇸🇦 Saudi';

  @override
  String get egyptian => '🇪🇬 Egyptian';

  @override
  String get jordanian => '🇯🇴 Jordanian';

  @override
  String get lebanese => '🇱🇧 Lebanese';

  @override
  String get syrian => '🇸🇾 Syrian';

  @override
  String get iraqi => '🇮🇶 Iraqi';

  @override
  String get kuwaiti => '🇰🇼 Kuwaiti';

  @override
  String get emirati => '🇦🇪 Emirati';

  @override
  String get qatari => '🇶🇦 Qatari';

  @override
  String get bahraini => '🇧🇭 Bahraini';

  @override
  String get omani => '🇴🇲 Omani';

  @override
  String get yemeni => '🇾🇪 Yemeni';

  @override
  String get palestinian => '🇵🇸 Palestinian';

  @override
  String get moroccan => '🇲🇦 Moroccan';

  @override
  String get tunisian => '🇹🇳 Tunisian';

  @override
  String get algerian => '🇩🇿 Algerian';

  @override
  String get libyan => '🇱🇾 Libyan';

  @override
  String get sudanese => '🇸🇩 Sudanese';

  @override
  String get somali => '🇸🇴 Somali';

  @override
  String get mauritanian => '🇲🇷 Mauritanian';

  @override
  String get djiboutian => '🇩🇯 Djiboutian';

  @override
  String get other => '🌍 Other Nationality';
}
