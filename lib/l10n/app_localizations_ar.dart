/// Arabic Localizations
///
/// Provides Arabic translations for the Towasl application
/// Replaces GetX Arabic translations with Flutter's standard localization
library app_localizations_ar;

import 'app_localizations.dart';

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([super.locale = 'ar']);

  // Common strings
  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get next => 'التالي';

  @override
  String get go => 'تحقق';

  @override
  String get sec => 'ثانية';

  // Authentication
  @override
  String get enterMobileNumber => 'ادخل رقم الجوال';

  @override
  String get mobileNumber => 'رقم الجوال';

  @override
  String get toReSendOtpCode => 'لإعادة ارسال رمز التحقق';

  @override
  String get incorrectOtpError => 'رمز التحقق غير صحيح';

  @override
  String get avoidBlockage => 'الرجاء المحاولة بعد انتهاء المؤقت';

  @override
  String get otpResentSuccessfully => 'تم إعادة إرسال رمز التحقق بنجاح';

  @override
  String get verificationCode => 'رمز التحقق';

  @override
  String get pleaseEnterCompleteOtp => 'الرجاء إدخال رمز التحقق كاملاً';

  @override
  String get pleaseEnterMobileNumber => 'الرجاء ادخال رقم الجوال';

  @override
  String get pleaseEnterValidMobileNumber => 'الرجاء ادخال رقم جوال صحيح';

  @override
  String get loginFirst => 'الرجاء تسجيل الدخول أولاً للمتابعة';

  // Interests
  @override
  String get selectYourInterests => 'اختر اهتماماتك';

  // Location and Privacy
  @override
  String get fetchingLocation => 'جاري جلب وحفظ الموقع ...';

  @override
  String get privacyFirstDesc =>
      'لا نقوم بمشاركة موقع منزلك إلى أي شخص، والهدف هو المساعدة في عرض اقرب الأنشطة لديك';

  @override
  String get needAllowLocation =>
      'تحتاج إلى السماح بالوصول للموقع لاستخدام التطبيق';

  @override
  String get allowLocation => 'الرجاء السماح بالوصول للموقع أولاً';

  // Home
  @override
  String get noRecordFound => 'لا يوجد بيانات حالياً';

  // Validation
  @override
  String get required => 'مطلوب';

  @override
  String get invalidYear => 'سنة غير صحيحة';

  @override
  String get incorrect => 'غير صحيح';

  @override
  String get mobileFormat => 'اكتب رقم جوال سعودي صحيح';

  @override
  String get invalidEmailFormat => 'تنسيق البريد الإلكتروني غير صحيح';

  @override
  String get nameTooShort => 'الاسم قصير جداً';

  @override
  String get passwordTooShort => 'كلمة المرور قصيرة جداً';

  // Welcome
  @override
  String get welcomeToTowasl => 'مرحباً بك في توصل!';

  @override
  String get welcomeDescription =>
      'تواصل مع أشخاص يشاركونك نفس الاهتمامات في منطقتك واكتشف علاقات ذات معنى.';

  @override
  String get getStarted => 'ابدأ الآن';

  // Location
  @override
  String get homeDistrict => 'الحي السكني';

  @override
  String get selectDistrict => 'اختر الحي';

  @override
  String get districtSelection => 'تسكن في حي آخر؟';

  @override
  String get pleaseSelectDistrict => 'يمكنك تغيير الحي';

  @override
  String get noDistrictsAvailable => 'لا توجد أحياء متاحة';

  @override
  String get searchDistricts => 'ابحث عن الأحياء';

  @override
  String get noDistrictsFound => 'لم يتم العثور على أحياء';

  @override
  String get pleaseEnableGpsAndAllowLocation =>
      'يرجى تفعيل نظام تحديد المواقع والسماح بالوصول للموقع. هذا يساعدنا في تقديم خدمة أفضل.';

  @override
  String get unknownCity => 'مدينة غير معروفة';

  @override
  String get unknownCountry => 'دولة غير معروفة';

  @override
  String get gpsLocationRequired => 'موقع GPS مطلوب';

  @override
  String get gettingLocation => 'جاري الحصول على الموقع...';

  @override
  String get getLocation => 'الحصول على الموقع';

  @override
  String get locationPermissionRequired => 'إذن الموقع مطلوب.';

  @override
  String get continueText => 'متابعة';

  // Authentication
  @override
  String get welcomeToTowasI => 'مرحباً بك في توصل';

  @override
  String get enterMobileNumberToGetStarted => 'أدخل رقم جوالك للبدء';

  @override
  String get iAcceptThe => 'أوافق على ';

  @override
  String get termsAndConditions => 'الشروط والأحكام';

  @override
  String get and => ' و ';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get couldNotLaunch => 'لا يمكن فتح';

  // Interests
  @override
  String get yourInterests => 'اهتماماتك';

  @override
  String get selectAtLeastThreeInterests => 'اختر على الأقل 3 اهتمامات.';

  @override
  String get loadingInterests => 'جاري تحميل الاهتمامات...';

  @override
  String get noInterestsAvailable => 'لا توجد اهتمامات متاحة';

  // Personal Info
  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get tellUsAboutYourself => 'أخبرنا عن نفسك';

  @override
  String get personalInfoDescription =>
      'هذه المعلومات تساعدنا في إنشاء ملف شخصي أفضل لك وإيجاد أشخاص أكثر توافقاً.';

  @override
  String get birthYear => 'سنة الميلاد (بالميلادي)';

  @override
  String get enterYourBirthYear => 'مثال: 1995';

  @override
  String get pleaseEnterYourBirthYear => 'يرجى إدخال سنة ميلادك';

  @override
  String get pleaseEnterValidBirthYear => 'يرجى إدخال سنة ميلاد صحيحة';

  @override
  String get gender => 'الجنس';

  @override
  String get selectYourGender => 'اختر جنسك';

  @override
  String get nationality => 'الجنسية';

  @override
  String get selectYourNationality => 'اختر جنسيتك';

  // Home
  @override
  String get towasl => 'توصل';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get regionNotSupported => 'المنطقة غير مدعومة';

  @override
  String get regionNotSupportedDescription =>
      'المنطقة غير مدعومة حالياً، سنخبرك عندما تصبح مدعومة.';

  // Profile
  @override
  String get profile => 'الملف الشخصي';

  @override
  String get myProfile => 'ملفي الشخصي';

  @override
  String get interests => 'الاهتمامات';

  @override
  String get personalInfo => 'المعلومات الشخصية';

  @override
  String get location => 'الموقع';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get signOutConfirmation => 'هل أنت متأكد من تسجيل الخروج؟';

  @override
  String get noInterestsSelected => 'لم يتم اختيار اهتمامات';

  @override
  String interestsCount(int count) => '$count اهتمامات محددة';

  @override
  String get notSet => 'غير محدد';

  @override
  String get yearsOld => 'سنة';

  @override
  String get loggingOut => 'جاري تسجيل الخروج...';

  // App Update
  @override
  String get updateRequired => 'تحديث مطلوب';

  @override
  String get updateAvailable => 'تحديث متاح';

  @override
  String get mandatoryUpdateDescription =>
      'يجب تحديث التطبيق للمتابعة. الإصدار الحالي لم يعد مدعوماً.';

  @override
  String get optionalUpdateDescription =>
      'إصدار جديد متاح من التطبيق مع تحسينات وميزات جديدة.';

  @override
  String get currentVersion => 'الإصدار الحالي:';

  @override
  String get newVersion => 'الإصدار الجديد:';

  @override
  String get updateApp => 'تحديث التطبيق';

  @override
  String get skipNow => 'تخطي الآن';

  @override
  String get cannotOpenAppStore => 'لا يمكن فتح متجر التطبيقات';

  @override
  String get updateLinkNotAvailable => 'رابط التحديث غير متاح';

  @override
  String get errorOpeningAppStore => 'حدث خطأ أثناء فتح متجر التطبيقات';

  // OTP Verification
  @override
  String get verifyMobileNumber => 'تحقق من رقم الجوال';

  @override
  String get enterVerificationCode => 'أدخل رمز التحقق';

  @override
  String get weSentCodeTo => 'تم إرسال رمز مكون من 4 أرقام من ASHKAL إلى';

  @override
  String weSentCodeToWithSender(String sender) =>
      'تم إرسال رمز مكون من 4 أرقام من $sender إلى';

  @override
  String get resendCodeIn => 'إعادة إرسال الرمز خلال';

  @override
  String get didntReceiveCode => 'لم تستلم الرمز؟ ';

  @override
  String get resend => 'إعادة إرسال';

  @override
  String get verify => 'تحقق';

  // Toast Messages
  @override
  String get otpSentSuccessfully => 'تم إرسال رمز التحقق بنجاح!';

  @override
  String get loginFailed => 'فشل تسجيل الدخول';

  @override
  String get anErrorOccurred => 'حدث خطأ. يرجى المحاولة مرة أخرى.';

  @override
  String get otpVerifiedSuccessfully => 'تم التحقق من الرمز بنجاح!';

  @override
  String get otpVerificationFailed => 'فشل التحقق من الرمز';

  @override
  String get pleaseWaitBeforeRequestingOtp =>
      'يرجى الانتظار قبل طلب رمز تحقق جديد';

  @override
  String get failedToResendOtp => 'فشل في إعادة إرسال رمز التحقق';

  @override
  String get numberBlockedCantLogin => 'الرقم محظور، لا يمكن تسجيل الدخول';

  @override
  String get profileSavedSuccessfully => 'تم حفظ الملف الشخصي بنجاح!';

  @override
  String get failedToSaveProfile => 'فشل في حفظ الملف الشخصي';

  @override
  String get anErrorOccurredWhileSavingPersonalInfo =>
      'حدث خطأ أثناء حفظ المعلومات الشخصية';

  @override
  String get accountDeletedSuccessfully => 'تم حذف الحساب بنجاح';

  @override
  String get failedToDeleteAccount => 'فشل في حذف الحساب';

  @override
  String get anErrorOccurredWhileSavingLocation => 'حدث خطأ أثناء حفظ الموقع';

  @override
  String get openingAppSettings => 'جاري فتح إعدادات التطبيق...';

  @override
  String get interestsUpdatedSuccessfully => 'تم تحديث الاهتمامات بنجاح';

  // Gender Options (for display)
  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثى';

  // Nationality Options (for display - common ones)
  @override
  String get saudi => '🇸🇦 سعودي';

  @override
  String get egyptian => '🇪🇬 مصري';

  @override
  String get jordanian => '🇯🇴 أردني';

  @override
  String get lebanese => '🇱🇧 لبناني';

  @override
  String get syrian => '🇸🇾 سوري';

  @override
  String get iraqi => '🇮🇶 عراقي';

  @override
  String get kuwaiti => '🇰🇼 كويتي';

  @override
  String get emirati => '🇦🇪 إماراتي';

  @override
  String get qatari => '🇶🇦 قطري';

  @override
  String get bahraini => '🇧🇭 بحريني';

  @override
  String get omani => '🇴🇲 عماني';

  @override
  String get yemeni => '🇾🇪 يمني';

  @override
  String get palestinian => '🇵🇸 فلسطيني';

  @override
  String get moroccan => '🇲🇦 مغربي';

  @override
  String get tunisian => '🇹🇳 تونسي';

  @override
  String get algerian => '🇩🇿 جزائري';

  @override
  String get libyan => '🇱🇾 ليبي';

  @override
  String get sudanese => '🇸🇩 سوداني';

  @override
  String get somali => '🇸🇴 صومالي';

  @override
  String get mauritanian => '🇲🇷 موريتاني';

  @override
  String get djiboutian => '🇩🇯 جيبوتي';

  @override
  String get other => '🌍 جنسية أخرى';
}
