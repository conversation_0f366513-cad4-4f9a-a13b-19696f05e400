/// Localization Helper
///
/// Provides helper functions for mapping between display names and storage values
/// for gender and nationality options while maintaining localization support
library localization_helper;

import 'package:flutter/material.dart';
import 'package:towasl/l10n/app_localizations.dart';

/// Helper class for localization-related operations
class LocalizationHelper {
  /// Maps gender display names to storage values
  ///
  /// Returns the English storage value for the given localized display name
  /// This ensures data consistency in the database while showing localized text to users
  static String getGenderStorageValue(
      BuildContext context, String displayName) {
    final localizations = AppLocalizations.of(context);

    if (displayName == localizations.male) {
      return 'Male';
    } else if (displayName == localizations.female) {
      return 'Female';
    }

    // Fallback to the display name if no mapping found
    return displayName;
  }

  /// Maps gender storage values to display names
  ///
  /// Returns the localized display name for the given English storage value
  static String getGenderDisplayName(
      BuildContext context, String storageValue) {
    final localizations = AppLocalizations.of(context);

    switch (storageValue.toLowerCase()) {
      case 'male':
        return localizations.male;
      case 'female':
        return localizations.female;
      default:
        return storageValue;
    }
  }

  /// Maps nationality display names to storage values
  ///
  /// Returns the English storage value for the given localized display name
  /// Display names include flag emojis, but storage values are clean English text
  static String getNationalityStorageValue(
      BuildContext context, String displayName) {
    final localizations = AppLocalizations.of(context);

    // Map localized nationality names (with flags) to English storage values (without flags)
    if (displayName == localizations.saudi) return 'Saudi';
    if (displayName == localizations.egyptian) return 'Egyptian';
    if (displayName == localizations.jordanian) return 'Jordanian';
    if (displayName == localizations.lebanese) return 'Lebanese';
    if (displayName == localizations.syrian) return 'Syrian';
    if (displayName == localizations.iraqi) return 'Iraqi';
    if (displayName == localizations.kuwaiti) return 'Kuwaiti';
    if (displayName == localizations.emirati) return 'Emirati';
    if (displayName == localizations.qatari) return 'Qatari';
    if (displayName == localizations.bahraini) return 'Bahraini';
    if (displayName == localizations.omani) return 'Omani';
    if (displayName == localizations.yemeni) return 'Yemeni';
    if (displayName == localizations.palestinian) return 'Palestinian';
    if (displayName == localizations.moroccan) return 'Moroccan';
    if (displayName == localizations.tunisian) return 'Tunisian';
    if (displayName == localizations.algerian) return 'Algerian';
    if (displayName == localizations.libyan) return 'Libyan';
    if (displayName == localizations.sudanese) return 'Sudanese';
    if (displayName == localizations.somali) return 'Somali';
    if (displayName == localizations.mauritanian) return 'Mauritanian';
    if (displayName == localizations.djiboutian) return 'Djiboutian';
    if (displayName == localizations.other) return 'Other';

    // Fallback to the display name if no mapping found
    return displayName;
  }

  /// Maps nationality storage values to display names
  ///
  /// Returns the localized display name (with flag emoji) for the given English storage value
  static String getNationalityDisplayName(
      BuildContext context, String storageValue) {
    final localizations = AppLocalizations.of(context);

    switch (storageValue.toLowerCase()) {
      case 'saudi':
        return localizations.saudi;
      case 'egyptian':
        return localizations.egyptian;
      case 'jordanian':
        return localizations.jordanian;
      case 'lebanese':
        return localizations.lebanese;
      case 'syrian':
        return localizations.syrian;
      case 'iraqi':
        return localizations.iraqi;
      case 'kuwaiti':
        return localizations.kuwaiti;
      case 'emirati':
        return localizations.emirati;
      case 'qatari':
        return localizations.qatari;
      case 'bahraini':
        return localizations.bahraini;
      case 'omani':
        return localizations.omani;
      case 'yemeni':
        return localizations.yemeni;
      case 'palestinian':
        return localizations.palestinian;
      case 'moroccan':
        return localizations.moroccan;
      case 'tunisian':
        return localizations.tunisian;
      case 'algerian':
        return localizations.algerian;
      case 'libyan':
        return localizations.libyan;
      case 'sudanese':
        return localizations.sudanese;
      case 'somali':
        return localizations.somali;
      case 'mauritanian':
        return localizations.mauritanian;
      case 'djiboutian':
        return localizations.djiboutian;
      case 'other':
        return localizations.other;
      default:
        return storageValue;
    }
  }

  /// Gets localized gender options for display
  ///
  /// Returns a list of localized gender options that can be shown in dropdowns
  static List<String> getLocalizedGenderOptions(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return [
      localizations.male,
      localizations.female,
    ];
  }

  /// Gets localized nationality options for display
  ///
  /// Returns a list of localized nationality options (with flag emojis) that can be shown in dropdowns
  static List<String> getLocalizedNationalityOptions(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return [
      localizations.saudi,
      localizations.egyptian,
      localizations.yemeni,
      localizations.jordanian,
      localizations.syrian,
      localizations.emirati,
      localizations.kuwaiti,
      localizations.qatari,
      localizations.bahraini,
      localizations.omani,
      localizations.lebanese,
      localizations.iraqi,
      localizations.palestinian,
      localizations.moroccan,
      localizations.tunisian,
      localizations.algerian,
      localizations.libyan,
      localizations.sudanese,
      localizations.somali,
      localizations.mauritanian,
      localizations.djiboutian,
      localizations.other,
    ];
  }
}
