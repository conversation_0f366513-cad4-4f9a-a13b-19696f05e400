/// Toast Helper
///
/// Provides localized toast messages throughout the application
/// Wraps ToastCustom with localization support
library toast_helper;

import 'package:flutter/material.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

/// Helper class for showing localized toast messages
class ToastHelper {
  /// Show OTP sent successfully toast
  static void showOtpSentSuccessfully(BuildContext context) {
    ToastCustom.successToast(AppLocalizations.of(context).otpSentSuccessfully);
  }

  /// Show login failed toast
  static void showLoginFailed(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).loginFailed);
  }

  /// Show generic error toast
  static void showAnErrorOccurred(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).anErrorOccurred);
  }

  /// Show OTP verified successfully toast
  static void showOtpVerifiedSuccessfully(BuildContext context) {
    ToastCustom.successToast(AppLocalizations.of(context).otpVerifiedSuccessfully);
  }

  /// Show OTP verification failed toast
  static void showOtpVerificationFailed(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).otpVerificationFailed);
  }

  /// Show please wait before requesting OTP toast
  static void showPleaseWaitBeforeRequestingOtp(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).pleaseWaitBeforeRequestingOtp);
  }

  /// Show failed to resend OTP toast
  static void showFailedToResendOtp(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).failedToResendOtp);
  }

  /// Show number blocked toast
  static void showNumberBlockedCantLogin(BuildContext context) {
    ToastCustom.warningToastCustom(
      AppLocalizations.of(context).numberBlockedCantLogin,
      context,
      true,
    );
  }

  /// Show profile saved successfully toast
  static void showProfileSavedSuccessfully(BuildContext context) {
    ToastCustom.successToast(AppLocalizations.of(context).profileSavedSuccessfully);
  }

  /// Show failed to save profile toast
  static void showFailedToSaveProfile(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).failedToSaveProfile);
  }

  /// Show error while saving personal info toast
  static void showAnErrorOccurredWhileSavingPersonalInfo(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).anErrorOccurredWhileSavingPersonalInfo);
  }

  /// Show account deleted successfully toast
  static void showAccountDeletedSuccessfully(BuildContext context) {
    ToastCustom.successToast(AppLocalizations.of(context).accountDeletedSuccessfully);
  }

  /// Show failed to delete account toast
  static void showFailedToDeleteAccount(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).failedToDeleteAccount);
  }

  /// Show error while saving location toast
  static void showAnErrorOccurredWhileSavingLocation(BuildContext context) {
    ToastCustom.errorToast(AppLocalizations.of(context).anErrorOccurredWhileSavingLocation);
  }

  /// Show opening app settings toast
  static void showOpeningAppSettings(BuildContext context) {
    ToastCustom.successToast(AppLocalizations.of(context).openingAppSettings);
  }

  /// Show custom error toast with message
  static void showCustomError(BuildContext context, String? message) {
    if (message != null && message.isNotEmpty) {
      ToastCustom.errorToast(message);
    } else {
      showAnErrorOccurred(context);
    }
  }

  /// Show custom success toast with message
  static void showCustomSuccess(BuildContext context, String message) {
    ToastCustom.successToast(message);
  }

  /// Show custom warning toast with message
  static void showCustomWarning(BuildContext context, String message) {
    ToastCustom.warningToast(message);
  }
}
