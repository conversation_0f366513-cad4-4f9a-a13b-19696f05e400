/// Validation Helpers
///
/// Provides generic validation functions used throughout the application
/// Centralized location for all validation logic
library validation_helpers;

import 'package:flutter/material.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:towasl/l10n/app_localizations.dart';

/// Generic Validation Functions
///
/// Static methods for validating different types of user input
/// Returns translated error messages or null if validation passes
class ValidationHelpers {
  /// Validates that a field is not empty
  ///
  /// @param context The build context for localization
  /// @param value The string to validate
  /// @return An error message if empty, null otherwise
  static String? validateRequired(BuildContext context, String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).required;
    }
    return null;
  }

  /// Validates a birth year
  ///
  /// Checks that:
  /// - The field is not empty
  /// - The year is a 4-digit number
  /// - The age is between 18 and 120 years
  ///
  /// @param context The build context for localization
  /// @param value The year string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateYear(BuildContext context, String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).required;
    }

    if (value.length != 4) {
      return AppLocalizations.of(context).invalidYear;
    }

    try {
      final year = int.parse(value);
      final currentYear = DateTime.now().year;
      final age = currentYear - year;

      if (age < 18 || age > 120) {
        return AppLocalizations.of(context).incorrect;
      }
    } catch (e) {
      return AppLocalizations.of(context).invalidYear;
    }

    return null;
  }

  /// Validates a phone number
  ///
  /// Checks that:
  /// - The phone number is not null
  /// - The phone number matches the Saudi format (05xxxxxxxx)
  ///
  /// @param context The build context for localization
  /// @param value The PhoneNumber object to validate
  /// @return An error message if invalid, null otherwise
  static String? validatePhone(BuildContext context, PhoneNumber? value) {
    if (value == null) {
      return AppLocalizations.of(context).required;
    }

    // Saudi phone number format: 05xxxxxxxx (10 digits total)
    final regex = RegExp(r"^05[0-9]{8}$");

    if (!regex.hasMatch(value.number)) {
      return AppLocalizations.of(context).mobileFormat;
    }

    return null;
  }

  /// Validates an email address
  ///
  /// Checks that the email follows a valid format
  ///
  /// @param context The build context for localization
  /// @param value The email string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateEmail(BuildContext context, String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).required;
    }

    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value)) {
      return AppLocalizations.of(context).invalidEmailFormat;
    }

    return null;
  }

  /// Validates a name field
  ///
  /// Checks that the name is not empty and contains only valid characters
  ///
  /// @param context The build context for localization
  /// @param value The name string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateName(BuildContext context, String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).required;
    }

    if (value.trim().length < 2) {
      return AppLocalizations.of(context).nameTooShort;
    }

    return null;
  }

  /// Validates a password
  ///
  /// Checks password strength requirements
  ///
  /// @param context The build context for localization
  /// @param value The password string to validate
  /// @return An error message if invalid, null otherwise
  static String? validatePassword(BuildContext context, String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).required;
    }

    if (value.length < 6) {
      return AppLocalizations.of(context).passwordTooShort;
    }

    return null;
  }
}
