# Helper Functions Organization

This directory contains the reorganized helper functions for the Towasl application. The helpers have been restructured to follow a feature-based organization pattern for better maintainability and code organization.

## Directory Structure

```
lib/shared/helpers/
├── helpers.dart                           # Main export file for easy imports
├── validation_helpers.dart                # Generic validation functions
├── text_helpers.dart                     # Text manipulation utilities
├── internationalization/
│   └── country_data.dart                 # Country data and utilities
└── README.md                             # This documentation file

lib/features/[feature]/helpers/
├── authentication/helpers/
│   └── auth_validation_helpers.dart      # Auth-specific validations
├── onboarding/helpers/
│   ├── country_helpers.dart              # Country selection utilities
│   └── onboarding_progress_helpers.dart  # Progress tracking utilities
└── profile/helpers/
    └── user_data_helpers.dart            # User data manipulation utilities
```

## Migration from Old Structure

### Before (Centralized)
```
lib/core/helpers/
├── profile_validation.dart               # Mixed validation functions
├── country_helper_custom.dart           # Country utilities
└── custom_countries.dart                # Country data
```

### After (Feature-Based)
- **Shared helpers**: Generic utilities used across multiple features
- **Feature-specific helpers**: Utilities specific to individual features
- **Domain-specific helpers**: Utilities organized by domain (e.g., internationalization)

## Usage Examples

### Importing Shared Helpers
```dart
// Import all shared helpers
import 'package:towasl/shared/helpers/helpers.dart';

// Or import specific helpers
import 'package:towasl/shared/helpers/validation_helpers.dart';
import 'package:towasl/shared/helpers/text_helpers.dart';
```

### Using Validation Helpers
```dart
// Generic validation
String? error = ValidationHelpers.validateRequired(value);
String? yearError = ValidationHelpers.validateYear(birthYear);

// Authentication-specific validation
import 'package:towasl/features/authentication/helpers/auth_validation_helpers.dart';
String? phoneError = AuthValidationHelpers.validateSaudiPhone(phoneNumber);
```

### Using Text Helpers
```dart
// Text manipulation
bool isNum = TextHelpers.isNumeric("123");
String normalized = TextHelpers.normalizeForSearch("Café");
String formatted = TextHelpers.formatPhoneNumber("0501234567");
```

### Using Country Helpers
```dart
// Country data
import 'package:towasl/shared/helpers/internationalization/country_data.dart';
Country? saudi = CountryHelpers.findByCode('SA');

// Country search (in onboarding)
import 'package:towasl/features/onboarding/helpers/country_helpers.dart';
List<Country> results = countries.searchCountries("Saudi");
```

### Using User Data Helpers
```dart
import 'package:towasl/features/profile/helpers/user_data_helpers.dart';

int age = UserDataHelpers.getUserAge(user);
bool complete = UserDataHelpers.isProfileComplete(user);
String display = UserDataHelpers.getDisplayName(user);
```

## Helper Categories

### 1. Shared Helpers (`lib/shared/helpers/`)
**Purpose**: Generic utilities used across multiple features

- **validation_helpers.dart**: Form validation functions
- **text_helpers.dart**: String manipulation and formatting
- **internationalization/country_data.dart**: Country data and utilities

### 2. Authentication Helpers (`lib/features/authentication/helpers/`)
**Purpose**: Authentication-specific utilities

- **auth_validation_helpers.dart**: Phone validation, OTP validation, auth-specific checks

### 3. Onboarding Helpers (`lib/features/onboarding/helpers/`)
**Purpose**: Onboarding flow utilities

- **country_helpers.dart**: Country selection and search functionality
- **onboarding_progress_helpers.dart**: Progress calculation and step management

### 4. Profile Helpers (`lib/features/profile/helpers/`)
**Purpose**: User profile management utilities

- **user_data_helpers.dart**: User data manipulation, age calculation, profile completion

## Migration Guide

### For Existing Code

1. **Update imports** from old helper locations to new ones
2. **Use new class names** (e.g., `VALIDATIONS` → `ValidationHelpers`)
3. **Leverage feature-specific helpers** for better organization

### Backward Compatibility

The old helper files have been updated with:
- `@Deprecated` annotations pointing to new locations
- Wrapper functions that delegate to new implementations
- Clear migration paths in documentation

### Example Migration

**Before:**
```dart
import 'package:towasl/core/helpers/profile_validation.dart';

String? error = VALIDATIONS.validateRequired(value);
```

**After:**
```dart
import 'package:towasl/shared/helpers/validation_helpers.dart';

String? error = ValidationHelpers.validateRequired(value);
```

## Benefits of New Structure

1. **Better Organization**: Helpers are grouped by feature and domain
2. **Improved Maintainability**: Easier to find and update related functionality
3. **Reduced Coupling**: Feature-specific helpers don't pollute shared space
4. **Clear Separation**: Generic vs. feature-specific utilities are clearly separated
5. **Easier Testing**: Helpers can be tested in isolation by feature
6. **Better Documentation**: Each helper category has clear purpose and scope

## Best Practices

1. **Use shared helpers** for generic functionality used across features
2. **Create feature-specific helpers** for domain-specific logic
3. **Keep helpers pure** - avoid side effects when possible
4. **Document helper functions** with clear parameter and return descriptions
5. **Use consistent naming** - follow the `[Domain]Helpers` pattern
6. **Group related functions** in the same helper class
7. **Prefer static methods** for stateless utility functions

## Future Enhancements

1. **Add more domain-specific helpers** as the app grows
2. **Create helper interfaces** for better testability
3. **Add helper factories** for complex object creation
4. **Implement helper caching** for expensive operations
5. **Add helper composition** for complex validation chains
