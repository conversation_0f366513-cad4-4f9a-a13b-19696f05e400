/// Text Helper Functions
///
/// Provides utility functions for text manipulation and processing
/// Includes string normalization, search functionality, and formatting
library text_helpers;

/// Text manipulation utilities
///
/// Static methods for common text processing operations
class TextHelpers {
  /// Checks if a string is numeric
  ///
  /// Removes any '+' characters before checking
  /// Used for validating phone numbers and dial codes
  ///
  /// @param s The string to check
  /// @return True if the string contains only numeric characters
  static bool isNumeric(String s) =>
      s.isNotEmpty && int.tryParse(s.replaceAll("+", "")) != null;

  /// Removes diacritical marks from a string
  ///
  /// Replaces accented characters with their non-accented equivalents
  /// Used for normalizing text for search and comparison
  ///
  /// @param str The string to process
  /// @return The string with diacritical marks removed
  static String removeDiacritics(String str) {
    // Characters with diacritical marks
    const withDia =
        'ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž';

    // Equivalent characters without diacritical marks
    const withoutDia =
        'AAAAAAaaaaaaOOOOOOOooooooEEEEeeeeeCcDIIIIiiiiUUUUuuuuNnSsYyyZz';

    // Replace each character with its non-diacritical equivalent
    for (int i = 0; i < withDia.length; i++) {
      str = str.replaceAll(withDia[i], withoutDia[i]);
    }

    return str;
  }

  /// Normalizes a string for search operations
  ///
  /// Removes diacritics and converts to lowercase
  /// Used for consistent text searching and comparison
  ///
  /// @param str The string to normalize
  /// @return The normalized string
  static String normalizeForSearch(String str) {
    return removeDiacritics(str.toLowerCase());
  }

  /// Capitalizes the first letter of a string
  ///
  /// @param str The string to capitalize
  /// @return The string with first letter capitalized
  static String capitalize(String str) {
    if (str.isEmpty) return str;
    return str[0].toUpperCase() + str.substring(1).toLowerCase();
  }

  /// Capitalizes the first letter of each word in a string
  ///
  /// @param str The string to title case
  /// @return The string with each word capitalized
  static String toTitleCase(String str) {
    if (str.isEmpty) return str;
    return str.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// Truncates a string to a specified length
  ///
  /// Adds ellipsis if the string is longer than the specified length
  ///
  /// @param str The string to truncate
  /// @param maxLength The maximum length
  /// @return The truncated string
  static String truncate(String str, int maxLength) {
    if (str.length <= maxLength) return str;
    return '${str.substring(0, maxLength)}...';
  }

  /// Formats a phone number for display
  ///
  /// Adds appropriate formatting for Saudi phone numbers
  ///
  /// @param phoneNumber The raw phone number
  /// @return The formatted phone number
  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.length == 10 && phoneNumber.startsWith('05')) {
      return '${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 6)} ${phoneNumber.substring(6)}';
    }
    return phoneNumber;
  }

  /// Removes all whitespace from a string
  ///
  /// @param str The string to process
  /// @return The string without whitespace
  static String removeWhitespace(String str) {
    return str.replaceAll(RegExp(r'\s+'), '');
  }

  /// Checks if a string contains only Arabic characters
  ///
  /// @param str The string to check
  /// @return True if the string contains only Arabic characters
  static bool isArabic(String str) {
    final arabicRegex = RegExp(r'^[\u0600-\u06FF\s]+$');
    return arabicRegex.hasMatch(str);
  }

  /// Checks if a string contains only English characters
  ///
  /// @param str The string to check
  /// @return True if the string contains only English characters
  static bool isEnglish(String str) {
    final englishRegex = RegExp(r'^[a-zA-Z\s]+$');
    return englishRegex.hasMatch(str);
  }

  /// Converts Arabic numerals to English numerals
  ///
  /// Maps Arabic-Indic digits (٠-٩) to ASCII digits (0-9)
  /// Used for number input fields to ensure consistent data format
  ///
  /// @param text The text containing Arabic numerals
  /// @return The text with Arabic numerals converted to English numerals
  static String convertArabicNumeralsToEnglish(String text) {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    String result = text;
    for (int i = 0; i < arabicNumerals.length; i++) {
      result = result.replaceAll(arabicNumerals[i], englishNumerals[i]);
    }
    return result;
  }
}
