/// User Progress Helpers
///
/// Utilities for calculating and managing user profile completion progress
/// Helps determine user completion status and next steps in the setup flow
library user_progress_helpers;

import 'package:towasl/shared/models/user_model.dart';

/// User setup step enumeration
enum UserSetupStep {
  interests,
  location,
  personalInfo,
  completed,
}

/// User progress utilities
class UserProgressHelpers {
  /// Total number of setup steps
  static const int totalSteps = 3;

  /// Check if user needs onboarding
  ///
  /// Determines if the user needs to go through the onboarding flow
  /// Note: name is not required for profile completion
  ///
  /// @param user The user model to check
  /// @return True if user needs onboarding, false if profile is complete
  static bool needsOnboarding(UserModel user) {
    return user.userInterest == null ||
        user.userLocation == null ||
        (user.nationality ?? "").isEmpty ||
        (user.gender ?? "").isEmpty ||
        (user.birthdayYear ?? "").isEmpty;
  }

  /// Get onboarding completion percentage
  ///
  /// Calculates how much of the onboarding process the user has completed
  ///
  /// @param user The user model to check
  /// @return Completion percentage (0.0 to 1.0)
  static double getOnboardingProgress(UserModel user) {
    int completedSteps = 0;

    if (user.userInterest != null && (user.userInterest?.isNotEmpty ?? false)) {
      completedSteps++;
    }
    if (user.userLocation != null) {
      completedSteps++;
    }
    if (!_isPersonalInfoComplete(user)) {
      completedSteps++;
    }

    return completedSteps / totalSteps;
  }

  /// Get the next setup step for the user
  ///
  /// Determines which step the user should complete next
  ///
  /// @param user The user model to check
  /// @return The next setup step
  static UserSetupStep getNextStep(UserModel user) {
    // Check in order of setup flow
    if (user.userInterest == null || (user.userInterest?.isEmpty ?? true)) {
      return UserSetupStep.interests;
    }

    if (user.userLocation == null) {
      return UserSetupStep.location;
    }

    if (!_isPersonalInfoComplete(user)) {
      return UserSetupStep.personalInfo;
    }

    return UserSetupStep.completed;
  }

  /// Get completed steps count
  ///
  /// @param user The user model to check
  /// @return Number of completed steps
  static int getCompletedStepsCount(UserModel user) {
    int completedSteps = 0;

    if (user.userInterest != null && (user.userInterest?.isNotEmpty ?? false)) {
      completedSteps++;
    }
    if (user.userLocation != null) {
      completedSteps++;
    }
    if (_isPersonalInfoComplete(user)) {
      completedSteps++;
    }

    return completedSteps;
  }

  /// Get remaining steps count
  ///
  /// @param user The user model to check
  /// @return Number of remaining steps
  static int getRemainingStepsCount(UserModel user) {
    return totalSteps - getCompletedStepsCount(user);
  }

  /// Check if interests step is complete
  ///
  /// @param user The user model to check
  /// @return True if interests are selected
  static bool isInterestsComplete(UserModel user) {
    return user.userInterest != null &&
        (user.userInterest?.isNotEmpty ?? false);
  }

  /// Check if location step is complete
  ///
  /// @param user The user model to check
  /// @return True if location is set
  static bool isLocationComplete(UserModel user) {
    return user.userLocation != null;
  }

  /// Check if personal info step is complete
  ///
  /// @param user The user model to check
  /// @return True if personal info is complete
  static bool isPersonalInfoComplete(UserModel user) {
    return _isPersonalInfoComplete(user);
  }

  /// Get setup step title
  ///
  /// @param step The setup step
  /// @return The localized title for the step
  static String getStepTitle(UserSetupStep step) {
    switch (step) {
      case UserSetupStep.interests:
        return 'Select Interests';
      case UserSetupStep.location:
        return 'Set Location';
      case UserSetupStep.personalInfo:
        return 'Personal Information';
      case UserSetupStep.completed:
        return 'Completed';
    }
  }

  /// Get setup step description
  ///
  /// @param step The setup step
  /// @return The localized description for the step
  static String getStepDescription(UserSetupStep step) {
    switch (step) {
      case UserSetupStep.interests:
        return 'Choose your interests to find better matches';
      case UserSetupStep.location:
        return 'Set your location to find people nearby';
      case UserSetupStep.personalInfo:
        return 'Complete your profile information';
      case UserSetupStep.completed:
        return 'Your profile is complete!';
    }
  }

  /// Private helper to check personal info completion
  /// Note: name is not required for profile completion during onboarding
  static bool _isPersonalInfoComplete(UserModel user) {
    return (user.nationality ?? "").isNotEmpty &&
        (user.gender ?? "").isNotEmpty &&
        (user.birthdayYear ?? "").isNotEmpty;
  }
}
