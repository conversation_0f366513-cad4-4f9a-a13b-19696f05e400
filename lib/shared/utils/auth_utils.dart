/// Authentication Utilities
///
/// Utility functions for authentication-related operations
/// Contains methods migrated from the deprecated social login controller
library auth_utils;

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:towasl/shared/services/firebase_service.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

/// Authentication utility functions
///
/// Static methods for common authentication operations
class AuthUtils {
  /// Check if a user is blocked
  ///
  /// Queries Firestore to check if the user with the given identifier is blocked
  /// Shows a warning toast if the user is blocked
  ///
  /// @param identifier The email or mobile to check
  /// @param context The BuildContext for showing toasts
  /// @param firebaseService The Firebase service for database operations
  /// @return True if user is blocked, false otherwise
  static Future<bool> checkUserBlockedStatus(String identifier,
      BuildContext context, FirebaseService firebaseService) async {
    // Check if the context is still valid
    if (!context.mounted) return false;

    try {
      // First check by mobile field
      var querySnapshot = await firebaseService.firestore
          .collection("users")
          .where("is_blocked", isEqualTo: true)
          .where("mobile", isEqualTo: identifier)
          .limit(1)
          .get();

      // If no results, try by email field (for backward compatibility)
      if (querySnapshot.docs.isEmpty) {
        querySnapshot = await firebaseService.firestore
            .collection("users")
            .where("is_blocked", isEqualTo: true)
            .where("email", isEqualTo: identifier)
            .limit(1)
            .get();
      }

      if (kDebugMode) {
        print("Checking blocked status for: $identifier");
      }

      if (querySnapshot.size > 0) {
        // Check again if context is still mounted before showing toast
        if (context.mounted) {
          // User is blocked, show warning toast
          ToastCustom.warningToastCustom(
              "الرقم محظور، لا يمكن تسجيل الدخول", context, true);
        }
        return true;
      } else {
        // User is not blocked
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error checking blocked status: $e");
      }
      return false;
    }
  }

  /// Generates a cryptographically secure random nonce
  ///
  /// Used for secure authentication flows that require a nonce
  /// (e.g., OAuth flows, secure token generation)
  ///
  /// @param length The length of the nonce to generate (default: 32)
  /// @return A random string of the specified length
  static String generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }
}
