/// Version Comparator Utility
///
/// Provides utilities for comparing semantic version strings
/// Handles version comparison logic for app update functionality
library version_comparator;

import 'package:flutter/foundation.dart';

/// Utility class for comparing semantic version strings
///
/// Provides methods to compare version strings in the format "x.y.z"
/// Used for determining if app updates are available or required
class VersionComparator {
  /// Compare two version strings
  ///
  /// Returns:
  /// - 1 if version1 > version2
  /// - 0 if version1 == version2
  /// - -1 if version1 < version2
  ///
  /// @param version1 First version string (e.g., "1.2.3")
  /// @param version2 Second version string (e.g., "1.1.0")
  /// @return Comparison result as integer
  static int compareVersions(String version1, String version2) {
    try {
      if (kDebugMode) {
        print('VersionComparator: Comparing $version1 with $version2');
      }

      // Handle null or empty versions
      if (version1.isEmpty && version2.isEmpty) return 0;
      if (version1.isEmpty) return -1;
      if (version2.isEmpty) return 1;

      // Split versions into parts
      final parts1 =
          version1.split('.').map((e) => int.tryParse(e) ?? 0).toList();
      final parts2 =
          version2.split('.').map((e) => int.tryParse(e) ?? 0).toList();

      // Ensure both lists have the same length (pad with zeros)
      final maxLength =
          parts1.length > parts2.length ? parts1.length : parts2.length;

      while (parts1.length < maxLength) {
        parts1.add(0);
      }
      while (parts2.length < maxLength) {
        parts2.add(0);
      }

      // Compare each part
      for (int i = 0; i < maxLength; i++) {
        if (parts1[i] > parts2[i]) {
          if (kDebugMode) {
            print('VersionComparator: $version1 > $version2');
          }
          return 1;
        } else if (parts1[i] < parts2[i]) {
          if (kDebugMode) {
            print('VersionComparator: $version1 < $version2');
          }
          return -1;
        }
      }

      if (kDebugMode) {
        print('VersionComparator: $version1 == $version2');
      }
      return 0;
    } catch (e) {
      if (kDebugMode) {
        print('VersionComparator: Error comparing versions - $e');
      }
      // On error, assume versions are equal
      return 0;
    }
  }

  /// Check if version1 is greater than version2
  ///
  /// @param version1 First version string
  /// @param version2 Second version string
  /// @return True if version1 > version2
  static bool isGreaterThan(String version1, String version2) {
    return compareVersions(version1, version2) > 0;
  }

  /// Check if version1 is less than version2
  ///
  /// @param version1 First version string
  /// @param version2 Second version string
  /// @return True if version1 < version2
  static bool isLessThan(String version1, String version2) {
    return compareVersions(version1, version2) < 0;
  }

  /// Check if version1 equals version2
  ///
  /// @param version1 First version string
  /// @param version2 Second version string
  /// @return True if version1 == version2
  static bool isEqual(String version1, String version2) {
    return compareVersions(version1, version2) == 0;
  }

  /// Check if version1 is greater than or equal to version2
  ///
  /// @param version1 First version string
  /// @param version2 Second version string
  /// @return True if version1 >= version2
  static bool isGreaterThanOrEqual(String version1, String version2) {
    return compareVersions(version1, version2) >= 0;
  }

  /// Check if version1 is less than or equal to version2
  ///
  /// @param version1 First version string
  /// @param version2 Second version string
  /// @return True if version1 <= version2
  static bool isLessThanOrEqual(String version1, String version2) {
    return compareVersions(version1, version2) <= 0;
  }

  /// Validate version string format
  ///
  /// Checks if the version string follows semantic versioning format
  ///
  /// @param version Version string to validate
  /// @return True if version format is valid
  static bool isValidVersion(String version) {
    if (version.isEmpty) return false;

    final regex = RegExp(r'^\d+(\.\d+)*$');
    return regex.hasMatch(version);
  }
}
