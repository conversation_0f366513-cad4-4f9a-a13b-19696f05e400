/// Custom Toast Notifications
///
/// Provides standardized toast notifications for different message types
/// Used throughout the application for user feedback
library toasts_custom;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Custom Toast Notification Class
///
/// Provides static methods for displaying different types of toast notifications
/// Includes warning, error, success, and custom toast types
class ToastCustom {
  /// Displays a warning toast notification
  ///
  /// Shows a red toast with the provided message at the bottom of the screen
  /// Text is right-aligned for RTL languages
  ///
  /// @param description The message to display in the toast
  static warningToast(String description) {
    Fluttertoast.showToast(
        msg: description,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: AppColors.red,
        textColor: AppColors.whitePure,
        fontSize: 16.0);
  }

  /// Displays an error toast notification
  ///
  /// Shows a red toast with the provided message at the bottom of the screen
  /// Identical to warningToast, kept for semantic clarity
  ///
  /// @param description The message to display in the toast
  static errorToast(String description) {
    Fluttertoast.showToast(
        msg: description,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: AppColors.red,
        textColor: AppColors.whitePure,
        fontSize: 16.0);
  }

  /// Displays a success toast notification
  ///
  /// Shows a green toast with the provided message at the bottom of the screen
  ///
  /// @param description The message to display in the toast
  static successToast(String description) {
    Fluttertoast.showToast(
        msg: description,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: AppColors.green,
        textColor: AppColors.whitePure,
        fontSize: 16.0);
  }

  /// Displays a custom toast notification for warnings or blockages
  ///
  /// Shows a colored toast with an icon and the provided message at the top of the screen
  /// Red for blockage messages, yellow for warning messages
  ///
  /// @param description The message to display in the toast
  /// @param context The BuildContext for showing the toast
  /// @param isBlockage Whether to show a blockage (true) or warning (false) style
  static warningToastCustom(
      String description, BuildContext context, bool isBlockage) {
    // Initialize FToast with the current context
    FToast fToast = FToast();
    fToast.init(context);

    // Create a custom toast widget
    Widget toast = Container(
      padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 9.6),
      width: double.infinity,
      decoration: BoxDecoration(
          // Rounded corners
          borderRadius: BorderRadius.circular(8.33),
          // Background color based on message type
          color:
              isBlockage ? AppColors.redLightest : AppColors.yellowLightest,
          // Border color based on message type
          border: Border.all(
              color: isBlockage
                  ? AppColors.redLightSalamon
                  : AppColors.yellow)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon based on message type
          SvgPicture.asset(
              isBlockage ? AppIcons.blockRedIcon : AppIcons.warningIcon),
          // Spacing
          const SizedBox(
            width: 10.0,
          ),
          // Message text with RTL alignment
          Expanded(
            child: Directionality(
              textDirection: TextDirection.rtl,
              child: Text(
                description,
                style: styleRedNormal,
                textAlign: TextAlign.right,
              ),
            ),
          ),
        ],
      ),
    );

    // Show the toast at the top of the screen for 15 seconds
    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: const Duration(seconds: 3),
    );
  }
}
