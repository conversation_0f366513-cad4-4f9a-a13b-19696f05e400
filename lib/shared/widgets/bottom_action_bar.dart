/// Bottom Action Bar Widget
///
/// A reusable bottom bar component with action button
/// Used across onboarding screens for consistent navigation
library bottom_action_bar;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';

/// Bottom Action Bar Widget
///
/// Provides a consistent bottom bar with action button across onboarding screens
/// Includes loading states and proper styling
class BottomActionBar extends StatelessWidget {
  /// Text to display on the action button
  final String buttonText;

  /// Function to execute when button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is in loading state
  final bool isLoading;

  /// Whether the button should be enabled
  final bool isEnabled;

  /// Creates a BottomActionBar
  const BottomActionBar({
    super.key,
    required this.buttonText,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.whitePure,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: (isEnabled && !isLoading) ? onPressed : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                foregroundColor: AppColors.whitePure,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.whitePure),
                      ),
                    )
                  : Text(
                      buttonText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
