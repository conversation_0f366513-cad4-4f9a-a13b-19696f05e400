/// Arabic to English Numeral Input Formatter
///
/// Custom TextInputFormatter that automatically converts Arabic numerals (٠-٩)
/// to English numerals (0-9) as the user types in number input fields
library arabic_to_english_numeral_formatter;

import 'package:flutter/services.dart';
import 'package:towasl/shared/helpers/text_helpers.dart';

/// Input formatter that converts Arabic numerals to English numerals
///
/// This formatter automatically converts Arabic-Indic digits (٠-٩) to ASCII digits (0-9)
/// when the user types in number input fields. This ensures consistent data format
/// regardless of the keyboard language setting.
///
/// Usage:
/// ```dart
/// TextField(
///   inputFormatters: [
///     ArabicToEnglishNumeralFormatter(),
///     FilteringTextInputFormatter.digitsOnly,
///   ],
///   keyboardType: TextInputType.number,
/// )
/// ```
class ArabicToEnglishNumeralFormatter extends TextInputFormatter {
  /// Creates an Arabic to English numeral formatter
  const ArabicToEnglishNumeralFormatter();

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Convert Arabic numerals to English numerals
    final convertedText = TextHelpers.convertArabicNumeralsToEnglish(newValue.text);
    
    // If no conversion was needed, return the original value
    if (convertedText == newValue.text) {
      return newValue;
    }
    
    // Calculate the cursor position after conversion
    // Since we're only replacing characters 1:1, the cursor position remains the same
    final newSelection = newValue.selection.copyWith(
      baseOffset: newValue.selection.baseOffset,
      extentOffset: newValue.selection.extentOffset,
    );
    
    return TextEditingValue(
      text: convertedText,
      selection: newSelection,
    );
  }
}
