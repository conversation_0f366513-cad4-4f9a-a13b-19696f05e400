/// Custom Text Field Widget
///
/// Provides a standardized text input field with consistent styling
/// Used throughout the application for text input with validation
library textfield_custom;

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Custom Text Field Widget
///
/// A reusable text input field component with customizable appearance and behavior
/// Provides consistent styling with the app's design language
class CustomTextfield extends StatefulWidget {
  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Icon button to display in the text field (usually for password visibility)
  final IconButton? icon;

  /// Hint text to display when the field is empty
  final String? hint;

  /// Label text to display above the field
  final String? label;

  /// Whether this is a password field (obscures text)
  final bool isPass;

  /// Whether this field is in settings mode (affects styling)
  final bool? isSettings;

  /// Whether the field is read-only
  final bool? readOnly;

  /// Controller for the text field
  final TextEditingController? controller;

  /// Validation function that returns an error message or null
  final String? Function(String?) validation;

  /// Function to call when the field is tapped (for read-only fields)
  final String? Function()? onTap;

  /// Function to call when the text changes
  final String? Function(String val)? onChanged;

  /// Keyboard type to use (e.g., email, number)
  final TextInputType? keyboardType;

  /// Input formatters to restrict or format input
  final List<TextInputFormatter>? inputFormatters;
  /// Creates a CustomTextfield
  ///
  /// @param icon Optional icon button to display in the field
  /// @param hint Optional hint text to display when empty
  /// @param controller Optional controller for the text field
  /// @param focusNode Optional focus node for controlling focus
  /// @param validation Required validation function
  /// @param isPass Required flag for password field behavior
  /// @param label Optional label text to display above the field
  /// @param readOnly Optional flag for read-only behavior
  /// @param onTap Optional function to call when tapped (for read-only fields)
  /// @param keyboardType Optional keyboard type to use
  /// @param inputFormatters Optional input formatters to restrict input
  /// @param onChanged Optional function to call when text changes
  /// @param isSettings Optional flag for settings mode styling
  const CustomTextfield(
      {super.key,
      this.icon,
      this.hint,
      this.controller,
      this.focusNode,
      required this.validation,
      required this.isPass,
      this.label,
      this.readOnly,
      this.onTap,
      this.keyboardType,
      this.inputFormatters,
      this.onChanged,
      this.isSettings});
  @override
  CustomTextfieldState createState() => CustomTextfieldState();
}

/// State class for CustomTextfield
class CustomTextfieldState extends State<CustomTextfield> {
  /// Shorthand accessor for the validation function
  get validation => widget.validation;

  @override
  Widget build(BuildContext context) {
    // TEXT FIELD IMPLEMENTATION ----------
    return TextFormField(
      // Handle text changes
      onChanged: widget.onChanged ?? (val) {},

      // Text alignment based on label and settings
      textAlign: widget.label.toString() == "null" &&
              (widget.isSettings ?? false) == false
          ? TextAlign.center
          : TextAlign.start,

      // Keyboard type with fallback
      keyboardType: widget.keyboardType ?? TextInputType.text,

      // Handle tap events
      onTap: widget.onTap ?? () {},

      // Read-only state
      readOnly: widget.readOnly ?? false,

      // Validation function
      validator: validation,

      // Focus and controller
      focusNode: widget.focusNode,
      controller: widget.controller,

      // Password field behavior
      obscureText: widget.isPass,

      // Cursor styling
      cursorColor: AppColors.primaryPurple,

      // Input formatting
      inputFormatters: widget.inputFormatters ?? [],
      // Field decoration based on whether label exists
      decoration: widget.label.toString() == "null"
          ? InputDecoration(
              // Allow multi-line error messages
              errorMaxLines: 2,

              // Padding based on settings mode and platform
              contentPadding: (widget.isSettings ?? false) == false
                  ? const EdgeInsets.all(12)
                  : EdgeInsets.all(Platform.isIOS ? 14 : 17),

              // Default border
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.red, width: 2),
              ),

              // Focused border
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.primaryPurple, width: 2),
              ),

              // Error borders
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.red, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.red, width: 2),
              ),

              // Icon and hint
              suffixIcon: widget.icon,
              hintText: widget.hint,
            )
          // Decoration when label exists
          : InputDecoration(
              // Allow multi-line error messages
              errorMaxLines: 2,

              // Label with forced LTR direction
              label: Directionality(
                textDirection: TextDirection.ltr,
                child: Text(
                  widget.label.toString(),
                  style: stylePrimaryLarge,
                ),
              ),

              // Consistent padding
              contentPadding: const EdgeInsets.all(17),

              // Default border
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.red, width: 2),
              ),

              // Focused border
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.primaryPurple, width: 2),
              ),

              // Error borders
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.red, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.red, width: 2),
              ),

              // Icon and hint
              suffixIcon: widget.icon,
              hintText: widget.hint,
            ),
    );
    // TEXT FIELD IMPLEMENTATION ##########
  }
}
