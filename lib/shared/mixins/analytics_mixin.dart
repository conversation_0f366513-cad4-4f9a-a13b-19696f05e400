/// Analytics Mixin
///
/// Provides analytics functionality to widgets
/// Simplifies screen tracking and event logging
library analytics_mixin;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/providers/service_providers.dart';

/// Analytics Mixin
///
/// Mixin that provides analytics functionality to ConsumerStatefulWidget states
/// Automatically handles screen view tracking and provides helper methods
mixin AnalyticsMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  /// Track screen view when widget is initialized
  ///
  /// @param screenName The name of the screen to track
  /// @param screenClass The class name of the screen (optional)
  /// @param parameters Additional parameters (optional)
  Future<void> trackScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logScreenView(
        screenName: screenName,
        screenClass: screenClass ?? runtimeType.toString(),
        parameters: parameters,
      );
    } catch (e) {
      debugPrint('Analytics: Error tracking screen view - $e');
    }
  }

  /// Log a custom event
  ///
  /// @param name The event name
  /// @param parameters Event parameters (optional)
  Future<void> logEvent({
    required String name,
    Map<String, Object?>? parameters,
  }) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logEvent(
        name: name,
        parameters: parameters,
      );
    } catch (e) {
      debugPrint('Analytics: Error logging event - $e');
    }
  }

  /// Log button click event
  ///
  /// @param buttonName The name of the button clicked
  /// @param screenName The screen where the button was clicked
  Future<void> logButtonClick({
    required String buttonName,
    required String screenName,
  }) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logButtonClick(
        buttonName: buttonName,
        screenName: screenName,
      );
    } catch (e) {
      debugPrint('Analytics: Error logging button click - $e');
    }
  }

  /// Log navigation event
  ///
  /// @param from The source screen
  /// @param to The destination screen
  Future<void> logNavigation({
    required String from,
    required String to,
  }) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logNavigation(
        from: from,
        to: to,
      );
    } catch (e) {
      debugPrint('Analytics: Error logging navigation - $e');
    }
  }

  /// Log user login event
  ///
  /// @param loginMethod The method used to login (e.g., 'mobile_otp')
  Future<void> logLogin(String loginMethod) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logLogin(loginMethod);
    } catch (e) {
      debugPrint('Analytics: Error logging login - $e');
    }
  }

  /// Log user signup event
  ///
  /// @param signUpMethod The method used to sign up (e.g., 'mobile_otp')
  Future<void> logSignUp(String signUpMethod) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logSignUp(signUpMethod);
    } catch (e) {
      debugPrint('Analytics: Error logging sign up - $e');
    }
  }

  /// Set user ID for analytics
  ///
  /// @param userId The user ID to set
  Future<void> setUserId(String? userId) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.setUserId(userId);
    } catch (e) {
      debugPrint('Analytics: Error setting user ID - $e');
    }
  }

  /// Set user property
  ///
  /// @param name The property name
  /// @param value The property value
  Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.setUserProperty(
        name: name,
        value: value,
      );
    } catch (e) {
      debugPrint('Analytics: Error setting user property - $e');
    }
  }

  /// Log profile completion step
  ///
  /// @param step The completion step (e.g., 'interests', 'location', 'personal_info')
  Future<void> logProfileCompletionStep(String step) async {
    try {
      final analyticsService = ref.read(analyticsServiceProvider);
      await analyticsService.logProfileCompletionStep(step);
    } catch (e) {
      debugPrint('Analytics: Error logging profile completion step - $e');
    }
  }
}

/// Analytics Screen Names
///
/// Centralized definition of screen names for consistent analytics tracking
class AnalyticsScreenNames {
  // Private constructor to prevent instantiation
  AnalyticsScreenNames._();

  // Authentication Screens
  static const String splash = 'splash_screen';
  static const String welcome = 'welcome_screen';
  static const String signupLogin = 'signup_login_screen';
  static const String mobileOtp = 'mobile_otp_screen';

  // Onboarding Screens
  static const String interests = 'interests_screen';
  static const String location = 'location_screen';
  static const String personalInfo = 'personal_info_screen';

  // Main App Screens
  static const String home = 'home_screen';
  static const String profile = 'profile_screen';

  // Profile Edit Screens
  static const String editInterests = 'edit_interests_screen';
  static const String editLocation = 'edit_location_screen';
  static const String editPersonalInfo = 'edit_personal_info_screen';
}

/// Analytics Event Names
///
/// Centralized definition of event names for consistent analytics tracking
class AnalyticsEventNames {
  // Private constructor to prevent instantiation
  AnalyticsEventNames._();

  // Authentication Events
  static const String loginAttempt = 'login_attempt';
  static const String loginSuccess = 'login_success';
  static const String loginFailure = 'login_failure';
  static const String otpSent = 'otp_sent';
  static const String otpVerified = 'otp_verified';
  static const String otpFailed = 'otp_failed';

  // Profile Events
  static const String profileCompletionStarted = 'profile_completion_started';
  static const String profileCompletionCompleted = 'profile_completion_completed';
  static const String interestsSelected = 'interests_selected';
  static const String locationSet = 'location_set';
  static const String personalInfoSaved = 'personal_info_saved';

  // Navigation Events
  static const String screenTransition = 'screen_transition';
  static const String backButtonPressed = 'back_button_pressed';

  // User Actions
  static const String buttonPressed = 'button_pressed';
  static const String formSubmitted = 'form_submitted';
  static const String searchPerformed = 'search_performed';

  // App Events
  static const String appOpened = 'app_opened';
  static const String appBackgrounded = 'app_backgrounded';
  static const String appForegrounded = 'app_foregrounded';
}
