// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'district_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$districtServiceHash() => r'c7f328ad51f9483db67a9455c463924902bf7681';

/// District Service Provider
///
/// Copied from [districtService].
@ProviderFor(districtService)
final districtServiceProvider =
    Provider<district_service.DistrictService>.internal(
  districtService,
  name: r'districtServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$districtServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DistrictServiceRef = ProviderRef<district_service.DistrictService>;
String _$availableDistrictsHash() =>
    r'035eec024d3653094605d4b83b7651b58107ee5c';

/// Provider for available districts
///
/// Copied from [availableDistricts].
@ProviderFor(availableDistricts)
final availableDistrictsProvider =
    AutoDisposeProvider<List<district_service.District>>.internal(
  availableDistricts,
  name: r'availableDistrictsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$availableDistrictsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AvailableDistrictsRef
    = AutoDisposeProviderRef<List<district_service.District>>;
String _$selectedDistrictHash() => r'ef6f320aa79603012c7c7c8c8bcc862499b0bb90';

/// Provider for selected district
///
/// Copied from [selectedDistrict].
@ProviderFor(selectedDistrict)
final selectedDistrictProvider =
    AutoDisposeProvider<district_service.District?>.internal(
  selectedDistrict,
  name: r'selectedDistrictProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedDistrictHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SelectedDistrictRef
    = AutoDisposeProviderRef<district_service.District?>;
String _$isDistrictLoadingHash() => r'2f69b4ed965d78f549b47b0c8766ce8adb221ad1';

/// Provider for district loading state
///
/// Copied from [isDistrictLoading].
@ProviderFor(isDistrictLoading)
final isDistrictLoadingProvider = AutoDisposeProvider<bool>.internal(
  isDistrictLoading,
  name: r'isDistrictLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isDistrictLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsDistrictLoadingRef = AutoDisposeProviderRef<bool>;
String _$areDistrictsLoadedHash() =>
    r'96f49eadd72cd871bc066a6a01c9d16e5a6ebc14';

/// Provider for districts loaded state
///
/// Copied from [areDistrictsLoaded].
@ProviderFor(areDistrictsLoaded)
final areDistrictsLoadedProvider = AutoDisposeProvider<bool>.internal(
  areDistrictsLoaded,
  name: r'areDistrictsLoadedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$areDistrictsLoadedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AreDistrictsLoadedRef = AutoDisposeProviderRef<bool>;
String _$districtErrorMessageHash() =>
    r'52c630014c16d903cf90c1549b1c28d72657dc98';

/// Provider for district error message
///
/// Copied from [districtErrorMessage].
@ProviderFor(districtErrorMessage)
final districtErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  districtErrorMessage,
  name: r'districtErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$districtErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DistrictErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$districtHash() => r'5e9ce4b1472e325d3ba5f571196afda2c7f3055e';

/// District Notifier
///
/// Manages district state and business logic
///
/// Copied from [District].
@ProviderFor(District)
final districtProvider = NotifierProvider<District, DistrictState>.internal(
  District.new,
  name: r'districtProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$districtHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$District = Notifier<DistrictState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
