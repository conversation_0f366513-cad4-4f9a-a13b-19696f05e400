/// District Provider
///
/// Provides Riverpod state management for district functionality
/// Manages district fetching and selection state
library district_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/shared/services/district_service.dart'
    as district_service;

part 'district_provider.g.dart';

/// District State
///
/// Represents the state of district management
class DistrictState {
  /// List of available districts for current city
  final List<district_service.District> availableDistricts;

  /// Currently selected district
  final district_service.District? selectedDistrict;

  /// Whether districts are being loaded
  final bool isLoading;

  /// Whether districts have been loaded for current city
  final bool districtsLoaded;

  /// Current city key for which districts are loaded
  final String currentCityKey;

  /// Error message if any
  final String? errorMessage;

  const DistrictState({
    this.availableDistricts = const [],
    this.selectedDistrict,
    this.isLoading = false,
    this.districtsLoaded = false,
    this.currentCityKey = '',
    this.errorMessage,
  });

  DistrictState copyWith({
    List<district_service.District>? availableDistricts,
    district_service.District? selectedDistrict,
    bool? isLoading,
    bool? districtsLoaded,
    String? currentCityKey,
    String? errorMessage,
  }) {
    return DistrictState(
      availableDistricts: availableDistricts ?? this.availableDistricts,
      selectedDistrict: selectedDistrict ?? this.selectedDistrict,
      isLoading: isLoading ?? this.isLoading,
      districtsLoaded: districtsLoaded ?? this.districtsLoaded,
      currentCityKey: currentCityKey ?? this.currentCityKey,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  DistrictState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Reset state
  DistrictState reset() {
    return const DistrictState();
  }

  @override
  String toString() {
    return 'DistrictState(availableDistricts: ${availableDistricts.length}, '
        'selectedDistrict: $selectedDistrict, isLoading: $isLoading, '
        'districtsLoaded: $districtsLoaded, currentCityKey: $currentCityKey, '
        'errorMessage: $errorMessage)';
  }
}

/// District Service Provider
@Riverpod(keepAlive: true)
district_service.DistrictService districtService(DistrictServiceRef ref) {
  final firebaseService = ref.read(firebaseServiceProvider);
  return district_service.DistrictServiceImpl(firebaseService: firebaseService);
}

/// District Notifier
///
/// Manages district state and business logic
@Riverpod(keepAlive: true)
class District extends _$District {
  @override
  DistrictState build() {
    if (kDebugMode) {
      print('DistrictNotifier: Initialized');
    }
    return const DistrictState();
  }

  /// Load districts for a specific city
  ///
  /// @param cityKey The city key to load districts for
  Future<void> loadDistrictsForCity(String cityKey) async {
    if (cityKey.isEmpty) {
      if (kDebugMode) {
        print('DistrictNotifier: Empty city key provided');
      }
      return;
    }

    // If districts are already loaded for this city, don't reload
    if (state.districtsLoaded && state.currentCityKey == cityKey) {
      if (kDebugMode) {
        print('DistrictNotifier: Districts already loaded for city: $cityKey');
      }
      return;
    }

    if (kDebugMode) {
      print('DistrictNotifier: Loading districts for city: $cityKey');
    }

    try {
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
        currentCityKey: cityKey,
      );

      final districtService = ref.read(districtServiceProvider);
      final districts = await districtService.getDistrictsForCity(cityKey);

      state = state.copyWith(
        availableDistricts: districts,
        isLoading: false,
        districtsLoaded: true,
        selectedDistrict: null, // Reset selection when loading new city
      );

      if (kDebugMode) {
        print(
            'DistrictNotifier: Loaded ${districts.length} districts for city: $cityKey');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load districts: $e',
      );

      if (kDebugMode) {
        print(
            'DistrictNotifier: Error loading districts for city "$cityKey" - $e');
      }
    }
  }

  /// Select a district
  ///
  /// @param district The district to select
  void selectDistrict(district_service.District district) {
    if (kDebugMode) {
      print('DistrictNotifier: Selecting district: ${district.key}');
    }

    state = state.copyWith(selectedDistrict: district);
  }

  /// Clear selected district
  void clearSelectedDistrict() {
    if (kDebugMode) {
      print('DistrictNotifier: Clearing selected district');
    }

    state = state.copyWith(selectedDistrict: null);
  }

  /// Check if a city has districts available
  ///
  /// @param cityKey The city key to check
  /// @return Future that resolves to true if city has districts
  Future<bool> hasCityDistricts(String cityKey) async {
    try {
      final districtService = ref.read(districtServiceProvider);
      return await districtService.hasCityDistricts(cityKey);
    } catch (e) {
      if (kDebugMode) {
        print('DistrictNotifier: Error checking if city has districts - $e');
      }
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Reset state
  void reset() {
    state = state.reset();

    if (kDebugMode) {
      print('DistrictNotifier: State reset');
    }
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for available districts
@riverpod
List<district_service.District> availableDistricts(AvailableDistrictsRef ref) {
  return ref.watch(districtProvider).availableDistricts;
}

/// Provider for selected district
@riverpod
district_service.District? selectedDistrict(SelectedDistrictRef ref) {
  return ref.watch(districtProvider).selectedDistrict;
}

/// Provider for district loading state
@riverpod
bool isDistrictLoading(IsDistrictLoadingRef ref) {
  return ref.watch(districtProvider).isLoading;
}

/// Provider for districts loaded state
@riverpod
bool areDistrictsLoaded(AreDistrictsLoadedRef ref) {
  return ref.watch(districtProvider).districtsLoaded;
}

/// Provider for district error message
@riverpod
String? districtErrorMessage(DistrictErrorMessageRef ref) {
  return ref.watch(districtProvider).errorMessage;
}
