// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'city_validation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cityValidationServiceHash() =>
    r'11ca767aad3089e37425d0377791e44146982133';

/// City Validation Service Provider
///
/// Provides the city validation service instance
///
/// Copied from [cityValidationService].
@ProviderFor(cityValidationService)
final cityValidationServiceProvider =
    AutoDisposeProvider<CityValidationService>.internal(
  cityValidationService,
  name: r'cityValidationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cityValidationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CityValidationServiceRef
    = AutoDisposeProviderRef<CityValidationService>;
String _$isValidatingCityHash() => r'4a43eb9c4f40e75fbd032d57a077718d13d1bc32';

/// Convenience providers for specific state values
///
/// Copied from [isValidatingCity].
@ProviderFor(isValidatingCity)
final isValidatingCityProvider = AutoDisposeProvider<bool>.internal(
  isValidatingCity,
  name: r'isValidatingCityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isValidatingCityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsValidatingCityRef = AutoDisposeProviderRef<bool>;
String _$lastValidationResultHash() =>
    r'dc52c269c520adfd2143841c5c4668813655faa9';

/// See also [lastValidationResult].
@ProviderFor(lastValidationResult)
final lastValidationResultProvider =
    AutoDisposeProvider<CityValidationResult?>.internal(
  lastValidationResult,
  name: r'lastValidationResultProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$lastValidationResultHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef LastValidationResultRef = AutoDisposeProviderRef<CityValidationResult?>;
String _$supportedCitiesHash() => r'a973dcd245147b3393720de7222e92efefe787b4';

/// See also [supportedCities].
@ProviderFor(supportedCities)
final supportedCitiesProvider = AutoDisposeProvider<List<String>>.internal(
  supportedCities,
  name: r'supportedCitiesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$supportedCitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SupportedCitiesRef = AutoDisposeProviderRef<List<String>>;
String _$areCitiesLoadedHash() => r'd6fce8f3cf7529fb640c690fcb5bff95f13bf9bb';

/// See also [areCitiesLoaded].
@ProviderFor(areCitiesLoaded)
final areCitiesLoadedProvider = AutoDisposeProvider<bool>.internal(
  areCitiesLoaded,
  name: r'areCitiesLoadedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$areCitiesLoadedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AreCitiesLoadedRef = AutoDisposeProviderRef<bool>;
String _$cityValidationHash() => r'9ecf56f40427640192664f1d2a4ec5cad92a238d';

/// City Validation Notifier
///
/// Manages city validation state and business logic
///
/// Copied from [CityValidation].
@ProviderFor(CityValidation)
final cityValidationProvider =
    AutoDisposeNotifierProvider<CityValidation, CityValidationState>.internal(
  CityValidation.new,
  name: r'cityValidationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cityValidationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CityValidation = AutoDisposeNotifier<CityValidationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
