/// City Validation Provider
///
/// Riverpod provider for city validation service
/// Manages city validation state and operations
library city_validation_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/shared/services/city_validation_service.dart';

part 'city_validation_provider.g.dart';

/// City Validation Service Provider
///
/// Provides the city validation service instance
@riverpod
CityValidationService cityValidationService(CityValidationServiceRef ref) {
  final firebaseService = ref.read(firebaseServiceProvider);
  return CityValidationServiceImpl(firebaseService);
}

/// City Validation State
///
/// Represents the state of city validation operations
class CityValidationState {
  /// Whether a validation check is in progress
  final bool isValidating;

  /// The last validation result
  final CityValidationResult? lastResult;

  /// List of supported cities (cached)
  final List<String> supportedCities;

  /// Whether supported cities have been loaded
  final bool citiesLoaded;

  /// Error message if any
  final String? errorMessage;

  /// Creates a CityValidationState
  ///
  /// @param isValidating Whether validation is in progress
  /// @param lastResult Last validation result
  /// @param supportedCities List of supported cities
  /// @param citiesLoaded Whether cities have been loaded
  /// @param errorMessage Error message if any
  const CityValidationState({
    this.isValidating = false,
    this.lastResult,
    this.supportedCities = const [],
    this.citiesLoaded = false,
    this.errorMessage,
  });

  /// Creates a copy of this state with updated values
  ///
  /// @param isValidating New validation status
  /// @param lastResult New validation result
  /// @param supportedCities New supported cities list
  /// @param citiesLoaded New cities loaded status
  /// @param errorMessage New error message
  /// @return Updated CityValidationState
  CityValidationState copyWith({
    bool? isValidating,
    CityValidationResult? lastResult,
    List<String>? supportedCities,
    bool? citiesLoaded,
    String? errorMessage,
  }) {
    return CityValidationState(
      isValidating: isValidating ?? this.isValidating,
      lastResult: lastResult ?? this.lastResult,
      supportedCities: supportedCities ?? this.supportedCities,
      citiesLoaded: citiesLoaded ?? this.citiesLoaded,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  CityValidationState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// City Validation Notifier
///
/// Manages city validation state and business logic
@riverpod
class CityValidation extends _$CityValidation {
  @override
  CityValidationState build() {
    if (kDebugMode) {
      print('CityValidationNotifier: Initialized');
    }
    return const CityValidationState();
  }

  /// Validate if a city is supported
  ///
  /// @param city The city name to validate
  /// @return Future that resolves to validation result
  Future<CityValidationResult> validateCity(String city) async {
    if (kDebugMode) {
      print('CityValidationNotifier: Validating city: $city');
    }

    try {
      state = state.copyWith(isValidating: true, errorMessage: null);

      final cityValidationService = ref.read(cityValidationServiceProvider);
      final isSupported = await cityValidationService.isCitySupported(city);

      final result = isSupported
          ? CityValidationResult.supported(city)
          : CityValidationResult.unsupported(city);

      state = state.copyWith(
        isValidating: false,
        lastResult: result,
      );

      if (kDebugMode) {
        print(
            'CityValidationNotifier: City "$city" is ${isSupported ? "SUPPORTED" : "NOT SUPPORTED"}');
      }

      return result;
    } catch (e) {
      final result = CityValidationResult.error(e.toString());

      state = state.copyWith(
        isValidating: false,
        lastResult: result,
        errorMessage: 'Failed to validate city: $e',
      );

      if (kDebugMode) {
        print('CityValidationNotifier: Error validating city - $e');
      }

      return result;
    }
  }

  /// Load supported cities from Firestore
  ///
  /// @return Future that resolves when cities are loaded
  Future<void> loadSupportedCities() async {
    if (state.citiesLoaded) {
      if (kDebugMode) {
        print('CityValidationNotifier: Cities already loaded');
      }
      return;
    }

    if (kDebugMode) {
      print('CityValidationNotifier: Loading supported cities');
    }

    try {
      state = state.copyWith(isValidating: true, errorMessage: null);

      final cityValidationService = ref.read(cityValidationServiceProvider);
      final cities = await cityValidationService.getSupportedCities();

      state = state.copyWith(
        isValidating: false,
        supportedCities: cities,
        citiesLoaded: true,
      );

      if (kDebugMode) {
        print(
            'CityValidationNotifier: Loaded ${cities.length} supported cities');
      }
    } catch (e) {
      state = state.copyWith(
        isValidating: false,
        errorMessage: 'Failed to load supported cities: $e',
      );

      if (kDebugMode) {
        print('CityValidationNotifier: Error loading supported cities - $e');
      }
    }
  }

  /// Check if a city is in the cached supported cities list
  ///
  /// @param city The city name to check
  /// @return True if city is supported (based on cached data)
  bool isCitySupportedCached(String city) {
    if (!state.citiesLoaded) {
      return false;
    }

    final normalizedCity = city.toLowerCase().trim();
    return state.supportedCities
        .map((c) => c.toLowerCase().trim())
        .contains(normalizedCity);
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Reset state
  void reset() {
    state = const CityValidationState();

    if (kDebugMode) {
      print('CityValidationNotifier: State reset');
    }
  }
}

/// Convenience providers for specific state values
@riverpod
bool isValidatingCity(IsValidatingCityRef ref) {
  return ref.watch(cityValidationProvider).isValidating;
}

@riverpod
CityValidationResult? lastValidationResult(LastValidationResultRef ref) {
  return ref.watch(cityValidationProvider).lastResult;
}

@riverpod
List<String> supportedCities(SupportedCitiesRef ref) {
  return ref.watch(cityValidationProvider).supportedCities;
}

@riverpod
bool areCitiesLoaded(AreCitiesLoadedRef ref) {
  return ref.watch(cityValidationProvider).citiesLoaded;
}
