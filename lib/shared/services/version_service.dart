/// Version Service
///
/// Provides access to application version information
/// <PERSON>les retrieval and caching of app version data
library version_service;

import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Service for managing application version information
///
/// Provides methods to retrieve app version and other
/// package information using the package_info_plus plugin
class VersionService {
  PackageInfo? _packageInfo;

  /// Get the build number
  ///
  /// Returns the build number or empty string if not initialized
  String get buildNumber {
    return _packageInfo?.buildNumber ?? '';
  }

  /// Initialize the version service
  ///
  /// Loads package information from the platform
  /// Should be called during app initialization
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('VersionService: Initializing...');
      }

      _packageInfo = await PackageInfo.fromPlatform();

      if (kDebugMode) {
        print('VersionService: Initialized successfully');
        print('  App Name: $appName');
        print('  Package Name: $packageName');
        print('  Version: $appVersion');
        print('  Build Number: $buildNumber');
      }
    } catch (e) {
      if (kDebugMode) {
        print('VersionService: Error during initialization - $e');
      }
      rethrow;
    }
  }

  /// Get the app version string
  ///
  /// Returns the version in format "1.0.0" or empty string if not initialized
  String get appVersion {
    return _packageInfo?.version ?? '';
  }

  /// Get the full version string
  ///
  /// Returns version with build number if available
  String get fullVersion {
    if (_packageInfo == null) return '';

    final version = _packageInfo!.version;
    final build = _packageInfo!.buildNumber;

    if (build.isNotEmpty) {
      return '$version ($build)';
    }

    return version;
  }

  /// Get the app name
  ///
  /// Returns the application name or empty string if not initialized
  String get appName {
    return _packageInfo?.appName ?? '';
  }

  /// Get the package name
  ///
  /// Returns the package/bundle identifier or empty string if not initialized
  String get packageName {
    return _packageInfo?.packageName ?? '';
  }

  /// Check if the service is initialized
  ///
  /// Returns true if package info has been loaded
  bool get isInitialized {
    return _packageInfo != null;
  }

  /// Get version for comparison
  ///
  /// Returns the version string suitable for version comparison
  /// Ensures the service is initialized before returning version
  String getVersionForComparison() {
    if (!isInitialized) {
      if (kDebugMode) {
        print(
            'VersionService: Warning - Service not initialized, returning default version');
      }
      return '1.0.0'; // Default version if not initialized
    }

    return appVersion;
  }
}
