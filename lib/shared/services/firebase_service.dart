/// Firebase Service
///
/// Provides methods for interacting with Firebase services
/// Centralizes Firebase access and operations
///
/// This service handles Firestore and Messaging
library firebase_service;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

/// Firebase Service Interface
///
/// Defines the contract for Firebase operations
/// Allows for easy mocking in tests
abstract class FirebaseService {
  /// Get the Firestore instance
  FirebaseFirestore get firestore;

  /// Get the Firebase Messaging instance
  FirebaseMessaging get messaging;

  /// Get a document from Firestore
  ///
  /// @param collection The collection name
  /// @param documentId The document ID
  /// @return A Future that resolves to the document snapshot
  Future<DocumentSnapshot> getDocument(String collection, String documentId);

  /// Set a document in Firestore
  ///
  /// @param collection The collection name
  /// @param documentId The document ID
  /// @param data The document data
  /// @return A Future that completes when the operation is done
  Future<void> setDocument(
      String collection, String documentId, Map<String, dynamic> data);

  /// Update a document in Firestore
  ///
  /// @param collection The collection name
  /// @param documentId The document ID
  /// @param data The document data to update
  /// @return A Future that completes when the operation is done
  Future<void> updateDocument(
      String collection, String documentId, Map<String, dynamic> data);

  /// Delete a document from Firestore
  ///
  /// @param collection The collection name
  /// @param documentId The document ID
  /// @return A Future that completes when the operation is done
  Future<void> deleteDocument(String collection, String documentId);

  /// Get a collection from Firestore
  ///
  /// @param collection The collection name
  /// @return A CollectionReference for the specified collection
  CollectionReference getCollection(String collection);

  /// Get the current user (dummy implementation)
  ///
  /// @return Always returns null since Firebase Auth is not used
  dynamic getCurrentUser();

  /// Sign out the current user (dummy implementation)
  ///
  /// @return A Future that completes immediately
  Future<void> signOut();

  /// Get the Firebase token for the current device
  ///
  /// @return A Future that resolves to the token string
  Future<String?> getToken();
}

/// Firebase Service Implementation
///
/// Implements the FirebaseService interface using Firebase SDKs
class FirebaseServiceImpl implements FirebaseService {
  final FirebaseFirestore _firestore;
  final FirebaseMessaging _messaging;

  /// Constructor that takes Firebase instances
  ///
  /// @param firestore The FirebaseFirestore instance
  /// @param messaging The FirebaseMessaging instance
  FirebaseServiceImpl({
    required FirebaseFirestore firestore,
    required FirebaseMessaging messaging,
  })  : _firestore = firestore,
        _messaging = messaging;

  @override
  FirebaseFirestore get firestore => _firestore;

  @override
  FirebaseMessaging get messaging => _messaging;

  @override
  Future<DocumentSnapshot> getDocument(
      String collection, String documentId) async {
    try {
      return await _firestore.collection(collection).doc(documentId).get();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting document: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> setDocument(
      String collection, String documentId, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(documentId).set(data);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting document: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> updateDocument(
      String collection, String documentId, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(documentId).update(data);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating document: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> deleteDocument(String collection, String documentId) async {
    try {
      await _firestore.collection(collection).doc(documentId).delete();
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting document: $e');
      }
      rethrow;
    }
  }

  @override
  CollectionReference getCollection(String collection) {
    return _firestore.collection(collection);
  }

  @override
  dynamic getCurrentUser() {
    // Return null since we're not using Firebase Auth
    return null;
  }

  @override
  Future<void> signOut() async {
    // No-op implementation since we're not using Firebase Auth
    if (kDebugMode) {
      print('Sign out called (no-op implementation)');
    }
    return;
  }

  @override
  Future<String?> getToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting token: $e');
      }
      return null;
    }
  }
}
