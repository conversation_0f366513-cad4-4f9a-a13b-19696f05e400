/// Storage Service
///
/// Provides methods for storing and retrieving persistent data
/// Used for saving user preferences, login state, and other app settings
///
/// This service uses GetStorage to store data locally on the device
library storage_service;

import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';

/// Storage keys used throughout the application
class StorageKeys {
  static const String loggedIn = 'loggedIn';
  static const String userID = 'userID';
  static const String langCode = 'lang_code';
  static const String isFirstTime = 'isFirstTime';
  static const String termsAccepted = 'termsAccepted';
  static const String optionalUpdateShown = 'optional_update_shown';
  static const String appSessionId = 'app_session_id';
}

/// Storage Service Interface
///
/// Defines the contract for storage operations
/// Allows for easy mocking in tests
abstract class StorageService {
  /// Saves user login data to storage
  ///
  /// Sets the logged-in status to true and saves the user ID
  ///
  /// @param userID The ID of the logged-in user
  void setLoginData(String userID);

  /// Retrieves the user's logged-in status
  ///
  /// @return True if the user is logged in, false otherwise
  bool getLoggedInStatus();

  /// Retrieves the user ID value from storage
  ///
  /// @return The stored user ID, or an empty string if none is stored
  String getUserIDValue();

  /// Saves the selected language code to storage
  ///
  /// @param langCode The language code to save (e.g., "en", "ar")
  void setLanguage(String langCode);

  /// Retrieves the saved language code
  ///
  /// @return The saved language code, or "ar" (Arabic) if none is saved
  String getLanguage();

  /// Clears user data from storage
  ///
  /// Used during logout or account deletion
  /// Preserves terms acceptance state
  ///
  /// @return A Future that completes when all data has been cleared
  Future<void> clearUserData();

  /// Sets the first-time app launch flag to false
  ///
  /// Used to track whether this is the first time the app has been launched
  /// Typically called after onboarding or initial setup
  void setFirstTime();

  /// Checks if this is the first time the app has been launched
  ///
  /// @return True if this is the first launch, false otherwise
  bool getFirstTime();

  /// Saves the terms and conditions acceptance state
  ///
  /// @param accepted Whether the user has accepted the terms and conditions
  Future<void> setTermsAccepted(bool accepted);

  /// Retrieves the terms and conditions acceptance state
  ///
  /// @return True if the user has accepted the terms, false otherwise
  bool getTermsAccepted();

  /// Generic read method for any key
  ///
  /// @param key The key to read from storage
  /// @return The value associated with the key, or null if not found
  T? read<T>(String key);

  /// Generic write method for any key-value pair
  ///
  /// @param key The key to store the value under
  /// @param value The value to store
  /// @return A Future that completes when the value is written
  Future<void> write<T>(String key, T value);

  /// Set that optional update dialog has been shown for current session
  ///
  /// @param version The version for which the dialog was shown
  Future<void> setOptionalUpdateShown(String version);

  /// Check if optional update dialog has been shown for current session
  ///
  /// @param version The version to check
  /// @return True if dialog has been shown for this version
  bool hasOptionalUpdateBeenShown(String version);

  /// Clear optional update shown status (typically on app restart)
  Future<void> clearOptionalUpdateShown();

  /// Initialize a new app session
  ///
  /// Generates a new session ID and clears optional update flags if it's a new session
  Future<void> initializeAppSession();

  /// Check if this is a new app session
  ///
  /// @return True if this is a new session (app was completely closed and reopened)
  bool isNewAppSession();

  /// Clear the current app session (typically when app is closed)
  Future<void> clearAppSession();

  /// Remove a specific key from storage
  ///
  /// @param key The key to remove
  /// @return A Future that completes when the key is removed
  Future<void> remove(String key);
}

/// Storage Service Implementation
///
/// Implements the StorageService interface using GetStorage
class StorageServiceImpl implements StorageService {
  /// The GetStorage instance used for all storage operations
  final GetStorage _storage;

  /// Constructor that takes a GetStorage instance
  ///
  /// @param storage The GetStorage instance to use
  StorageServiceImpl(this._storage);

  @override
  void setLoginData(String userID) {
    _storage.write(StorageKeys.loggedIn, true);
    _storage.write(StorageKeys.userID, userID);
  }

  @override
  bool getLoggedInStatus() {
    return _storage.read(StorageKeys.loggedIn) ?? false;
  }

  @override
  String getUserIDValue() {
    return _storage.read(StorageKeys.userID) ?? '';
  }

  @override
  void setLanguage(String langCode) {
    _storage.write(StorageKeys.langCode, langCode);
  }

  @override
  String getLanguage() {
    return _storage.read(StorageKeys.langCode) ?? "ar";
  }

  @override
  Future<void> clearUserData() async {
    // Save the terms acceptance state before clearing
    bool termsAccepted = getTermsAccepted();
    if (kDebugMode) {
      print('Preserving terms acceptance state during logout: $termsAccepted');
    }

    // Clear all storage
    await _storage.erase();

    // Restore the terms acceptance state
    await setTermsAccepted(termsAccepted);
  }

  @override
  void setFirstTime() {
    _storage.write(StorageKeys.isFirstTime, false);
  }

  @override
  bool getFirstTime() {
    return _storage.read(StorageKeys.isFirstTime) ?? true;
  }

  @override
  Future<void> setTermsAccepted(bool accepted) async {
    // Write the value to storage
    await _storage.write(StorageKeys.termsAccepted, accepted);

    // Force a save to disk to ensure persistence
    await _storage.save();

    if (kDebugMode) {
      print('StorageService: Saved terms acceptance state: $accepted');
      print(
          'StorageService: Verification read: ${_storage.read(StorageKeys.termsAccepted)}');
    }
  }

  @override
  bool getTermsAccepted() {
    bool result = _storage.read(StorageKeys.termsAccepted) ?? false;
    if (kDebugMode) {
      print('StorageService: Retrieved terms acceptance state: $result');
    }
    return result;
  }

  @override
  T? read<T>(String key) {
    return _storage.read<T>(key);
  }

  @override
  Future<void> write<T>(String key, T value) async {
    await _storage.write(key, value);
  }

  @override
  Future<void> remove(String key) async {
    await _storage.remove(key);
  }

  @override
  Future<void> setOptionalUpdateShown(String version) async {
    if (kDebugMode) {
      print(
          'StorageService: Marking optional update as shown for version: $version');
    }
    await _storage.write('${StorageKeys.optionalUpdateShown}_$version', true);
  }

  @override
  bool hasOptionalUpdateBeenShown(String version) {
    final result =
        _storage.read<bool>('${StorageKeys.optionalUpdateShown}_$version') ??
            false;
    if (kDebugMode) {
      print(
          'StorageService: Optional update shown status for version $version: $result');
    }
    return result;
  }

  @override
  Future<void> clearOptionalUpdateShown() async {
    if (kDebugMode) {
      print('StorageService: Clearing all optional update shown flags');
    }

    // Get all keys and convert to list to avoid concurrent modification
    final allKeys = _storage.getKeys();
    final optionalUpdateKeys = allKeys
        .where(
            (key) => key.toString().startsWith(StorageKeys.optionalUpdateShown))
        .toList(); // Convert to list to avoid concurrent modification

    for (final key in optionalUpdateKeys) {
      await _storage.remove(key);
    }
  }

  @override
  Future<void> initializeAppSession() async {
    // Check if this is the first time the app is launched in this session
    final hasSessionId =
        _storage.read<String>(StorageKeys.appSessionId) != null;

    if (!hasSessionId) {
      // This is a new session (app was completely closed and reopened)
      if (kDebugMode) {
        print(
            'StorageService: New app session detected, clearing optional update flags');
      }

      // Clear optional update flags for new session
      await clearOptionalUpdateShown();

      // Generate and store a session ID to mark this session as initialized
      final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
      await _storage.write(StorageKeys.appSessionId, sessionId);

      if (kDebugMode) {
        print('StorageService: App session initialized with ID: $sessionId');
      }
    } else {
      if (kDebugMode) {
        print('StorageService: Continuing existing app session');
      }
    }
  }

  @override
  bool isNewAppSession() {
    final currentSessionId = _storage.read<String>(StorageKeys.appSessionId);
    final result = currentSessionId == null;

    if (kDebugMode) {
      print('StorageService: Checking if new session - Result: $result');
    }

    return result;
  }

  @override
  Future<void> clearAppSession() async {
    if (kDebugMode) {
      print('StorageService: Clearing app session');
    }

    await _storage.remove(StorageKeys.appSessionId);
  }
}
