/// City Validation Service
///
/// Service for validating if a user's city is supported by the application
/// Checks against Firestore app_settings collection supported_cities document
library city_validation_service;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// City Validation Service Interface
///
/// Defines the contract for city validation operations
/// Allows for easy mocking in tests
abstract class CityValidationService {
  /// Check if a city is supported
  ///
  /// Validates if the given city is in the list of supported cities
  ///
  /// @param city The city name to validate
  /// @return A Future that resolves to true if city is supported, false otherwise
  Future<bool> isCitySupported(String city);

  /// Get list of supported cities
  ///
  /// Retrieves the complete list of supported cities from Firestore
  ///
  /// @return A Future that resolves to a list of supported city names
  Future<List<String>> getSupportedCities();
}

/// City Validation Service Implementation
///
/// Implements city validation using Firebase Firestore
/// Checks against app_settings/supported_cities document
/// Supports both new map structure (SA -> { cityKey -> { name_ar, name_en } })
/// and legacy array structure for backward compatibility
class CityValidationServiceImpl implements CityValidationService {
  /// Firebase service for Firestore operations
  final FirebaseService _firebaseService;

  /// Collection name in Firestore
  static const String _collectionName = 'app_settings';

  /// Document ID for supported cities
  static const String _documentId = 'supported_cities';

  /// Field name for Saudi Arabia cities map
  static const String _citiesField = 'SA';

  /// Creates a CityValidationServiceImpl
  ///
  /// @param firebaseService Firebase service instance
  const CityValidationServiceImpl(this._firebaseService);

  @override
  Future<bool> isCitySupported(String city) async {
    try {
      if (city.isEmpty) {
        if (kDebugMode) {
          print('CityValidationService: Empty city provided');
        }
        return false;
      }

      final supportedCities = await getSupportedCities();

      // Case-insensitive comparison
      final normalizedCity = city.toLowerCase().trim();
      final isSupported = supportedCities
          .map((c) => c.toLowerCase().trim())
          .contains(normalizedCity);

      if (kDebugMode) {
        print(
            'CityValidationService: City "$city" is ${isSupported ? 'supported' : 'not supported'}');
      }

      return isSupported;
    } catch (e) {
      if (kDebugMode) {
        print('CityValidationService: Error checking city support - $e');
      }
      // Return false on error to be safe
      return false;
    }
  }

  @override
  Future<List<String>> getSupportedCities() async {
    try {
      if (kDebugMode) {
        print(
            'CityValidationService: Fetching supported cities from Firestore');
      }

      final doc = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      if (doc.exists && doc.data() != null) {
        final data = doc.data() as Map<String, dynamic>;

        if (data.containsKey(_citiesField)) {
          final citiesData = data[_citiesField];

          // Handle new map structure: SA -> { cityKey -> { name_ar, name_en } }
          if (citiesData is Map<String, dynamic>) {
            final cities = <String>[];

            for (final cityEntry in citiesData.values) {
              if (cityEntry is Map<String, dynamic>) {
                // Add Arabic name if available
                if (cityEntry.containsKey('name_ar') &&
                    cityEntry['name_ar'] != null &&
                    cityEntry['name_ar'].toString().isNotEmpty) {
                  cities.add(cityEntry['name_ar'].toString());
                }

                // Add English name if available
                if (cityEntry.containsKey('name_en') &&
                    cityEntry['name_en'] != null &&
                    cityEntry['name_en'].toString().isNotEmpty) {
                  cities.add(cityEntry['name_en'].toString());
                }
              }
            }

            if (kDebugMode) {
              print(
                  'CityValidationService: Found ${cities.length} supported cities from map structure');
            }

            return cities;
          }

          // Fallback: Handle legacy array structure for backward compatibility
          else if (citiesData is List) {
            final cities = citiesData
                .map((city) => city.toString())
                .where((city) => city.isNotEmpty)
                .toList();

            if (kDebugMode) {
              print(
                  'CityValidationService: Found ${cities.length} supported cities from legacy array structure');
            }

            return cities;
          }
        }
      }

      if (kDebugMode) {
        print('CityValidationService: No supported cities found in Firestore');
      }

      // Return empty list if no data found
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('CityValidationService: Error fetching supported cities - $e');
      }

      // Return empty list on error
      return [];
    }
  }
}

/// City Validation Result
///
/// Represents the result of a city validation check
class CityValidationResult {
  /// Whether the city is supported
  final bool isSupported;

  /// Message explaining the validation result
  final String message;

  /// Creates a CityValidationResult
  ///
  /// @param isSupported Whether the city is supported
  /// @param message Explanation message
  const CityValidationResult({
    required this.isSupported,
    required this.message,
  });

  /// Creates a successful validation result
  ///
  /// @param city The supported city name
  /// @return CityValidationResult indicating success
  factory CityValidationResult.supported(String city) {
    return CityValidationResult(
      isSupported: true,
      message: 'City "$city" is supported',
    );
  }

  /// Creates a failed validation result
  ///
  /// @param city The unsupported city name
  /// @return CityValidationResult indicating failure
  factory CityValidationResult.unsupported(String city) {
    return const CityValidationResult(
      isSupported: false,
      message:
          'The region is not supported now, we will let you know once it is supported.',
    );
  }

  /// Creates an error validation result
  ///
  /// @param error The error message
  /// @return CityValidationResult indicating error
  factory CityValidationResult.error(String error) {
    return CityValidationResult(
      isSupported: false,
      message: 'Unable to validate city: $error',
    );
  }
}
