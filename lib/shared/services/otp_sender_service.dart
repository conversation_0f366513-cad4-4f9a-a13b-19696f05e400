/// OTP Sender Service
///
/// Service for fetching OTP sender configuration from Firestore
/// Retrieves sender name from app_settings collection other document
library otp_sender_service;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// OTP Sender Service Interface
///
/// Defines the contract for OTP sender configuration operations
/// Allows for easy mocking in tests
abstract class OtpSenderService {
  /// Get OTP sender name from Firestore
  ///
  /// Retrieves the OTP sender name from app_settings/other document
  ///
  /// @return A Future that resolves to the sender name
  Future<String> getOtpSender();
}

/// OTP Sender Service Implementation
///
/// Implements OTP sender configuration fetching using Firebase Firestore
/// Retrieves sender name from app_settings/other document otp_sender field
class OtpSenderServiceImpl implements OtpSenderService {
  /// Firebase service for Firestore operations
  final FirebaseService _firebaseService;

  /// Collection name in Firestore
  static const String _collectionName = 'app_settings';

  /// Document ID for other settings
  static const String _documentId = 'other';

  /// Field name for OTP sender
  static const String _otpSenderField = 'otp_sender';

  /// Default OTP sender name (fallback)
  static const String _defaultOtpSender = 'ASHKAL';

  /// Creates an OtpSenderServiceImpl
  ///
  /// @param firebaseService Firebase service instance
  const OtpSenderServiceImpl(this._firebaseService);

  @override
  Future<String> getOtpSender() async {
    try {
      if (kDebugMode) {
        print('OtpSenderService: Fetching OTP sender from Firestore');
      }

      final doc = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      if (!doc.exists || doc.data() == null) {
        if (kDebugMode) {
          print('OtpSenderService: Other settings document not found, using default');
        }
        return _defaultOtpSender;
      }

      final data = doc.data() as Map<String, dynamic>;

      if (!data.containsKey(_otpSenderField)) {
        if (kDebugMode) {
          print('OtpSenderService: OTP sender field not found, using default');
        }
        return _defaultOtpSender;
      }

      final otpSender = data[_otpSenderField]?.toString().trim() ?? '';

      if (otpSender.isEmpty) {
        if (kDebugMode) {
          print('OtpSenderService: OTP sender field is empty, using default');
        }
        return _defaultOtpSender;
      }

      if (kDebugMode) {
        print('OtpSenderService: Retrieved OTP sender: $otpSender');
      }

      return otpSender;
    } catch (e) {
      if (kDebugMode) {
        print('OtpSenderService: Error fetching OTP sender - $e');
      }
      // Return default on error to ensure app functionality
      return _defaultOtpSender;
    }
  }

  /// Test connection to Firestore
  ///
  /// Verifies that the Firestore connection is working
  /// and the other settings document exists
  ///
  /// @return A Future that resolves to true if connection is successful
  Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('OtpSenderService: Testing Firestore connection');
      }

      final doc = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      final isConnected = doc.exists;

      if (kDebugMode) {
        print('OtpSenderService: Connection test result: $isConnected');
      }

      return isConnected;
    } catch (e) {
      if (kDebugMode) {
        print('OtpSenderService: Connection test failed - $e');
      }
      return false;
    }
  }

  /// Get raw other settings data
  ///
  /// Retrieves the raw document data without parsing
  /// Useful for debugging and testing
  ///
  /// @return A Future that resolves to raw document data
  Future<Map<String, dynamic>?> getRawOtherSettingsData() async {
    try {
      if (kDebugMode) {
        print('OtpSenderService: Fetching raw other settings data');
      }

      final doc = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      if (!doc.exists) {
        if (kDebugMode) {
          print('OtpSenderService: Other settings document does not exist');
        }
        return null;
      }

      final data = doc.data() as Map<String, dynamic>?;

      if (kDebugMode) {
        print('OtpSenderService: Raw other settings data: $data');
      }

      return data;
    } catch (e) {
      if (kDebugMode) {
        print('OtpSenderService: Error fetching raw data - $e');
      }
      return null;
    }
  }
}
