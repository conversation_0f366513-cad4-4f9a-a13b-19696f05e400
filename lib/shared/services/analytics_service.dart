/// Analytics Service
///
/// Provides methods for tracking user interactions and screen views
/// Centralizes Firebase Analytics operations
///
/// This service handles screen tracking, event logging, and user properties
library analytics_service;

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

/// Analytics Service Interface
///
/// Defines the contract for analytics operations
/// Allows for easy mocking in tests
abstract class AnalyticsService {
  /// Get the Firebase Analytics instance
  FirebaseAnalytics get analytics;

  /// Log a screen view
  ///
  /// @param screenName The name of the screen
  /// @param screenClass The class name of the screen (optional)
  /// @param parameters Additional parameters (optional)
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  });

  /// Log a custom event
  ///
  /// @param name The event name
  /// @param parameters Event parameters (optional)
  Future<void> logEvent({
    required String name,
    Map<String, Object?>? parameters,
  });

  /// Log user login event
  ///
  /// @param loginMethod The method used to login (e.g., 'mobile_otp')
  Future<void> logLogin(String loginMethod);

  /// Log user signup event
  ///
  /// @param signUpMethod The method used to sign up (e.g., 'mobile_otp')
  Future<void> logSignUp(String signUpMethod);

  /// Set user ID for analytics
  ///
  /// @param userId The user ID to set
  Future<void> setUserId(String? userId);

  /// Set user property
  ///
  /// @param name The property name
  /// @param value The property value
  Future<void> setUserProperty({
    required String name,
    required String? value,
  });

  /// Log app open event
  Future<void> logAppOpen();

  /// Log profile completion step
  ///
  /// @param step The completion step (e.g., 'interests', 'location', 'personal_info')
  Future<void> logProfileCompletionStep(String step);

  /// Log button click event
  ///
  /// @param buttonName The name of the button clicked
  /// @param screenName The screen where the button was clicked
  Future<void> logButtonClick({
    required String buttonName,
    required String screenName,
  });

  /// Log navigation event
  ///
  /// @param from The source screen
  /// @param to The destination screen
  Future<void> logNavigation({
    required String from,
    required String to,
  });
}

/// Analytics Service Implementation
///
/// Implements the AnalyticsService interface using Firebase Analytics SDK
class AnalyticsServiceImpl implements AnalyticsService {
  final FirebaseAnalytics _analytics;

  /// Constructor that takes Firebase Analytics instance
  ///
  /// @param analytics The FirebaseAnalytics instance
  AnalyticsServiceImpl({
    required FirebaseAnalytics analytics,
  }) : _analytics = analytics;

  @override
  FirebaseAnalytics get analytics => _analytics;

  @override
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
        parameters: parameters?.cast<String, Object>(),
      );

      if (kDebugMode) {
        print('Analytics: Screen view logged - $screenName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging screen view - $e');
      }
    }
  }

  @override
  Future<void> logEvent({
    required String name,
    Map<String, Object?>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: name,
        parameters: parameters?.cast<String, Object>(),
      );

      if (kDebugMode) {
        print('Analytics: Event logged - $name');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging event - $e');
      }
    }
  }

  @override
  Future<void> logLogin(String loginMethod) async {
    try {
      await _analytics.logLogin(loginMethod: loginMethod);

      if (kDebugMode) {
        print('Analytics: Login logged - $loginMethod');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging login - $e');
      }
    }
  }

  @override
  Future<void> logSignUp(String signUpMethod) async {
    try {
      await _analytics.logSignUp(signUpMethod: signUpMethod);

      if (kDebugMode) {
        print('Analytics: Sign up logged - $signUpMethod');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging sign up - $e');
      }
    }
  }

  @override
  Future<void> setUserId(String? userId) async {
    try {
      await _analytics.setUserId(id: userId);

      if (kDebugMode) {
        print('Analytics: User ID set - $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error setting user ID - $e');
      }
    }
  }

  @override
  Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);

      if (kDebugMode) {
        print('Analytics: User property set - $name: $value');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error setting user property - $e');
      }
    }
  }

  @override
  Future<void> logAppOpen() async {
    try {
      await _analytics.logAppOpen();

      if (kDebugMode) {
        print('Analytics: App open logged');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging app open - $e');
      }
    }
  }

  @override
  Future<void> logProfileCompletionStep(String step) async {
    try {
      await _analytics.logEvent(
        name: 'profile_completion_step',
        parameters: {
          'step': step,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      if (kDebugMode) {
        print('Analytics: Profile completion step logged - $step');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging profile completion step - $e');
      }
    }
  }

  @override
  Future<void> logButtonClick({
    required String buttonName,
    required String screenName,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'button_click',
        parameters: {
          'button_name': buttonName,
          'screen_name': screenName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      if (kDebugMode) {
        print('Analytics: Button click logged - $buttonName on $screenName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging button click - $e');
      }
    }
  }

  @override
  Future<void> logNavigation({
    required String from,
    required String to,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'navigation',
        parameters: {
          'from_screen': from,
          'to_screen': to,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      if (kDebugMode) {
        print('Analytics: Navigation logged - $from to $to');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Analytics: Error logging navigation - $e');
      }
    }
  }
}
