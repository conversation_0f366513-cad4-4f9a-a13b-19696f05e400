/// Selected Interest Model
///
/// Represents a user's selected interest with category and subcategory IDs
/// Used for storing user interest selections in Firestore
///
/// This model follows the new Firestore structure for interests
library selected_interest_model;

import 'package:cloud_firestore/cloud_firestore.dart';

/// Selected Interest Model Class
///
/// Contains the category ID, subcategory ID, and selection timestamp
/// Used in the user's selected_interests array
class SelectedInterest {
  /// Reference to catalog category ID
  final String categoryId;

  /// Reference to catalog subcategory ID  
  final String subcategoryId;

  /// When the user selected this interest
  final Timestamp selectedAt;

  /// Creates a SelectedInterest instance
  ///
  /// @param categoryId The category ID from the catalog
  /// @param subcategoryId The subcategory ID from the catalog
  /// @param selectedAt When this interest was selected
  const SelectedInterest({
    required this.categoryId,
    required this.subcategoryId,
    required this.selectedAt,
  });

  /// Factory constructor to create a SelectedInterest from JSON
  ///
  /// Parses a JSON map and creates a SelectedInterest instance
  /// Handles timestamp conversion from Firestore
  ///
  /// @param json Map containing selected interest data from Firestore
  /// @return SelectedInterest instance with data from the JSON map
  factory SelectedInterest.fromJson(Map<String, dynamic> json) {
    return SelectedInterest(
      categoryId: json['category_id'] ?? '',
      subcategoryId: json['subcategory_id'] ?? '',
      selectedAt: json['selected_at'] is Timestamp
          ? json['selected_at']
          : Timestamp.now(),
    );
  }

  /// Converts the SelectedInterest to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing the selected interest data
  Map<String, dynamic> toJson() {
    return {
      'category_id': categoryId,
      'subcategory_id': subcategoryId,
      'selected_at': selectedAt,
    };
  }

  /// Creates a copy of this interest with updated values
  ///
  /// @param categoryId New category ID (optional)
  /// @param subcategoryId New subcategory ID (optional)
  /// @param selectedAt New selection timestamp (optional)
  /// @return New SelectedInterest instance with updated values
  SelectedInterest copyWith({
    String? categoryId,
    String? subcategoryId,
    Timestamp? selectedAt,
  }) {
    return SelectedInterest(
      categoryId: categoryId ?? this.categoryId,
      subcategoryId: subcategoryId ?? this.subcategoryId,
      selectedAt: selectedAt ?? this.selectedAt,
    );
  }

  /// Check if two SelectedInterest instances are equal
  ///
  /// @param other The other SelectedInterest to compare with
  /// @return True if both instances have the same category and subcategory IDs
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SelectedInterest &&
        other.categoryId == categoryId &&
        other.subcategoryId == subcategoryId;
  }

  /// Generate hash code for this instance
  ///
  /// @return Hash code based on category and subcategory IDs
  @override
  int get hashCode {
    return categoryId.hashCode ^ subcategoryId.hashCode;
  }

  /// String representation of this SelectedInterest
  ///
  /// @return String describing this selected interest
  @override
  String toString() {
    return 'SelectedInterest(categoryId: $categoryId, subcategoryId: $subcategoryId, selectedAt: $selectedAt)';
  }
}
