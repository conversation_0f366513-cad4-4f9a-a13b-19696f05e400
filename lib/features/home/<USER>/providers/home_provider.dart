/// Home Provider
///
/// Provides Riverpod state management for home screen functionality
/// Simple home screen without matching functionality
library home_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/shared/models/user_model.dart';

part 'home_provider.g.dart';

/// State class for home screen
class HomeState {
  /// Current user model
  final UserModel? currentUser;

  /// Whether home data is loading
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  const HomeState({
    this.currentUser,
    this.isLoading = false,
    this.errorMessage,
  });

  HomeState copyWith({
    UserModel? currentUser,
    bool? isLoading,
    String? errorMessage,
  }) {
    return HomeState(
      currentUser: currentUser ?? this.currentUser,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  HomeState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// Home Notifier
///
/// Manages home screen state and business logic
/// Simple home screen without matching functionality
@riverpod
class Home extends _$Home {
  @override
  HomeState build() {
    // Defer loading initial data to avoid initialization issues
    Future.microtask(() => _loadInitialData());

    if (kDebugMode) {
      print('HomeNotifier: Initialized');
    }

    return const HomeState();
  }

  /// Load initial home data
  Future<void> _loadInitialData() async {
    try {
      state = state.copyWith(isLoading: true);

      // Get current user - handle case where it might be null
      final currentUser = ref.read(currentUserModelProvider);

      if (currentUser != null) {
        state = state.copyWith(currentUser: currentUser);
      } else {
        // User model not available yet - start with empty state
        if (kDebugMode) {
          print('HomeNotifier: User model not available yet');
        }
      }

      state = state.copyWith(isLoading: false);

      if (kDebugMode) {
        print('HomeNotifier: Initial data loaded');
      }
    } catch (e) {
      // Handle provider not initialized error gracefully
      state = state.copyWith(
        isLoading: false,
        errorMessage: null, // Don't show error for initialization issues
      );

      if (kDebugMode) {
        print(
            'HomeNotifier: Error loading initial data (likely provider not initialized) - $e');
        print('HomeNotifier: Starting with empty state');
      }
    }
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Reload home data
  Future<void> reloadData() async {
    await _loadInitialData();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for current user
@riverpod
UserModel? homeCurrentUser(HomeCurrentUserRef ref) {
  return ref.watch(homeProvider).currentUser;
}

/// Provider for home loading state
@riverpod
bool isHomeLoading(IsHomeLoadingRef ref) {
  return ref.watch(homeProvider).isLoading;
}

/// Provider for home error message
@riverpod
String? homeErrorMessage(HomeErrorMessageRef ref) {
  return ref.watch(homeProvider).errorMessage;
}
