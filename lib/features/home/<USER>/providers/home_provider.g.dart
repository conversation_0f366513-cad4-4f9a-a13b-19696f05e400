// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$homeCurrentUserHash() => r'4f4a6d7d36cffb0bbcf0defab1bf6ef7fb1018da';

/// Provider for current user
///
/// Copied from [homeCurrentUser].
@ProviderFor(homeCurrentUser)
final homeCurrentUserProvider = AutoDisposeProvider<UserModel?>.internal(
  homeCurrentUser,
  name: r'homeCurrentUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$homeCurrentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HomeCurrentUserRef = AutoDisposeProviderRef<UserModel?>;
String _$isHomeLoadingHash() => r'5e1c4af12d9c67b97706eab675c9719c6b0b35f4';

/// Provider for home loading state
///
/// Copied from [isHomeLoading].
@ProviderFor(isHomeLoading)
final isHomeLoadingProvider = AutoDisposeProvider<bool>.internal(
  isHomeLoading,
  name: r'isHomeLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isHomeLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsHomeLoadingRef = AutoDisposeProviderRef<bool>;
String _$homeErrorMessageHash() => r'9169a9e484335374b5de6ea4ce8ca362f319663c';

/// Provider for home error message
///
/// Copied from [homeErrorMessage].
@ProviderFor(homeErrorMessage)
final homeErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  homeErrorMessage,
  name: r'homeErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$homeErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HomeErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$homeHash() => r'62838c981d30f3663fd0ef1a3ddc99f2ec811e44';

/// Home Notifier
///
/// Manages home screen state and business logic
/// Simple home screen without matching functionality
///
/// Copied from [Home].
@ProviderFor(Home)
final homeProvider = AutoDisposeNotifierProvider<Home, HomeState>.internal(
  Home.new,
  name: r'homeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$homeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Home = AutoDisposeNotifier<HomeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
