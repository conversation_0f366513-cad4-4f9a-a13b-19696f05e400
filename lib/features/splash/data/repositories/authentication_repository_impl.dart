/// Authentication Repository Implementation
///
/// Implements the AuthenticationRepository interface using local data sources
/// Provides concrete implementation for authentication data operations
library authentication_repository_impl;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/splash/domain/entities/authentication_status.dart';
import 'package:towasl/features/splash/domain/repositories/authentication_repository.dart';
import 'package:towasl/features/splash/data/datasources/local_authentication_datasource.dart';

/// Implementation of AuthenticationRepository
///
/// Concrete implementation that uses LocalAuthenticationDataSource
/// to provide authentication data operations
class AuthenticationRepositoryImpl implements AuthenticationRepository {
  /// Local data source for authentication operations
  final LocalAuthenticationDataSource _localDataSource;

  /// Creates an AuthenticationRepositoryImpl
  ///
  /// @param localDataSource Data source for local authentication operations
  const AuthenticationRepositoryImpl(this._localDataSource);

  @override
  Future<AuthenticationStatus> checkAuthenticationStatus() async {
    try {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Checking authentication status');
      }

      // Get authentication status from local data source
      final authStatusModel = await _localDataSource.getAuthenticationStatus();
      
      // Convert model to domain entity
      final authStatus = authStatusModel.toEntity();

      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Authentication status retrieved');
        print('  - Status: $authStatus');
      }

      return authStatus;
    } catch (e) {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Error checking authentication status: $e');
      }
      
      // Return unauthenticated status on error
      return AuthenticationStatus.unauthenticated();
    }
  }

  @override
  Future<void> clearAuthenticationData() async {
    try {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Clearing authentication data');
      }

      await _localDataSource.clearAuthenticationData();

      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Authentication data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Error clearing authentication data: $e');
      }
      rethrow;
    }
  }

  @override
  Future<String?> getStoredUserId() async {
    try {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Getting stored user ID');
      }

      final userId = await _localDataSource.getStoredUserId();

      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: User ID retrieved: ${userId ?? 'null'}');
      }

      return userId;
    } catch (e) {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Error getting stored user ID: $e');
      }
      return null;
    }
  }

  @override
  Future<bool> isUserLoggedIn() async {
    try {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Checking if user is logged in');
      }

      final isLoggedIn = await _localDataSource.isUserLoggedIn();

      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: User logged in status: $isLoggedIn');
      }

      return isLoggedIn;
    } catch (e) {
      if (kDebugMode) {
        print('AuthenticationRepositoryImpl: Error checking login status: $e');
      }
      return false;
    }
  }
}
