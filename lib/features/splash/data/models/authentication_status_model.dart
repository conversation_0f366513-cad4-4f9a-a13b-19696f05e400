/// Authentication Status Model
///
/// Data model for authentication status with serialization capabilities
/// Extends the domain entity with JSON serialization for data persistence
library authentication_status_model;

import 'package:towasl/features/splash/domain/entities/authentication_status.dart';

/// Authentication Status Data Model
///
/// Extends AuthenticationStatus entity with serialization capabilities
/// Used for converting between domain entities and data storage formats
class AuthenticationStatusModel extends AuthenticationStatus {
  /// Creates an AuthenticationStatusModel
  ///
  /// @param isLoggedIn Whether the user is currently logged in
  /// @param userId User ID if logged in, null otherwise
  /// @param hasInterests Whether user has completed interests selection
  /// @param hasLocation Whether user has set location
  /// @param hasPersonalInfo Whether user has completed personal information
  const AuthenticationStatusModel({
    required super.isLoggedIn,
    super.userId,
    required super.hasInterests,
    required super.hasLocation,
    required super.hasPersonalInfo,
  });

  /// Creates an AuthenticationStatusModel from domain entity
  ///
  /// @param entity The domain entity to convert
  /// @return AuthenticationStatusModel instance
  factory AuthenticationStatusModel.fromEntity(AuthenticationStatus entity) {
    return AuthenticationStatusModel(
      isLoggedIn: entity.isLoggedIn,
      userId: entity.userId,
      hasInterests: entity.hasInterests,
      hasLocation: entity.hasLocation,
      hasPersonalInfo: entity.hasPersonalInfo,
    );
  }

  /// Creates an AuthenticationStatusModel from JSON
  ///
  /// @param json Map containing the authentication status data
  /// @return AuthenticationStatusModel instance
  factory AuthenticationStatusModel.fromJson(Map<String, dynamic> json) {
    return AuthenticationStatusModel(
      isLoggedIn: json['isLoggedIn'] as bool? ?? false,
      userId: json['userId'] as String?,
      hasInterests: json['hasInterests'] as bool? ?? false,
      hasLocation: json['hasLocation'] as bool? ?? false,
      hasPersonalInfo: json['hasPersonalInfo'] as bool? ?? false,
    );
  }

  /// Converts the model to JSON
  ///
  /// @return Map containing the authentication status data
  Map<String, dynamic> toJson() {
    return {
      'isLoggedIn': isLoggedIn,
      'userId': userId,
      'hasInterests': hasInterests,
      'hasLocation': hasLocation,
      'hasPersonalInfo': hasPersonalInfo,
    };
  }

  /// Creates an unauthenticated model
  ///
  /// @return AuthenticationStatusModel with unauthenticated state
  factory AuthenticationStatusModel.unauthenticated() {
    return const AuthenticationStatusModel(
      isLoggedIn: false,
      userId: null,
      hasInterests: false,
      hasLocation: false,
      hasPersonalInfo: false,
    );
  }

  /// Creates an authenticated model
  ///
  /// @param userId User ID
  /// @param hasInterests Whether user has completed interests selection
  /// @param hasLocation Whether user has set location
  /// @param hasPersonalInfo Whether user has completed personal information
  /// @return AuthenticationStatusModel with authenticated state
  factory AuthenticationStatusModel.authenticated({
    required String userId,
    required bool hasInterests,
    required bool hasLocation,
    required bool hasPersonalInfo,
  }) {
    return AuthenticationStatusModel(
      isLoggedIn: true,
      userId: userId,
      hasInterests: hasInterests,
      hasLocation: hasLocation,
      hasPersonalInfo: hasPersonalInfo,
    );
  }

  /// Converts the model to domain entity
  ///
  /// @return AuthenticationStatus domain entity
  AuthenticationStatus toEntity() {
    return AuthenticationStatus(
      isLoggedIn: isLoggedIn,
      userId: userId,
      hasInterests: hasInterests,
      hasLocation: hasLocation,
      hasPersonalInfo: hasPersonalInfo,
    );
  }

  /// Creates a copy of the model with updated values
  ///
  /// @param isLoggedIn New logged in status
  /// @param userId New user ID
  /// @param hasInterests New interests completion status
  /// @param hasLocation New location completion status
  /// @param hasPersonalInfo New personal info completion status
  /// @return New AuthenticationStatusModel instance
  AuthenticationStatusModel copyWith({
    bool? isLoggedIn,
    String? userId,
    bool? hasInterests,
    bool? hasLocation,
    bool? hasPersonalInfo,
  }) {
    return AuthenticationStatusModel(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      userId: userId ?? this.userId,
      hasInterests: hasInterests ?? this.hasInterests,
      hasLocation: hasLocation ?? this.hasLocation,
      hasPersonalInfo: hasPersonalInfo ?? this.hasPersonalInfo,
    );
  }

  @override
  String toString() {
    return 'AuthenticationStatusModel(isLoggedIn: $isLoggedIn, userId: $userId, '
           'hasInterests: $hasInterests, hasLocation: $hasLocation, '
           'hasPersonalInfo: $hasPersonalInfo)';
  }
}
