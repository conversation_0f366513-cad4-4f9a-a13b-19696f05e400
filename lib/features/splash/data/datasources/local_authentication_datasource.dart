/// Local Authentication Data Source
///
/// Handles local storage operations for authentication data
/// Provides methods for storing and retrieving authentication status
library local_authentication_datasource;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/storage_service.dart';
import 'package:towasl/features/splash/data/models/authentication_status_model.dart';

/// Local data source for authentication operations
///
/// Handles reading and writing authentication data to local storage
/// Integrates with existing storage services and user controllers
abstract class LocalAuthenticationDataSource {
  /// Get authentication status from local storage
  ///
  /// @return A Future that resolves to an AuthenticationStatusModel
  Future<AuthenticationStatusModel> getAuthenticationStatus();

  /// Clear authentication data from local storage
  ///
  /// @return A Future that completes when data is cleared
  Future<void> clearAuthenticationData();

  /// Check if user is logged in
  ///
  /// @return A Future that resolves to true if user is logged in
  Future<bool> isUserLoggedIn();

  /// Get stored user ID
  ///
  /// @return A Future that resolves to the user ID or null
  Future<String?> getStoredUserId();
}

/// Implementation of LocalAuthenticationDataSource
///
/// Concrete implementation that uses StorageService and existing controllers
/// to retrieve authentication status and user profile completion data
class LocalAuthenticationDataSourceImpl
    implements LocalAuthenticationDataSource {
  /// Storage service for local data operations
  final StorageService _storageService;

  /// Creates a LocalAuthenticationDataSourceImpl
  ///
  /// @param storageService Service for local storage operations
  const LocalAuthenticationDataSourceImpl(this._storageService);

  @override
  Future<AuthenticationStatusModel> getAuthenticationStatus() async {
    try {
      if (kDebugMode) {
        print('LocalAuthenticationDataSource: Getting authentication status');
      }

      // Check if user is logged in
      final isLoggedIn = _storageService.getLoggedInStatus();

      if (!isLoggedIn) {
        if (kDebugMode) {
          print('LocalAuthenticationDataSource: User is not logged in');
        }
        return AuthenticationStatusModel.unauthenticated();
      }

      // Get user ID
      final userId = _storageService.getUserIDValue();
      if (userId.isEmpty) {
        if (kDebugMode) {
          print('LocalAuthenticationDataSource: No user ID found');
        }
        return AuthenticationStatusModel.unauthenticated();
      }

      // For now, return a simple status indicating user needs onboarding
      // In a full Riverpod implementation, this would use providers

      if (kDebugMode) {
        print(
            'LocalAuthenticationDataSource: User authenticated, returning welcome status');
      }

      // Return welcome status to start onboarding flow
      return AuthenticationStatusModel.unauthenticated();
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocalAuthenticationDataSource: Error getting authentication status: $e');
      }

      // Return unauthenticated status on error
      return AuthenticationStatusModel.unauthenticated();
    }
  }

  @override
  Future<void> clearAuthenticationData() async {
    try {
      if (kDebugMode) {
        print('LocalAuthenticationDataSource: Clearing authentication data');
      }

      // Clear user data (simplified for Riverpod migration)
      // In full Riverpod implementation, this would clear provider state

      if (kDebugMode) {
        print('LocalAuthenticationDataSource: Authentication data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocalAuthenticationDataSource: Error clearing authentication data: $e');
      }
      rethrow;
    }
  }

  @override
  Future<bool> isUserLoggedIn() async {
    try {
      return _storageService.getLoggedInStatus();
    } catch (e) {
      if (kDebugMode) {
        print('LocalAuthenticationDataSource: Error checking login status: $e');
      }
      return false;
    }
  }

  @override
  Future<String?> getStoredUserId() async {
    try {
      final userId = _storageService.getUserIDValue();
      return userId.isEmpty ? null : userId;
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocalAuthenticationDataSource: Error getting stored user ID: $e');
      }
      return null;
    }
  }
}
