/// Splash View
///
/// Entry point of the application that displays the app logo
/// Handles authentication status check and navigation to appropriate screens
/// Migrated to Riverpod for state management
library splash_view;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/shared/mixins/analytics_mixin.dart';
import 'package:towasl/features/splash/presentation/widgets/splash_logo_widget.dart';
import 'package:towasl/features/splash/presentation/widgets/version_display_widget.dart';
import 'package:towasl/features/welcome/presentation/views/welcome_view.dart';
import 'package:towasl/features/home/<USER>/views/home_view.dart';
import 'package:towasl/features/interests/presentation/views/interests_view.dart';
import 'package:towasl/features/location/presentation/views/location_view.dart';
import 'package:towasl/features/personal_info/presentation/views/personal_info_view.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/features/app_update/app_update.dart';
import 'package:towasl/features/app_update/presentation/widgets/update_dialog_widget.dart';

/// Splash View
///
/// Entry point of the application that displays the app logo
/// Handles authentication status check and navigation to appropriate screens
/// Follows MVVM pattern with Riverpod providers
class SplashView extends ConsumerStatefulWidget {
  /// Creates a SplashView
  const SplashView({super.key});

  @override
  ConsumerState<SplashView> createState() => _SplashViewState();
}

/// State for the SplashView
class _SplashViewState extends ConsumerState<SplashView> with AnalyticsMixin {
  @override
  void initState() {
    super.initState();

    if (kDebugMode) {
      print('SplashView: Initialized');
    }

    // Track screen view
    trackScreenView(screenName: AnalyticsScreenNames.splash);

    // Check authentication status and navigate accordingly
    _checkAuthenticationAndNavigate();
  }

  /// Check authentication status and navigate to appropriate screen
  Future<void> _checkAuthenticationAndNavigate() async {
    if (kDebugMode) {
      print('SplashView: Starting authentication check');
    }

    // Show splash for minimum 2 seconds
    await Future.delayed(const Duration(seconds: 2));
    if (kDebugMode) {
      print('SplashView: Splash delay completed');
    }

    if (!mounted) {
      if (kDebugMode) {
        print('SplashView: Widget not mounted, returning');
      }
      return;
    }

    // Check for app updates first
    await _checkForAppUpdates();

    if (!mounted) return;

    try {
      if (kDebugMode) {
        print('SplashView: Checking login status directly from storage');
      }
      // Check login status directly from storage service as a workaround
      // for the provider architecture issue
      final storageService = ref.read(storageServiceProvider);
      final isLoggedIn = storageService.getLoggedInStatus();
      final userId = storageService.getUserIDValue();

      if (kDebugMode) {
        print(
            'SplashView: Storage - User ID: $userId, isLoggedIn: $isLoggedIn');
      }
      if (kDebugMode) {
        print('SplashView: User logged in status: $isLoggedIn');
      }

      if (!mounted) return;

      if (isLoggedIn) {
        if (kDebugMode) {
          print('SplashView: User is logged in, checking profile completion');
        }
        // Check profile completion before navigating
        await _checkProfileCompletionAndNavigate(userId);
      } else {
        if (kDebugMode) {
          print('SplashView: Navigating to WelcomeView');
        }
        // User is not logged in, navigate to welcome
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const WelcomeView(),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('SplashView: Error checking authentication: $e');
      }
      // On error, navigate to welcome screen as fallback
      if (mounted) {
        if (kDebugMode) {
          print('SplashView: Navigating to WelcomeView (fallback)');
        }
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const WelcomeView(),
          ),
        );
      }
    }
  }

  /// Check profile completion and navigate accordingly
  Future<void> _checkProfileCompletionAndNavigate(String userId) async {
    try {
      if (kDebugMode) {
        print('SplashView: Checking profile completion for user: $userId');
      }

      // Check profile completion using the profile checker
      final hasInterests = await ref.read(hasInterestsProvider(userId).future);
      final hasLocation = await ref.read(hasLocationProvider(userId).future);
      final hasPersonalInfo =
          await ref.read(hasPersonalInfoProvider(userId).future);

      if (kDebugMode) {
        print('SplashView: Profile completion status:');
        print('  - Has interests: $hasInterests');
        print('  - Has location: $hasLocation');
        print('  - Has personal info: $hasPersonalInfo');
      }

      if (!mounted) return;

      // Navigate based on profile completion in the correct order
      // New order: Interests → Personal Info → Location → Home
      if (!hasInterests) {
        if (kDebugMode) {
          print('SplashView: Missing interests, navigating to interests page');
        }
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const InterestsView()),
        );
      } else if (!hasPersonalInfo) {
        if (kDebugMode) {
          print(
              'SplashView: Missing personal info, navigating to personal info page');
        }
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const PersonalInfoView()),
        );
      } else if (!hasLocation) {
        if (kDebugMode) {
          print('SplashView: Missing location, navigating to location page');
        }
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LocationView()),
        );
      } else {
        if (kDebugMode) {
          print('SplashView: Profile complete, navigating to home page');
        }
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeView()),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('SplashView: Error checking profile completion - $e');
      }
      // On error, navigate to home as fallback
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeView()),
        );
      }
    }
  }

  /// Check for app updates
  Future<void> _checkForAppUpdates() async {
    try {
      if (kDebugMode) {
        print('SplashView: Checking for app updates');
      }

      // Initialize version service
      final versionService = ref.read(versionServiceProvider);
      await versionService.initialize();

      // Get current app version
      final currentVersion = versionService.getVersionForComparison();

      if (kDebugMode) {
        print('SplashView: Current app version: $currentVersion');
      }

      // Get the use case for checking updates
      final checkUpdateUseCase = ref.read(checkAppUpdateUseCaseProvider);

      // Check for updates with fallback to cached data
      final updateResult =
          await checkUpdateUseCase.executeWithFallback(currentVersion);

      if (!mounted) return;

      if (updateResult != null && updateResult.hasUpdate) {
        if (kDebugMode) {
          print('SplashView: Update available - ${updateResult.status}');
        }

        if (updateResult.isMandatory) {
          // Show mandatory update dialog
          await UpdateDialogWidget.showMandatoryUpdateDialog(
              context, updateResult);
          // For mandatory updates, don't proceed with normal flow
          return;
        } else if (updateResult.isOptional) {
          // Check if optional update dialog has already been shown for this version
          final storageService = ref.read(storageServiceProvider);
          final latestVersion =
              updateResult.versionControl.currentPlatformLatestVersion;

          if (!storageService.hasOptionalUpdateBeenShown(latestVersion)) {
            if (kDebugMode) {
              print(
                  'SplashView: Showing optional update dialog for version $latestVersion');
            }

            // Show optional update dialog
            final shouldUpdate =
                await UpdateDialogWidget.showOptionalUpdateDialog(
                    context, updateResult);

            // Mark as shown regardless of user choice
            await storageService.setOptionalUpdateShown(latestVersion);

            if (kDebugMode) {
              print('SplashView: User chose to update: $shouldUpdate');
            }
          } else {
            if (kDebugMode) {
              print(
                  'SplashView: Optional update dialog already shown for version $latestVersion, skipping');
            }
          }
          // Continue with normal flow regardless of user choice for optional updates
        }
      } else {
        if (kDebugMode) {
          print('SplashView: No updates available or update check failed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('SplashView: Error checking for updates - $e');
      }
      // Continue with normal flow even if update check fails
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      body: SafeArea(
        child: _buildBody(),
      ),
    );
  }

  /// Build the main body of the splash screen
  Widget _buildBody() {
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Main content area with logo
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Animated splash logo
                SplashLogoWidget(
                  height: 130,
                  width: double.infinity,
                  repeat: false,
                ),
              ],
            ),
          ),
        ),

        // App version display at bottom (centered)
        SizedBox(
          width: double.infinity,
          child: VersionDisplayWidget(),
        ),
      ],
    );
  }
}
