/// Splash Logo Widget
///
/// Reusable widget for displaying the animated splash logo
/// Encapsulates the logo animation logic and styling
library splash_logo_widget;

import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:towasl/core/theme/app_animation.dart';

/// Widget for displaying the animated splash logo
///
/// Shows the Lottie animation for the app logo during splash screen
/// Provides consistent styling and animation behavior
class SplashLogoWidget extends StatelessWidget {
  /// Height of the logo animation
  final double height;

  /// Width of the logo animation
  final double width;

  /// Whether the animation should repeat
  final bool repeat;

  /// Animation controller for custom control (optional)
  final AnimationController? controller;

  /// Callback when animation completes
  final VoidCallback? onAnimationComplete;

  /// Creates a SplashLogoWidget
  ///
  /// @param height Height of the logo animation (default: 130)
  /// @param width Width of the logo animation (default: double.infinity)
  /// @param repeat Whether the animation should repeat (default: false)
  /// @param controller Optional animation controller for custom control
  /// @param onAnimationComplete Callback when animation completes
  const SplashLogoWidget({
    super.key,
    this.height = 130,
    this.width = double.infinity,
    this.repeat = false,
    this.controller,
    this.onAnimationComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Lottie.asset(
        AppAnimation.splashLogo,
        height: height,
        width: width,
        repeat: repeat,
        controller: controller,
        onLoaded: (composition) {
          // Set up animation controller if provided
          if (controller != null) {
            controller!
              ..duration = composition.duration
              ..forward().then((_) {
                if (onAnimationComplete != null) {
                  onAnimationComplete!();
                }
              });
          }
        },
      ),
    );
  }
}

/// Widget for displaying a static version of the splash logo
///
/// Shows a non-animated version of the logo for cases where animation is not needed
class SplashLogoStaticWidget extends StatelessWidget {
  /// Height of the logo
  final double height;

  /// Width of the logo
  final double width;

  /// Creates a SplashLogoStaticWidget
  ///
  /// @param height Height of the logo (default: 130)
  /// @param width Width of the logo (default: double.infinity)
  const SplashLogoStaticWidget({
    super.key,
    this.height = 130,
    this.width = double.infinity,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Lottie.asset(
        AppAnimation.splashLogo,
        height: height,
        width: width,
        repeat: false,
        animate: false, // Static display
      ),
    );
  }
}

/// Widget for displaying splash logo with loading indicator
///
/// Combines the logo with a loading indicator for better user feedback
class SplashLogoWithLoadingWidget extends StatelessWidget {
  /// Height of the logo animation
  final double logoHeight;

  /// Width of the logo animation
  final double logoWidth;

  /// Whether to show loading indicator
  final bool showLoading;

  /// Loading indicator color
  final Color? loadingColor;

  /// Creates a SplashLogoWithLoadingWidget
  ///
  /// @param logoHeight Height of the logo animation (default: 130)
  /// @param logoWidth Width of the logo animation (default: double.infinity)
  /// @param showLoading Whether to show loading indicator (default: true)
  /// @param loadingColor Color of the loading indicator
  const SplashLogoWithLoadingWidget({
    super.key,
    this.logoHeight = 130,
    this.logoWidth = double.infinity,
    this.showLoading = true,
    this.loadingColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SplashLogoWidget(
          height: logoHeight,
          width: logoWidth,
        ),
        if (showLoading) ...[
          const SizedBox(height: 24),
          SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                loadingColor ?? Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
