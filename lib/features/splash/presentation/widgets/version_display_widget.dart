/// Version Display Widget
///
/// Reusable widget for displaying app version information
/// Shows the current app version at the bottom of the splash screen
library version_display_widget;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Widget for displaying app version information
///
/// Shows the current app version with consistent styling
/// Used in splash screen and other places where version info is needed
class VersionDisplayWidget extends StatelessWidget {
  /// Text style for the version display
  final TextStyle? textStyle;

  /// Padding around the version text
  final EdgeInsetsGeometry? padding;

  /// Text alignment for the version display
  final TextAlign textAlign;

  /// Whether to show the "v" prefix
  final bool showPrefix;

  /// Custom version text (if not using VersionService)
  final String? customVersion;

  /// Creates a VersionDisplayWidget
  ///
  /// @param textStyle Custom text style for the version display
  /// @param padding Padding around the version text
  /// @param textAlign Text alignment for the version display
  /// @param showPrefix Whether to show the "v" prefix (default: true)
  /// @param customVersion Custom version text to display
  const VersionDisplayWidget({
    super.key,
    this.textStyle,
    this.padding,
    this.textAlign = TextAlign.center,
    this.showPrefix = true,
    this.customVersion,
  });

  @override
  Widget build(BuildContext context) {
    // Use a simple hardcoded version for now
    String versionText = customVersion ?? '1.0.0';

    // Add prefix if requested
    if (showPrefix && !versionText.startsWith('v')) {
      versionText = 'v$versionText';
    }

    return Padding(
      padding: padding ?? const EdgeInsets.only(bottom: 24.0),
      child: Text(
        versionText,
        style: textStyle ??
            styleGreyNormal.copyWith(
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
        textAlign: textAlign,
      ),
    );
  }
}
