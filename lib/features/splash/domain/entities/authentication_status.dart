/// Authentication Status Entity
///
/// Represents the authentication state of a user in the domain layer
/// This is a pure business entity without any framework dependencies
library authentication_status_entity;

/// Authentication Status Entity
///
/// Contains information about user authentication state and profile completion
/// Used by use cases to determine navigation flow
class AuthenticationStatus {
  /// Whether the user is currently logged in
  final bool isLoggedIn;

  /// User ID if logged in, null otherwise
  final String? userId;

  /// Whether user has completed interests selection
  final bool hasInterests;

  /// Whether user has set location
  final bool hasLocation;

  /// Whether user has completed personal information
  final bool hasPersonalInfo;

  /// Creates an AuthenticationStatus entity
  ///
  /// @param isLoggedIn Whether the user is currently logged in
  /// @param userId User ID if logged in, null otherwise
  /// @param hasInterests Whether user has completed interests selection
  /// @param hasLocation Whether user has set location
  /// @param hasPersonalInfo Whether user has completed personal information
  const AuthenticationStatus({
    required this.isLoggedIn,
    this.userId,
    required this.hasInterests,
    required this.hasLocation,
    required this.hasPersonalInfo,
  });

  /// Creates an unauthenticated status
  ///
  /// Used when user is not logged in
  factory AuthenticationStatus.unauthenticated() {
    return const AuthenticationStatus(
      isLoggedIn: false,
      userId: null,
      hasInterests: false,
      hasLocation: false,
      hasPersonalInfo: false,
    );
  }

  /// Creates an authenticated status with profile completion info
  ///
  /// Used when user is logged in
  factory AuthenticationStatus.authenticated({
    required String userId,
    required bool hasInterests,
    required bool hasLocation,
    required bool hasPersonalInfo,
  }) {
    return AuthenticationStatus(
      isLoggedIn: true,
      userId: userId,
      hasInterests: hasInterests,
      hasLocation: hasLocation,
      hasPersonalInfo: hasPersonalInfo,
    );
  }

  /// Determines the next navigation step based on profile completion
  ///
  /// @return String representing the next required step
  String getNextStep() {
    if (!isLoggedIn) return 'welcome';
    if (!hasInterests) return 'interests';
    if (!hasLocation) return 'location';
    if (!hasPersonalInfo) return 'personal_info';
    return 'home';
  }

  /// Checks if user profile is complete
  ///
  /// @return True if all profile steps are completed
  bool get isProfileComplete => hasInterests && hasLocation && hasPersonalInfo;

  /// Calculates profile completion percentage
  ///
  /// @return Completion percentage (0.0 to 1.0)
  double get completionPercentage {
    if (!isLoggedIn) return 0.0;
    
    int completedSteps = 0;
    const int totalSteps = 3;
    
    if (hasInterests) completedSteps++;
    if (hasLocation) completedSteps++;
    if (hasPersonalInfo) completedSteps++;
    
    return completedSteps / totalSteps;
  }

  @override
  String toString() {
    return 'AuthenticationStatus(isLoggedIn: $isLoggedIn, userId: $userId, '
           'hasInterests: $hasInterests, hasLocation: $hasLocation, '
           'hasPersonalInfo: $hasPersonalInfo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AuthenticationStatus &&
        other.isLoggedIn == isLoggedIn &&
        other.userId == userId &&
        other.hasInterests == hasInterests &&
        other.hasLocation == hasLocation &&
        other.hasPersonalInfo == hasPersonalInfo;
  }

  @override
  int get hashCode {
    return isLoggedIn.hashCode ^
        userId.hashCode ^
        hasInterests.hashCode ^
        hasLocation.hashCode ^
        hasPersonalInfo.hashCode;
  }
}
