/// Check Authentication Use Case
///
/// Business logic for checking user authentication status and determining navigation flow
/// Encapsulates the rules for splash screen navigation based on user state
library check_authentication_usecase;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/splash/domain/entities/authentication_status.dart';
import 'package:towasl/features/splash/domain/repositories/authentication_repository.dart';

/// Use case for checking authentication status
///
/// Handles the business logic for determining user authentication state
/// and profile completion status during app startup
class CheckAuthenticationUseCase {
  /// Authentication repository for data operations
  final AuthenticationRepository _authenticationRepository;

  /// Creates a CheckAuthenticationUseCase
  ///
  /// @param authenticationRepository Repository for authentication operations
  const CheckAuthenticationUseCase(this._authenticationRepository);

  /// Execute the authentication check
  ///
  /// Performs the complete authentication status check including:
  /// 1. Checking if user is logged in
  /// 2. Retrieving user profile completion status
  /// 3. Determining next navigation step
  ///
  /// @return A Future that resolves to an AuthenticationStatus entity
  Future<AuthenticationStatus> execute() async {
    try {
      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Starting authentication check');
      }

      // Get complete authentication status from repository
      final authStatus = await _authenticationRepository.checkAuthenticationStatus();

      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Authentication status retrieved');
        print('  - Is logged in: ${authStatus.isLoggedIn}');
        print('  - User ID: ${authStatus.userId}');
        print('  - Has interests: ${authStatus.hasInterests}');
        print('  - Has location: ${authStatus.hasLocation}');
        print('  - Has personal info: ${authStatus.hasPersonalInfo}');
        print('  - Next step: ${authStatus.getNextStep()}');
        print('  - Profile completion: ${(authStatus.completionPercentage * 100).toStringAsFixed(1)}%');
      }

      return authStatus;
    } catch (e) {
      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Error checking authentication status: $e');
      }
      
      // Return unauthenticated status on error to ensure app doesn't crash
      return AuthenticationStatus.unauthenticated();
    }
  }

  /// Clear authentication data
  ///
  /// Clears stored authentication information when user needs to be logged out
  /// Used when navigating to welcome screen
  ///
  /// @return A Future that completes when data is cleared
  Future<void> clearAuthentication() async {
    try {
      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Clearing authentication data');
      }

      await _authenticationRepository.clearAuthenticationData();

      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Authentication data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Error clearing authentication data: $e');
      }
      rethrow;
    }
  }

  /// Quick authentication check
  ///
  /// Performs a simple check to see if user is logged in
  /// without retrieving full profile completion details
  ///
  /// @return A Future that resolves to true if user is logged in
  Future<bool> isAuthenticated() async {
    try {
      return await _authenticationRepository.isUserLoggedIn();
    } catch (e) {
      if (kDebugMode) {
        print('CheckAuthenticationUseCase: Error checking if user is authenticated: $e');
      }
      return false;
    }
  }
}
