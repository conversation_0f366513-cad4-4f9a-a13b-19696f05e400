/// Authentication Repository Interface
///
/// Defines the contract for authentication data operations in the splash feature
/// Provides methods for checking authentication status and user profile completion
library authentication_repository;

import 'package:towasl/features/splash/domain/entities/authentication_status.dart';

/// Abstract repository for authentication operations
///
/// Defines the interface for checking user authentication status and profile completion
/// Implementations should handle data source specifics (storage, API, etc.)
abstract class AuthenticationRepository {
  /// Check current authentication status
  ///
  /// Retrieves the user's authentication state and profile completion status
  /// Used by the splash screen to determine navigation flow
  ///
  /// @return A Future that resolves to an AuthenticationStatus entity
  Future<AuthenticationStatus> checkAuthenticationStatus();

  /// Clear authentication data
  ///
  /// Removes stored authentication information when user logs out
  /// Used when navigating to welcome screen for unauthenticated users
  ///
  /// @return A Future that completes when data is cleared
  Future<void> clearAuthenticationData();

  /// Get stored user ID
  ///
  /// Retrieves the currently stored user ID from local storage
  /// Returns null if no user is logged in
  ///
  /// @return A Future that resolves to the user ID or null
  Future<String?> getStoredUserId();

  /// Check if user is logged in
  ///
  /// Simple check for authentication status without profile completion details
  /// Used for quick authentication checks
  ///
  /// @return A Future that resolves to true if user is logged in
  Future<bool> isUserLoggedIn();
}
