/// Personal Info Domain Entity
///
/// Pure business object representing user personal information
/// Contains no external dependencies and represents core business logic
///
/// This entity defines the structure for personal info data in the domain layer
library personal_info_entity;

/// Personal Info Entity
///
/// Represents user's personal information including name, gender, birth year, and nationality
/// Used for profile completion and user identification
class PersonalInfoEntity {
  /// User's gender (e.g., "male", "female")
  final String gender;

  /// User's birth year (used for age calculation)
  final String birthdayYear;

  /// User's nationality
  final String nationality;

  /// Creates a PersonalInfoEntity
  ///
  /// @param name User's display name
  /// @param gender User's gender
  /// @param birthdayYear User's birth year
  /// @param nationality User's nationality
  const PersonalInfoEntity({
    required this.gender,
    required this.birthdayYear,
    required this.nationality,
  });

  /// Creates a copy of this entity with updated values
  ///
  /// @param name New name (optional)
  /// @param gender New gender (optional)
  /// @param birthdayYear New birth year (optional)
  /// @param nationality New nationality (optional)
  /// @return New PersonalInfoEntity instance with updated values
  PersonalInfoEntity copyWith({
    String? gender,
    String? birthdayYear,
    String? nationality,
  }) {
    return PersonalInfoEntity(
      gender: gender ?? this.gender,
      birthdayYear: birthdayYear ?? this.birthdayYear,
      nationality: nationality ?? this.nationality,
    );
  }

  /// Creates an empty PersonalInfoEntity
  ///
  /// @return PersonalInfoEntity with empty values
  factory PersonalInfoEntity.empty() {
    return const PersonalInfoEntity(
      gender: '',
      birthdayYear: '',
      nationality: '',
    );
  }

  /// Gets the user's age based on birth year
  ///
  /// @return User's age as an integer, or 0 if birth year is invalid
  int get age {
    try {
      final currentYear = DateTime.now().year;
      return currentYear - int.parse(birthdayYear);
    } catch (e) {
      return 0;
    }
  }

  /// Checks if the personal info is complete
  ///
  /// @return True if all required fields are filled
  bool get isComplete {
    return gender.isNotEmpty &&
        birthdayYear.isNotEmpty &&
        nationality.isNotEmpty;
  }

  /// Gets completion percentage
  ///
  /// @return Completion percentage (0.0 to 1.0)
  double get completionPercentage {
    int completedFields = 0;
    const int totalFields = 4; // name, gender, birthdayYear, nationality

    if (gender.isNotEmpty) completedFields++;
    if (birthdayYear.isNotEmpty) completedFields++;
    if (nationality.isNotEmpty) completedFields++;

    return completedFields / totalFields;
  }

  /// Gets missing fields
  ///
  /// @return List of missing field names
  List<String> get missingFields {
    final List<String> missingFields = [];

    if (gender.isEmpty) missingFields.add('Gender');
    if (birthdayYear.isEmpty) missingFields.add('Birth Year');
    if (nationality.isEmpty) missingFields.add('Nationality');

    return missingFields;
  }

  /// Checks if the user is male
  ///
  /// @return True if gender is male
  bool get isMale => gender.toLowerCase() == 'male';

  /// Checks if the user is female
  ///
  /// @return True if gender is female
  bool get isFemale => gender.toLowerCase() == 'female';

  /// Checks if the user is an adult (18 or older)
  ///
  /// @return True if age is 18 or older
  bool get isAdult => age >= 18;

  /// Checks if the user is a minor (under 18)
  ///
  /// @return True if age is under 18
  bool get isMinor => age < 18;

  /// Gets the birth year as an integer
  ///
  /// @return Birth year as integer, or 0 if invalid
  int get birthYearInt {
    try {
      return int.parse(birthdayYear);
    } catch (e) {
      return 0;
    }
  }

  /// Validates the birth year
  ///
  /// @return True if birth year is valid (between 1900 and current year)
  bool get isValidBirthYear {
    final year = birthYearInt;
    final currentYear = DateTime.now().year;
    return year >= 1900 && year <= currentYear;
  }

  /// Validates the gender
  ///
  /// @return True if gender is valid (male or female)
  bool get isValidGender {
    final lowerGender = gender.toLowerCase();
    return lowerGender == 'male' || lowerGender == 'female';
  }

  /// Validates the nationality
  ///
  /// @return True if nationality is valid (not empty)
  bool get isValidNationality {
    return nationality.isNotEmpty && nationality.length >= 2;
  }

  /// Validates all fields
  ///
  /// @return PersonalInfoValidationResult with validation status
  PersonalInfoValidationResult validate() {
    final errors = <String>[];

    if (!isValidGender) {
      errors.add('Gender must be either male or female');
    }

    if (!isValidBirthYear) {
      errors.add('Birth year must be between 1900 and current year');
    }

    if (!isValidNationality) {
      errors.add('Nationality is required');
    }

    if (errors.isEmpty) {
      return PersonalInfoValidationResult.success();
    } else {
      return PersonalInfoValidationResult.failure(errors.join(', '));
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PersonalInfoEntity &&
        other.gender == gender &&
        other.birthdayYear == birthdayYear &&
        other.nationality == nationality;
  }

  @override
  int get hashCode {
    return gender.hashCode ^ birthdayYear.hashCode ^ nationality.hashCode;
  }

  @override
  String toString() {
    return 'PersonalInfoEntity(gender: $gender, '
        'age: $age, nationality: $nationality, complete: $isComplete)';
  }
}

/// Personal Info Validation Result
///
/// Represents the result of personal info validation operations
class PersonalInfoValidationResult {
  /// Whether the personal info is valid
  final bool isValid;

  /// Error message if validation failed
  final String? errorMessage;

  /// Creates a PersonalInfoValidationResult
  ///
  /// @param isValid Whether the personal info is valid
  /// @param errorMessage Error message if validation failed
  const PersonalInfoValidationResult({
    required this.isValid,
    this.errorMessage,
  });

  /// Creates a successful validation result
  ///
  /// @return PersonalInfoValidationResult indicating success
  factory PersonalInfoValidationResult.success() {
    return const PersonalInfoValidationResult(isValid: true);
  }

  /// Creates a failed validation result
  ///
  /// @param message Error message describing the validation failure
  /// @return PersonalInfoValidationResult indicating failure
  factory PersonalInfoValidationResult.failure(String message) {
    return PersonalInfoValidationResult(
      isValid: false,
      errorMessage: message,
    );
  }

  @override
  String toString() {
    return 'PersonalInfoValidationResult(isValid: $isValid, '
        'errorMessage: $errorMessage)';
  }
}

/// Gender Enumeration
///
/// Represents the available gender options
enum Gender {
  /// Male gender
  male,

  /// Female gender
  female,
}

/// Extension for Gender
extension GenderExtension on Gender {
  /// Gets the display name for the gender
  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
    }
  }

  /// Gets the string value for the gender
  String get value {
    switch (this) {
      case Gender.male:
        return 'male';
      case Gender.female:
        return 'female';
    }
  }

  /// Creates Gender from string value
  static Gender? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      default:
        return null;
    }
  }
}
