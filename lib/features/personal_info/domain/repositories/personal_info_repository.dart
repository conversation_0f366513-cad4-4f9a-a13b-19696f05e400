/// Personal Info Repository Interface
///
/// Defines the contract for personal information data operations
/// Implementations should handle data source specifics (Firestore, API, etc.)
library personal_info_repository;

import '../entities/personal_info_entity.dart';

/// Abstract repository for personal info data operations
///
/// Defines the interface for personal information-related data operations
/// Implementations should handle user profile data, validation, and persistence
abstract class PersonalInfoRepository {
  /// Get user's personal information
  ///
  /// Retrieves the user's personal information from the data source
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to PersonalInfoEntity or null if not found
  /// @throws PersonalInfoException if retrieval fails
  Future<PersonalInfoEntity?> getPersonalInfo(String userId);

  /// Update user's personal information
  ///
  /// Saves the user's personal information to the data source
  ///
  /// @param userId User identifier
  /// @param personalInfo PersonalInfoEntity to save
  /// @return A Future that completes when personal info is saved
  /// @throws PersonalInfoException if saving fails
  Future<void> updatePersonalInfo(
      String userId, PersonalInfoEntity personalInfo);

  /// Update user's gender
  ///
  /// Updates only the user's gender field
  ///
  /// @param userId User identifier
  /// @param gender New gender
  /// @return A Future that completes when gender is updated
  /// @throws PersonalInfoException if update fails
  Future<void> updateGender(String userId, String gender);

  /// Update user's birth year
  ///
  /// Updates only the user's birth year field
  ///
  /// @param userId User identifier
  /// @param birthYear New birth year
  /// @return A Future that completes when birth year is updated
  /// @throws PersonalInfoException if update fails
  Future<void> updateBirthYear(String userId, String birthYear);

  /// Update user's nationality
  ///
  /// Updates only the user's nationality field
  ///
  /// @param userId User identifier
  /// @param nationality New nationality
  /// @return A Future that completes when nationality is updated
  /// @throws PersonalInfoException if update fails
  Future<void> updateNationality(String userId, String nationality);

  /// Check if personal info is complete
  ///
  /// Verifies that all required personal information fields are filled
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to true if personal info is complete
  /// @throws PersonalInfoException if check fails
  Future<bool> isPersonalInfoComplete(String userId);

  /// Validate personal information
  ///
  /// Validates that the personal information is complete and accurate
  ///
  /// @param personalInfo PersonalInfoEntity to validate
  /// @return PersonalInfoValidationResult indicating validation status
  PersonalInfoValidationResult validatePersonalInfo(
      PersonalInfoEntity personalInfo);
}

/// Personal Info Exception
///
/// Custom exception for personal info-related errors
class PersonalInfoException implements Exception {
  /// Error message describing what went wrong
  final String message;

  /// Error code for programmatic handling
  final String? code;

  /// Creates a PersonalInfoException
  ///
  /// @param message Error message
  /// @param code Optional error code
  const PersonalInfoException(this.message, {this.code});

  @override
  String toString() =>
      'PersonalInfoException: $message${code != null ? ' (Code: $code)' : ''}';
}
