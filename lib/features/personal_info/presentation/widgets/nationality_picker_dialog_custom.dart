import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/shared/helpers/internationalization/country_data.dart';

/// Custom nationality picker dialog
class NationalityPickerDialogCustom extends StatefulWidget {
  final List<Country> countryList;
  final Country selectedCountry;
  final List<Country> filteredCountries;
  final String searchText;
  final Function(Country) onCountryChanged;
  final String languageCode;
  final NationalityPickerDialogStyleCustom? style;

  const NationalityPickerDialogCustom({
    super.key,
    required this.countryList,
    required this.selectedCountry,
    required this.filteredCountries,
    required this.searchText,
    required this.onCountryChanged,
    required this.languageCode,
    this.style,
  });

  @override
  State<NationalityPickerDialogCustom> createState() =>
      _NationalityPickerDialogCustomState();
}

class _NationalityPickerDialogCustomState
    extends State<NationalityPickerDialogCustom> {
  late TextEditingController _searchController;
  late List<Country> _filteredCountries;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.searchText);
    _filteredCountries = List.from(widget.filteredCountries);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = List.from(widget.countryList);
      } else {
        _filteredCountries = widget.countryList
            .where((country) =>
                country
                    .getLocalizedName(widget.languageCode)
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                country.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: widget.style?.backgroundColor ?? AppColors.whitePure,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Nationality',
                  style: stylePrimaryLarge.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search field
            TextField(
              controller: _searchController,
              onChanged: _filterCountries,
              decoration: InputDecoration(
                hintText: 'Search',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Countries list
            Expanded(
              child: ListView.builder(
                itemCount: _filteredCountries.length,
                itemBuilder: (context, index) {
                  final country = _filteredCountries[index];
                  final isSelected =
                      country.code == widget.selectedCountry.code;

                  return ListTile(
                    leading: Text(
                      country.flag,
                      style: const TextStyle(fontSize: 24),
                    ),
                    title: Text(
                      country.getLocalizedName(widget.languageCode),
                      style: stylePrimaryLarge.copyWith(
                        color: isSelected
                            ? AppColors.primaryPurple
                            : AppColors.greyDark,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    trailing: isSelected
                        ? const Icon(
                            Icons.check,
                            color: AppColors.primaryPurple,
                          )
                        : null,
                    onTap: () {
                      widget.onCountryChanged(country);
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Style class for nationality picker dialog
class NationalityPickerDialogStyleCustom {
  final Color? backgroundColor;
  final TextStyle? titleStyle;
  final TextStyle? itemStyle;
  final Color? selectedItemColor;

  const NationalityPickerDialogStyleCustom({
    this.backgroundColor,
    this.titleStyle,
    this.itemStyle,
    this.selectedItemColor,
  });
}
