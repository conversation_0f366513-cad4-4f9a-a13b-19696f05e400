/// Personal Info Provider
///
/// Provides Riverpod state management for personal information functionality
/// Replaces PersonalInfoViewModel with Riverpod architecture
library personal_info_provider;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/core/routes.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

part 'personal_info_provider.g.dart';

/// State class for personal information management
class PersonalInfoState {
  /// User's birth year
  final String birthdayYear;

  /// User's gender
  final String gender;

  /// User's nationality
  final String nationality;

  /// Whether personal info is loading
  final bool isLoading;

  /// Whether personal info is being saved
  final bool isSaving;

  /// Error message if any
  final String? errorMessage;

  const PersonalInfoState({
    this.birthdayYear = '',
    this.gender = '',
    this.nationality = '',
    this.isLoading = false,
    this.isSaving = false,
    this.errorMessage,
  });

  PersonalInfoState copyWith({
    String? birthdayYear,
    String? gender,
    String? nationality,
    bool? isLoading,
    bool? isSaving,
    String? errorMessage,
  }) {
    return PersonalInfoState(
      birthdayYear: birthdayYear ?? this.birthdayYear,
      gender: gender ?? this.gender,
      nationality: nationality ?? this.nationality,
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  PersonalInfoState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Clear all messages
  PersonalInfoState clearMessages() {
    return copyWith(errorMessage: null);
  }

  /// Check if all required fields are filled
  bool get isFormValid {
    return birthdayYear.trim().isNotEmpty &&
        gender.trim().isNotEmpty &&
        nationality.trim().isNotEmpty;
  }
}

/// Personal Info Notifier
///
/// Manages personal information state and business logic
/// Follows MVVM pattern with Riverpod state management
@riverpod
class PersonalInfo extends _$PersonalInfo {
  /// Text controllers for form fields
  late final TextEditingController birthdayYearController;

  @override
  PersonalInfoState build() {
    // Initialize text controllers
    birthdayYearController = TextEditingController();

    // Listen to controller changes
    birthdayYearController.addListener(_onBirthdayYearChanged);

    // Clean up when provider is disposed
    ref.onDispose(() {
      birthdayYearController.removeListener(_onBirthdayYearChanged);
      birthdayYearController.dispose();
    });

    if (kDebugMode) {
      print('PersonalInfoNotifier: Initialized');
    }

    return const PersonalInfoState();
  }

  /// Handle birthday year controller changes
  void _onBirthdayYearChanged() {
    state = state.copyWith(birthdayYear: birthdayYearController.text);
  }

  /// Load user's existing personal info
  Future<void> loadUserPersonalInfo(String userId) async {
    if (kDebugMode) {
      print('PersonalInfoNotifier: Loading user personal info for: $userId');
    }

    try {
      state = state.copyWith(isLoading: true);

      // Get user repository and fetch fresh data from Firestore
      final userRepository = ref.read(userRepositoryProvider);
      final userModel = await userRepository.getUserById(userId);

      if (userModel != null) {
        final birthdayYear = userModel.birthdayYear ?? '';
        final gender = userModel.gender ?? '';
        final nationality = userModel.nationality ?? '';

        // Update state
        state = state.copyWith(
          birthdayYear: birthdayYear,
          gender: gender,
          nationality: nationality,
          isLoading: false,
        );

        // Update controllers
        birthdayYearController.text = birthdayYear;

        if (kDebugMode) {
          print(
              'PersonalInfoNotifier: User personal info loaded from Firestore');
          print('  - Birth year: $birthdayYear');
          print('  - Gender: $gender');
          print('  - Nationality: $nationality');
        }
      } else {
        state = state.copyWith(isLoading: false);

        if (kDebugMode) {
          print('PersonalInfoNotifier: No user found in Firestore');
        }
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load personal info',
      );

      if (kDebugMode) {
        print('PersonalInfoNotifier: Error loading user personal info - $e');
      }
    }
  }

  /// Update birthday year
  void updateBirthdayYear(String year) {
    state = state.copyWith(birthdayYear: year);
    if (birthdayYearController.text != year) {
      birthdayYearController.text = year;
    }
  }

  /// Update gender
  void updateGender(String gender) {
    state = state.copyWith(gender: gender);

    if (kDebugMode) {
      print('PersonalInfoNotifier: Gender updated to: $gender');
    }
  }

  /// Update nationality
  void updateNationality(String nationality) {
    state = state.copyWith(nationality: nationality);

    if (kDebugMode) {
      print('PersonalInfoNotifier: Nationality updated to: $nationality');
    }
  }

  /// Validate form
  bool validateForm() {
    if (state.birthdayYear.trim().isEmpty) {
      state = state.copyWith(errorMessage: 'Please enter your birth year');
      return false;
    }

    // Validate birth year
    final year = int.tryParse(state.birthdayYear);
    if (year == null ||
        year < DateTime.now().year - 90 ||
        year > DateTime.now().year - 13) {
      state = state.copyWith(errorMessage: 'Please enter a valid birth year');
      return false;
    }

    if (state.gender.trim().isEmpty) {
      state = state.copyWith(errorMessage: 'Please select your gender');
      return false;
    }

    if (state.nationality.trim().isEmpty) {
      state = state.copyWith(errorMessage: 'Please select your nationality');
      return false;
    }

    // Clear error if validation passes
    state = state.clearError();
    return true;
  }

  /// Save personal information
  Future<void> savePersonalInfo() async {
    if (!validateForm()) {
      ToastCustom.errorToast(
          state.errorMessage ?? 'Please fill all required fields');
      return;
    }

    // Try to get user ID from multiple sources
    String userId = '';

    // First try from app state provider
    try {
      userId = ref.read(userIdProvider);
    } catch (e) {
      if (kDebugMode) {
        print(
            'PersonalInfoNotifier: Could not get user ID from app state provider - $e');
      }
    }

    // If still empty, try from storage service directly
    if (userId.isEmpty) {
      try {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();
        if (kDebugMode) {
          print('PersonalInfoNotifier: Got user ID from storage: $userId');
        }
      } catch (e) {
        if (kDebugMode) {
          print(
              'PersonalInfoNotifier: Could not get user ID from storage - $e');
        }
      }
    }

    if (userId.isEmpty) {
      state = state.copyWith(errorMessage: 'User not found');
      ToastCustom.errorToast('User not found');
      if (kDebugMode) {
        print('PersonalInfoNotifier: No user ID found from any source');
      }
      return;
    }

    if (kDebugMode) {
      print('PersonalInfoNotifier: Saving personal info for user: $userId');
      print('PersonalInfoNotifier: Birth Year: ${state.birthdayYear}');
      print('PersonalInfoNotifier: Gender: ${state.gender}');
      print('PersonalInfoNotifier: Nationality: ${state.nationality}');
    }

    try {
      state = state.copyWith(isSaving: true);

      // Update personal info through user provider
      final success = await ref.read(userProvider.notifier).updatePersonalInfo(
            userId,
            birthdayYear: state.birthdayYear.trim(),
            gender: state.gender,
            nationality: state.nationality,
          );

      if (success) {
        state = state.copyWith(isSaving: false);

        if (kDebugMode) {
          print('PersonalInfoNotifier: Personal info saved successfully');
        }

        // Navigate to location screen (new flow order)
        _navigateToLocation();
      } else {
        state = state.copyWith(
          isSaving: false,
          errorMessage: 'Failed to save personal info',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        errorMessage: 'An error occurred while saving personal info',
      );

      if (kDebugMode) {
        print('PersonalInfoNotifier: Error saving personal info - $e');
      }

      ToastCustom.errorToast('حدث خطأ أثناء حفظ المعلومات الشخصية');
    }
  }

  /// Navigate to location screen
  void _navigateToLocation() {
    if (kDebugMode) {
      print('PersonalInfoNotifier: Navigating to location screen');
    }

    ref
        .read(navigationProvider.notifier)
        .navigateToReplacement(AppRoutes.location);
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Clear messages
  void clearMessages() {
    state = state.clearMessages();
  }

  /// Reset form
  void resetForm() {
    state = const PersonalInfoState();
    birthdayYearController.clear();

    if (kDebugMode) {
      print('PersonalInfoNotifier: Form reset');
    }
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for birth year
@riverpod
String userBirthdayYear(UserBirthdayYearRef ref) {
  return ref.watch(personalInfoProvider).birthdayYear;
}

/// Provider for gender
@riverpod
String userGender(UserGenderRef ref) {
  return ref.watch(personalInfoProvider).gender;
}

/// Provider for nationality
@riverpod
String userNationality(UserNationalityRef ref) {
  return ref.watch(personalInfoProvider).nationality;
}

/// Provider for personal info loading state
@riverpod
bool isPersonalInfoLoading(IsPersonalInfoLoadingRef ref) {
  return ref.watch(personalInfoProvider).isLoading;
}

/// Provider for personal info saving state
@riverpod
bool isPersonalInfoSaving(IsPersonalInfoSavingRef ref) {
  return ref.watch(personalInfoProvider).isSaving;
}

/// Provider for form validation
@riverpod
bool isPersonalInfoFormValid(IsPersonalInfoFormValidRef ref) {
  return ref.watch(personalInfoProvider).isFormValid;
}

/// Provider for personal info error message
@riverpod
String? personalInfoErrorMessage(PersonalInfoErrorMessageRef ref) {
  return ref.watch(personalInfoProvider).errorMessage;
}

/// Available genders list
@riverpod
List<String> availableGenders(AvailableGendersRef ref) {
  return ['Male', 'Female'];
}

/// Available nationalities list
@riverpod
List<String> availableNationalities(AvailableNationalitiesRef ref) {
  return [
    'Saudi',
    'Emirati',
    'Kuwaiti',
    'Qatari',
    'Bahraini',
    'Omani',
    'Yemeni',
    'Egyptian',
    'Jordanian',
    'Lebanese',
    'Syrian',
    'Iraqi',
    'Palestinian',
    'Moroccan',
    'Tunisian',
    'Algerian',
    'Libyan',
    'Sudanese',
    'Somali',
    'Mauritanian',
    'Djiboutian',
    'Other',
  ];
}
