/// Personal Info Repository Implementation
///
/// Concrete implementation of PersonalInfoRepository interface
/// Handles personal info data operations using FirebaseService for data persistence
library personal_info_repository_impl;

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../domain/entities/personal_info_entity.dart';
import '../../domain/repositories/personal_info_repository.dart';
import '../../../../shared/services/firebase_service.dart';

/// Personal Info Repository Implementation
///
/// Implements personal info data operations using FirebaseService for Firestore operations
/// Handles user profile data, validation, and persistence
class PersonalInfoRepositoryImpl implements PersonalInfoRepository {
  final FirebaseService _firebaseService;

  /// Constructor with dependency injection
  ///
  /// @param firebaseService Service for Firestore operations
  PersonalInfoRepositoryImpl({
    required FirebaseService firebaseService,
  }) : _firebaseService = firebaseService;

  @override
  Future<PersonalInfoEntity?> getPersonalInfo(String userId) async {
    try {
      if (kDebugMode) {
        print(
            'PersonalInfoRepository: Getting personal info for user: $userId');
      }

      final doc = await _firebaseService.getDocument('users', userId);

      if (!doc.exists) {
        if (kDebugMode) {
          print('PersonalInfoRepository: User document not found');
        }
        return null;
      }

      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) {
        if (kDebugMode) {
          print('PersonalInfoRepository: No data found for user');
        }
        return null;
      }

      final personalInfo = PersonalInfoEntity(
        gender: data['gender'] ?? '',
        birthdayYear: data['birthdayYear'] ?? '',
        nationality: data['nationality'] ?? '',
      );

      if (kDebugMode) {
        print('PersonalInfoRepository: Personal info retrieved successfully');
      }

      return personalInfo;
    } catch (e) {
      if (kDebugMode) {
        print('PersonalInfoRepository: Error getting personal info: $e');
      }
      throw PersonalInfoException('Failed to get personal info: $e');
    }
  }

  @override
  Future<void> updatePersonalInfo(
      String userId, PersonalInfoEntity personalInfo) async {
    try {
      if (kDebugMode) {
        print(
            'PersonalInfoRepository: Updating personal info for user: $userId');
      }

      // Validate personal info before saving
      final validation = validatePersonalInfo(personalInfo);
      if (!validation.isValid) {
        throw PersonalInfoException(
            'Invalid personal info: ${validation.errorMessage}');
      }

      final data = {
        'gender': personalInfo.gender,
        'birthdayYear': personalInfo.birthdayYear,
        'nationality': personalInfo.nationality,
      };

      await _firebaseService.updateDocument('users', userId, data);

      if (kDebugMode) {
        print('PersonalInfoRepository: Personal info updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PersonalInfoRepository: Error updating personal info: $e');
      }
      throw PersonalInfoException('Failed to update personal info: $e');
    }
  }

  @override
  Future<void> updateGender(String userId, String gender) async {
    try {
      if (kDebugMode) {
        print('PersonalInfoRepository: Updating gender for user: $userId');
      }

      await _firebaseService
          .updateDocument('users', userId, {'gender': gender});

      if (kDebugMode) {
        print('PersonalInfoRepository: Gender updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PersonalInfoRepository: Error updating gender: $e');
      }
      throw PersonalInfoException('Failed to update gender: $e');
    }
  }

  @override
  Future<void> updateBirthYear(String userId, String birthYear) async {
    try {
      if (kDebugMode) {
        print('PersonalInfoRepository: Updating birth year for user: $userId');
      }

      await _firebaseService
          .updateDocument('users', userId, {'birthdayYear': birthYear});

      if (kDebugMode) {
        print('PersonalInfoRepository: Birth year updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PersonalInfoRepository: Error updating birth year: $e');
      }
      throw PersonalInfoException('Failed to update birth year: $e');
    }
  }

  @override
  Future<void> updateNationality(String userId, String nationality) async {
    try {
      if (kDebugMode) {
        print('PersonalInfoRepository: Updating nationality for user: $userId');
      }

      await _firebaseService
          .updateDocument('users', userId, {'nationality': nationality});

      if (kDebugMode) {
        print('PersonalInfoRepository: Nationality updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PersonalInfoRepository: Error updating nationality: $e');
      }
      throw PersonalInfoException('Failed to update nationality: $e');
    }
  }

  @override
  Future<bool> isPersonalInfoComplete(String userId) async {
    try {
      final personalInfo = await getPersonalInfo(userId);
      return personalInfo?.isComplete ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('PersonalInfoRepository: Error checking completion: $e');
      }
      throw PersonalInfoException(
          'Failed to check personal info completion: $e');
    }
  }

  @override
  PersonalInfoValidationResult validatePersonalInfo(
      PersonalInfoEntity personalInfo) {
    return personalInfo.validate();
  }
}
