/// User ID Generator Service
///
/// Domain service for generating unique user IDs
/// Provides business logic for creating unique identifiers
library user_id_generator_service;

import 'dart:math';
import 'package:flutter/foundation.dart';

/// Abstract interface for user ID generation service
abstract class UserIdGeneratorService {
  /// Generate a unique 6-character user ID
  ///
  /// @param checkExistence Function to check if a user ID already exists
  /// @return A unique 6-character user ID
  Future<String> generateUniqueUserId(Future<bool> Function(String) checkExistence);
}

/// Implementation of UserIdGeneratorService
///
/// Generates unique 6-character alphanumeric user IDs
class UserIdGeneratorServiceImpl implements UserIdGeneratorService {
  /// Maximum number of attempts to generate a unique ID
  static const int maxAttempts = 10;

  @override
  Future<String> generateUniqueUserId(Future<bool> Function(String) checkExistence) async {
    int attempts = 0;

    while (attempts < maxAttempts) {
      final userId = _generateRandom6CharId();
      
      if (kDebugMode) {
        print('UserIdGeneratorService: Generated user ID: $userId (attempt ${attempts + 1})');
      }

      // Check if this ID already exists
      final exists = await checkExistence(userId);
      
      if (!exists) {
        if (kDebugMode) {
          print('UserIdGeneratorService: User ID $userId is unique');
        }
        return userId;
      }

      if (kDebugMode) {
        print('UserIdGeneratorService: User ID $userId already exists, generating new one');
      }

      attempts++;
    }

    // If we couldn't generate a unique ID after maxAttempts, fall back to timestamp-based ID
    final fallbackId = _generateTimestampBasedId();
    if (kDebugMode) {
      print('UserIdGeneratorService: Using fallback timestamp-based ID: $fallbackId');
    }
    return fallbackId;
  }

  /// Generate a random 6-character alphanumeric ID
  ///
  /// Uses lowercase letters and numbers for better readability
  /// Excludes confusing characters like 0, o, 1, l
  ///
  /// @return A 6-character random string
  String _generateRandom6CharId() {
    // Exclude confusing characters: 0, o, 1, l
    const String chars = 'abcdefghijkmnpqrstuvwxyz23456789';
    final Random random = Random();
    
    return String.fromCharCodes(
      Iterable.generate(6, (_) => chars.codeUnitAt(random.nextInt(chars.length)))
    );
  }

  /// Generate a timestamp-based fallback ID
  ///
  /// Used as a fallback when unique 6-char ID generation fails
  /// Combines timestamp and random number to ensure uniqueness
  ///
  /// @return A timestamp-based unique ID (6 characters)
  String _generateTimestampBasedId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999);
    
    // Take last 3 digits of timestamp + 3 random digits
    final timestampPart = timestamp.toString().substring(timestamp.toString().length - 3);
    final randomPart = random.toString().padLeft(3, '0');
    
    return '$timestampPart$randomPart';
  }
}
