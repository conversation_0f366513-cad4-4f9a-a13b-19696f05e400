/// Login Request Entity
///
/// Represents a login request in the domain layer
/// This is a pure business entity without any framework dependencies
library login_request_entity;

/// Login Request Entity
///
/// Contains information needed to initiate a login request
/// Used by use cases to handle authentication operations
class LoginRequest {
  /// Mobile number for authentication
  final String mobileNumber;

  /// Country code for the mobile number
  final String countryCode;

  /// Creates a LoginRequest
  ///
  /// @param mobileNumber The mobile number for authentication
  /// @param countryCode The country code for the mobile number
  const LoginRequest({
    required this.mobileNumber,
    required this.countryCode,
  });

  /// Creates a copy of the request with updated values
  ///
  /// @param mobileNumber New mobile number
  /// @param countryCode New country code
  /// @return New LoginRequest instance
  LoginRequest copyWith({
    String? mobileNumber,
    String? countryCode,
  }) {
    return LoginRequest(
      mobileNumber: mobileNumber ?? this.mobileNumber,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginRequest &&
        other.mobileNumber == mobileNumber &&
        other.countryCode == countryCode;
  }

  @override
  int get hashCode => mobileNumber.hashCode ^ countryCode.hashCode;

  @override
  String toString() {
    return 'LoginRequest(mobileNumber: $mobileNumber, countryCode: $countryCode)';
  }
}
