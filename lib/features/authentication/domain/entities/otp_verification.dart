/// OTP Verification Entity
///
/// Represents an OTP verification request in the domain layer
/// This is a pure business entity without any framework dependencies
library otp_verification_entity;

/// OTP Verification Entity
///
/// Contains information needed to verify an OTP code
/// Used by use cases to handle OTP verification operations
class OtpVerification {
  /// Mobile number that received the OTP
  final String mobileNumber;

  /// OTP code to verify
  final String otpCode;

  /// User ID associated with the verification (optional during verification)
  final String userId;

  /// Creates an OtpVerification
  ///
  /// @param mobileNumber The mobile number that received the OTP
  /// @param otpCode The OTP code to verify
  /// @param userId The user ID associated with the verification (optional)
  const OtpVerification({
    required this.mobileNumber,
    required this.otpCode,
    this.userId = '',
  });

  /// Creates a copy of the verification with updated values
  ///
  /// @param mobileNumber New mobile number
  /// @param otpCode New OTP code
  /// @param userId New user ID
  /// @return New OtpVerification instance
  OtpVerification copyWith({
    String? mobileNumber,
    String? otpCode,
    String? userId,
  }) {
    return OtpVerification(
      mobileNumber: mobileNumber ?? this.mobileNumber,
      otpCode: otpCode ?? this.otpCode,
      userId: userId ?? this.userId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OtpVerification &&
        other.mobileNumber == mobileNumber &&
        other.otpCode == otpCode &&
        other.userId == userId;
  }

  @override
  int get hashCode =>
      mobileNumber.hashCode ^ otpCode.hashCode ^ userId.hashCode;

  @override
  String toString() {
    return 'OtpVerification(mobileNumber: $mobileNumber, otpCode: $otpCode, userId: $userId)';
  }
}
