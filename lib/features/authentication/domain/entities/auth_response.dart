/// Authentication Response Entity
///
/// Represents the response from authentication operations in the domain layer
/// This is a pure business entity without any framework dependencies
library auth_response_entity;

/// Authentication Response Entity
///
/// Contains the result of authentication operations
/// Used by use cases to return authentication results
class AuthResponse {
  /// Whether the authentication was successful
  final bool isSuccess;

  /// User ID if authentication was successful
  final String? userId;

  /// Error message if authentication failed
  final String? errorMessage;

  /// Session token for authenticated user
  final String? sessionToken;

  /// Whether this is a new user registration
  final bool isNewUser;

  /// Creates an AuthResponse
  ///
  /// @param isSuccess Whether the authentication was successful
  /// @param userId User ID if authentication was successful
  /// @param errorMessage Error message if authentication failed
  /// @param sessionToken Session token for authenticated user
  /// @param isNewUser Whether this is a new user registration
  const AuthResponse({
    required this.isSuccess,
    this.userId,
    this.errorMessage,
    this.sessionToken,
    this.isNewUser = false,
  });

  /// Creates a successful authentication response
  ///
  /// @param userId The authenticated user's ID
  /// @param sessionToken The session token
  /// @param isNewUser Whether this is a new user
  /// @return AuthResponse with success state
  factory AuthResponse.success({
    required String userId,
    String? sessionToken,
    bool isNewUser = false,
  }) {
    return AuthResponse(
      isSuccess: true,
      userId: userId,
      sessionToken: sessionToken,
      isNewUser: isNewUser,
    );
  }

  /// Creates a failed authentication response
  ///
  /// @param errorMessage The error message
  /// @return AuthResponse with failure state
  factory AuthResponse.failure(String errorMessage) {
    return AuthResponse(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  /// Creates a copy of the response with updated values
  ///
  /// @param isSuccess New success status
  /// @param userId New user ID
  /// @param errorMessage New error message
  /// @param sessionToken New session token
  /// @param isNewUser New user status
  /// @return New AuthResponse instance
  AuthResponse copyWith({
    bool? isSuccess,
    String? userId,
    String? errorMessage,
    String? sessionToken,
    bool? isNewUser,
  }) {
    return AuthResponse(
      isSuccess: isSuccess ?? this.isSuccess,
      userId: userId ?? this.userId,
      errorMessage: errorMessage ?? this.errorMessage,
      sessionToken: sessionToken ?? this.sessionToken,
      isNewUser: isNewUser ?? this.isNewUser,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResponse &&
        other.isSuccess == isSuccess &&
        other.userId == userId &&
        other.errorMessage == errorMessage &&
        other.sessionToken == sessionToken &&
        other.isNewUser == isNewUser;
  }

  @override
  int get hashCode {
    return isSuccess.hashCode ^
        userId.hashCode ^
        errorMessage.hashCode ^
        sessionToken.hashCode ^
        isNewUser.hashCode;
  }

  @override
  String toString() {
    return 'AuthResponse(isSuccess: $isSuccess, userId: $userId, '
           'errorMessage: $errorMessage, sessionToken: $sessionToken, '
           'isNewUser: $isNewUser)';
  }
}
