/// Login User Use Case
///
/// Business logic for user login operations
/// Handles the complete login flow including OTP sending and verification
library login_user_usecase;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/authentication/domain/entities/login_request.dart';
import 'package:towasl/features/authentication/domain/entities/otp_verification.dart';
import 'package:towasl/features/authentication/domain/entities/auth_response.dart';
import 'package:towasl/features/authentication/domain/repositories/auth_repository.dart';
import 'package:towasl/features/authentication/domain/usecases/send_otp_usecase.dart';
import 'package:towasl/features/authentication/domain/usecases/verify_otp_usecase.dart';

/// Use case for complete user login operations
///
/// Orchestrates the login flow including OTP sending and verification
/// Provides high-level login operations for the presentation layer
class LoginUserUseCase {
  /// Authentication repository for data operations
  final AuthRepository _authRepository;

  /// Use case for sending OTP
  late final SendOtpUseCase _sendOtpUseCase;

  /// Use case for verifying OTP
  late final VerifyOtpUseCase _verifyOtpUseCase;

  /// Creates a LoginUserUseCase
  ///
  /// @param authRepository Repository for authentication operations
  LoginUserUseCase(this._authRepository) {
    _sendOtpUseCase = SendOtpUseCase(_authRepository);
    _verifyOtpUseCase = VerifyOtpUseCase(_authRepository);
  }

  /// Initiate login by sending OTP
  ///
  /// Starts the login process by sending an OTP to the mobile number
  ///
  /// @param mobileNumber The mobile number to send OTP to
  /// @param countryCode The country code for the mobile number
  /// @return A Future that resolves to an AuthResponse
  Future<AuthResponse> initiateLogin(
      String mobileNumber, String countryCode) async {
    try {
      if (kDebugMode) {
        print('LoginUserUseCase: Initiating login for $mobileNumber');
      }

      final loginRequest = LoginRequest(
        mobileNumber: mobileNumber,
        countryCode: countryCode,
      );

      final result = await _sendOtpUseCase.execute(loginRequest);

      if (kDebugMode) {
        print(
            'LoginUserUseCase: Login initiation result - Success: ${result.isSuccess}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error initiating login - $e');
      }
      return AuthResponse.failure(
          'Failed to initiate login. Please try again.');
    }
  }

  /// Complete login by verifying OTP
  ///
  /// Completes the login process by verifying the OTP code
  ///
  /// @param mobileNumber The mobile number that received the OTP
  /// @param otpCode The OTP code to verify
  /// @param userId The user ID for verification
  /// @return A Future that resolves to an AuthResponse
  Future<AuthResponse> completeLogin(
      String mobileNumber, String otpCode, String userId) async {
    try {
      if (kDebugMode) {
        print('LoginUserUseCase: Completing login for $mobileNumber');
      }

      final otpVerification = OtpVerification(
        mobileNumber: mobileNumber,
        otpCode: otpCode,
        userId: userId,
      );

      final result = await _verifyOtpUseCase.execute(otpVerification);

      if (kDebugMode) {
        print(
            'LoginUserUseCase: Login completion result - Success: ${result.isSuccess}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error completing login - $e');
      }
      return AuthResponse.failure(
          'Failed to complete login. Please try again.');
    }
  }

  /// Resend OTP
  ///
  /// Resends the OTP code to the mobile number
  ///
  /// @param mobileNumber The mobile number to resend OTP to
  /// @return A Future that resolves to an AuthResponse
  Future<AuthResponse> resendOtp(String mobileNumber) async {
    try {
      if (kDebugMode) {
        print('LoginUserUseCase: Resending OTP for $mobileNumber');
      }

      final result = await _authRepository.resendOtp(mobileNumber);

      if (kDebugMode) {
        print(
            'LoginUserUseCase: Resend OTP result - Success: ${result.isSuccess}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error resending OTP - $e');
      }
      return AuthResponse.failure('Failed to resend OTP. Please try again.');
    }
  }

  /// Sign out user
  ///
  /// Signs out the current user and clears authentication data
  ///
  /// @return A Future that resolves to an AuthResponse
  Future<AuthResponse> signOut() async {
    try {
      if (kDebugMode) {
        print('LoginUserUseCase: Signing out user');
      }

      final result = await _authRepository.signOut();

      if (kDebugMode) {
        print(
            'LoginUserUseCase: Sign out result - Success: ${result.isSuccess}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error signing out - $e');
      }
      return AuthResponse.failure('Failed to sign out. Please try again.');
    }
  }

  /// Check authentication status
  ///
  /// Checks if the user is currently authenticated
  ///
  /// @return A Future that resolves to true if authenticated, false otherwise
  Future<bool> isAuthenticated() async {
    try {
      return await _authRepository.isAuthenticated();
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error checking authentication status - $e');
      }
      return false;
    }
  }

  /// Get current user ID
  ///
  /// Retrieves the current authenticated user's ID
  ///
  /// @return A Future that resolves to the user ID if authenticated, null otherwise
  Future<String?> getCurrentUserId() async {
    try {
      return await _authRepository.getCurrentUserId();
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error getting current user ID - $e');
      }
      return null;
    }
  }

  /// Save authentication data
  ///
  /// Saves authentication data (user ID and session token) to local storage
  ///
  /// @param userId The user ID to save
  /// @param sessionToken The session token to save
  /// @return A Future that completes when data is saved
  Future<void> saveAuthenticationData(
      String userId, String sessionToken) async {
    try {
      if (kDebugMode) {
        print('LoginUserUseCase: Saving authentication data for user: $userId');
      }

      await _authRepository.saveAuthData(userId, sessionToken);

      if (kDebugMode) {
        print('LoginUserUseCase: Authentication data saved successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LoginUserUseCase: Error saving authentication data - $e');
      }
      rethrow;
    }
  }
}
