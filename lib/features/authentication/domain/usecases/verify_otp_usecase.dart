/// Verify OTP Use Case
///
/// Business logic for verifying OTP code
/// <PERSON>les validation and delegates to repository for data operations
library verify_otp_usecase;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/authentication/domain/entities/otp_verification.dart';
import 'package:towasl/features/authentication/domain/entities/auth_response.dart';
import 'package:towasl/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for verifying OTP code
///
/// Encapsulates the business logic for OTP verification
/// Validates input and coordinates with repository
class VerifyOtpUseCase {
  /// Authentication repository for data operations
  final AuthRepository _authRepository;

  /// Creates a VerifyOtpUseCase
  ///
  /// @param authRepository Repository for authentication operations
  const VerifyOtpUseCase(this._authRepository);

  /// Execute the verify OTP operation
  ///
  /// Validates the OTP verification request and verifies the code
  ///
  /// @param otpVerification The OTP verification request
  /// @return A Future that resolves to an AuthResponse
  Future<AuthResponse> execute(OtpVerification otpVerification) async {
    try {
      if (kDebugMode) {
        print(
            'VerifyOtpUseCase: Executing OTP verification for ${otpVerification.mobileNumber}');
      }

      // Validate OTP code format
      final validationError = _validateOtpCode(otpVerification.otpCode);
      if (validationError != null) {
        if (kDebugMode) {
          print('VerifyOtpUseCase: Validation failed - $validationError');
        }
        return AuthResponse.failure(validationError);
      }

      // Validate mobile number
      if (otpVerification.mobileNumber.isEmpty) {
        if (kDebugMode) {
          print('VerifyOtpUseCase: Mobile number is required');
        }
        return AuthResponse.failure('Mobile number is required');
      }

      // Note: User ID is not required for OTP verification
      // It will be generated/retrieved during the verification process

      // Verify OTP through repository
      final result = await _authRepository.verifyOtp(otpVerification);

      if (kDebugMode) {
        print(
            'VerifyOtpUseCase: OTP verification result - Success: ${result.isSuccess}');
      }

      // Save authentication data if verification successful
      if (result.isSuccess && result.userId != null) {
        await _authRepository.saveAuthData(result.userId!, result.sessionToken);
        if (kDebugMode) {
          print('VerifyOtpUseCase: Authentication data saved');
        }
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('VerifyOtpUseCase: Error verifying OTP - $e');
      }
      return AuthResponse.failure('Failed to verify OTP. Please try again.');
    }
  }

  /// Validate OTP code format
  ///
  /// @param otpCode The OTP code to validate
  /// @return Error message if invalid, null if valid
  String? _validateOtpCode(String otpCode) {
    if (otpCode.isEmpty) {
      return 'OTP code is required';
    }

    // Remove any whitespace
    final cleanOtp = otpCode.replaceAll(RegExp(r'\s'), '');

    // Check if it's exactly 4 digits (based on current implementation)
    if (cleanOtp.length != 4) {
      return 'OTP must be 4 digits';
    }

    // Check if it contains only digits
    if (!RegExp(r'^\d{4}$').hasMatch(cleanOtp)) {
      return 'OTP must contain only numbers';
    }

    return null;
  }
}
