/// Authentication Repository Interface
///
/// Defines the contract for authentication data operations
/// This interface is implemented by the data layer
library auth_repository_interface;

import 'package:towasl/features/authentication/domain/entities/login_request.dart';
import 'package:towasl/features/authentication/domain/entities/otp_verification.dart';
import 'package:towasl/features/authentication/domain/entities/auth_response.dart';

/// Abstract repository for authentication operations
///
/// Defines the interface for authentication data operations
/// Implementations should handle data source specifics (Firebase, API, etc.)
abstract class AuthRepository {
  /// Send OTP to mobile number
  ///
  /// Initiates the authentication process by sending an OTP to the provided mobile number
  ///
  /// @param loginRequest The login request containing mobile number and country code
  /// @return A Future that resolves to an AuthResponse indicating success or failure
  Future<AuthResponse> sendOtp(LoginRequest loginRequest);

  /// Verify OTP code
  ///
  /// Verifies the OTP code entered by the user
  ///
  /// @param otpVerification The OTP verification request
  /// @return A Future that resolves to an AuthResponse with authentication result
  Future<AuthResponse> verifyOtp(OtpVerification otpVerification);

  /// Resend OTP code
  ///
  /// Resends the OTP code to the mobile number
  ///
  /// @param mobileNumber The mobile number to resend OTP to
  /// @return A Future that resolves to an AuthResponse indicating success or failure
  Future<AuthResponse> resendOtp(String mobileNumber);

  /// Sign out user
  ///
  /// Signs out the current user and clears authentication data
  ///
  /// @return A Future that resolves to an AuthResponse indicating success or failure
  Future<AuthResponse> signOut();

  /// Check if user is authenticated
  ///
  /// Checks the current authentication status
  ///
  /// @return A Future that resolves to true if user is authenticated, false otherwise
  Future<bool> isAuthenticated();

  /// Get current user ID
  ///
  /// Retrieves the current authenticated user's ID
  ///
  /// @return A Future that resolves to the user ID if authenticated, null otherwise
  Future<String?> getCurrentUserId();

  /// Get current session token
  ///
  /// Retrieves the current session token
  ///
  /// @return A Future that resolves to the session token if available, null otherwise
  Future<String?> getCurrentSessionToken();

  /// Save authentication data
  ///
  /// Saves authentication data locally for persistence
  ///
  /// @param userId The user ID to save
  /// @param sessionToken The session token to save
  /// @return A Future that resolves when data is saved
  Future<void> saveAuthData(String userId, String? sessionToken);

  /// Clear authentication data
  ///
  /// Clears all locally stored authentication data
  ///
  /// @return A Future that resolves when data is cleared
  Future<void> clearAuthData();
}
