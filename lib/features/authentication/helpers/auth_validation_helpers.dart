/// Authentication Validation Helpers
///
/// Provides validation functions specific to authentication flows
/// Includes phone number validation, OTP validation, and auth-specific checks
/// Moved to domain layer as part of Clean Architecture restructuring
library auth_validation_helpers;

import 'package:intl_phone_field/phone_number.dart';

/// Authentication-specific validation functions
class AuthValidationHelpers {
  /// Validates a Saudi phone number for authentication
  ///
  /// Checks that:
  /// - The phone number is not null
  /// - The phone number matches the Saudi format (05xxxxxxxx)
  /// - The phone number is exactly 10 digits
  ///
  /// @param value The PhoneNumber object to validate
  /// @return An error message if invalid, null otherwise
  static String? validateSaudiPhone(PhoneNumber? value) {
    if (value == null) {
      return 'مطلوب';
    }

    // Saudi phone number format: 05xxxxxxxx (10 digits total)
    final regex = RegExp(r"^05[0-9]{8}$");

    if (!regex.hasMatch(value.number)) {
      return 'اكتب رقم جوال سعودي صحيح';
    }

    return null;
  }

  /// Validates a raw phone number string
  ///
  /// @param phoneNumber The phone number string to validate
  /// @return An error message if invalid, null otherwise
  static String? validatePhoneString(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return 'مطلوب';
    }

    // Remove any non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Check Saudi format
    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return null;
    }

    // Check international format with country code
    if (cleanNumber.length == 13 && cleanNumber.startsWith('96605')) {
      return null;
    }

    return 'اكتب رقم جوال سعودي صحيح';
  }

  /// Validates an OTP code
  ///
  /// Checks that the OTP is exactly 4 digits
  ///
  /// @param otp The OTP string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateOTP(String? otp) {
    if (otp == null || otp.isEmpty) {
      return 'Required';
    }

    // Remove any whitespace
    final cleanOtp = otp.replaceAll(RegExp(r'\s'), '');

    // Check if it's exactly 4 digits
    if (cleanOtp.length != 4) {
      return "OTP must be 4 digits";
    }

    // Check if it contains only digits
    if (!RegExp(r'^\d{4}$').hasMatch(cleanOtp)) {
      return "OTP must contain only numbers";
    }

    return null;
  }

  /// Validates a verification code (generic)
  ///
  /// @param code The verification code to validate
  /// @param expectedLength The expected length of the code
  /// @return An error message if invalid, null otherwise
  static String? validateVerificationCode(String? code, int expectedLength) {
    if (code == null || code.isEmpty) {
      return 'Required';
    }

    final cleanCode = code.replaceAll(RegExp(r'\s'), '');

    if (cleanCode.length != expectedLength) {
      return "Code must be $expectedLength digits";
    }

    if (!RegExp(r'^\d+$').hasMatch(cleanCode)) {
      return "Code must contain only numbers";
    }

    return null;
  }

  /// Formats a phone number for display in authentication
  ///
  /// @param phoneNumber The raw phone number
  /// @return The formatted phone number
  static String formatPhoneForDisplay(String phoneNumber) {
    // Remove any non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Format Saudi numbers
    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return '${cleanNumber.substring(0, 3)} ${cleanNumber.substring(3, 6)} ${cleanNumber.substring(6)}';
    }

    // Format international numbers
    if (cleanNumber.length == 13 && cleanNumber.startsWith('966')) {
      final localPart = cleanNumber.substring(3);
      return '+966 ${localPart.substring(0, 2)} ${localPart.substring(2, 5)} ${localPart.substring(5)}';
    }

    return phoneNumber;
  }

  /// Normalizes a phone number for storage/comparison
  ///
  /// @param phoneNumber The phone number to normalize
  /// @return The normalized phone number
  static String normalizePhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Convert to standard format (05xxxxxxxx)
    if (cleanNumber.length == 13 && cleanNumber.startsWith('966')) {
      return '0${cleanNumber.substring(3)}';
    }

    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return cleanNumber;
    }

    return phoneNumber;
  }

  /// Checks if a phone number is in Saudi format
  ///
  /// @param phoneNumber The phone number to check
  /// @return True if it's a valid Saudi phone number
  static bool isSaudiPhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    return cleanNumber.length == 10 && cleanNumber.startsWith('05');
  }

  /// Validates that two phone numbers match
  ///
  /// @param phone1 The first phone number
  /// @param phone2 The second phone number
  /// @return True if they match after normalization
  static bool phoneNumbersMatch(String phone1, String phone2) {
    final normalized1 = normalizePhoneNumber(phone1);
    final normalized2 = normalizePhoneNumber(phone2);
    return normalized1 == normalized2;
  }

  /// Generates a masked phone number for display
  ///
  /// Shows only the last 4 digits for privacy
  ///
  /// @param phoneNumber The phone number to mask
  /// @return The masked phone number
  static String maskPhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanNumber.length >= 4) {
      final lastFour = cleanNumber.substring(cleanNumber.length - 4);
      final masked = '*' * (cleanNumber.length - 4);
      return '$masked$lastFour';
    }

    return phoneNumber;
  }
}
