/// Authentication Repository Implementation
///
/// Implements the AuthRepository interface using data sources
/// Provides concrete implementation for authentication data operations
library auth_repository_impl;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/authentication/domain/entities/login_request.dart';
import 'package:towasl/features/authentication/domain/entities/otp_verification.dart';
import 'package:towasl/features/authentication/domain/entities/auth_response.dart';
import 'package:towasl/features/authentication/domain/repositories/auth_repository.dart';
import 'package:towasl/features/authentication/data/models/login_request_model.dart';
import 'package:towasl/features/authentication/data/models/otp_verification_model.dart';
import 'package:towasl/features/authentication/data/datasources/auth_remote_datasource.dart';
import 'package:towasl/features/authentication/data/datasources/auth_local_datasource.dart';

/// Implementation of AuthRepository
///
/// Concrete implementation that uses remote and local data sources
/// to provide authentication data operations
class AuthRepositoryImpl implements AuthRepository {
  /// Remote data source for authentication operations
  final AuthRemoteDataSource _remoteDataSource;

  /// Local data source for authentication data persistence
  final AuthLocalDataSource _localDataSource;

  /// Creates an AuthRepositoryImpl
  ///
  /// @param remoteDataSource Remote data source for authentication operations
  /// @param localDataSource Local data source for authentication data persistence
  const AuthRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<AuthResponse> sendOtp(LoginRequest loginRequest) async {
    try {
      if (kDebugMode) {
        print('AuthRepository: Sending OTP for ${loginRequest.mobileNumber}');
      }

      // Convert domain entity to data model
      final loginRequestModel = LoginRequestModel.fromEntity(loginRequest);

      // Send OTP through remote data source
      final responseModel = await _remoteDataSource.sendOtp(loginRequestModel);

      // Convert data model back to domain entity
      final response = responseModel.toEntity();

      if (kDebugMode) {
        print('AuthRepository: Send OTP result - Success: ${response.isSuccess}');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error sending OTP - $e');
      }
      return AuthResponse.failure('Failed to send OTP. Please try again.');
    }
  }

  @override
  Future<AuthResponse> verifyOtp(OtpVerification otpVerification) async {
    try {
      if (kDebugMode) {
        print('AuthRepository: Verifying OTP for ${otpVerification.mobileNumber}');
      }

      // Convert domain entity to data model
      final otpVerificationModel = OtpVerificationModel.fromEntity(otpVerification);

      // Verify OTP through remote data source
      final responseModel = await _remoteDataSource.verifyOtp(otpVerificationModel);

      // Convert data model back to domain entity
      final response = responseModel.toEntity();

      if (kDebugMode) {
        print('AuthRepository: Verify OTP result - Success: ${response.isSuccess}');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error verifying OTP - $e');
      }
      return AuthResponse.failure('Failed to verify OTP. Please try again.');
    }
  }

  @override
  Future<AuthResponse> resendOtp(String mobileNumber) async {
    try {
      if (kDebugMode) {
        print('AuthRepository: Resending OTP for $mobileNumber');
      }

      // Resend OTP through remote data source
      final responseModel = await _remoteDataSource.resendOtp(mobileNumber);

      // Convert data model back to domain entity
      final response = responseModel.toEntity();

      if (kDebugMode) {
        print('AuthRepository: Resend OTP result - Success: ${response.isSuccess}');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error resending OTP - $e');
      }
      return AuthResponse.failure('Failed to resend OTP. Please try again.');
    }
  }

  @override
  Future<AuthResponse> signOut() async {
    try {
      if (kDebugMode) {
        print('AuthRepository: Signing out user');
      }

      // Sign out through remote data source
      final responseModel = await _remoteDataSource.signOut();

      // Clear local authentication data
      await _localDataSource.clearAuthData();

      // Convert data model back to domain entity
      final response = responseModel.toEntity();

      if (kDebugMode) {
        print('AuthRepository: Sign out result - Success: ${response.isSuccess}');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error signing out - $e');
      }
      return AuthResponse.failure('Failed to sign out. Please try again.');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      return await _localDataSource.isAuthenticated();
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error checking authentication status - $e');
      }
      return false;
    }
  }

  @override
  Future<String?> getCurrentUserId() async {
    try {
      return await _localDataSource.getCurrentUserId();
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error getting current user ID - $e');
      }
      return null;
    }
  }

  @override
  Future<String?> getCurrentSessionToken() async {
    try {
      return await _localDataSource.getCurrentSessionToken();
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error getting current session token - $e');
      }
      return null;
    }
  }

  @override
  Future<void> saveAuthData(String userId, String? sessionToken) async {
    try {
      if (kDebugMode) {
        print('AuthRepository: Saving authentication data for user: $userId');
      }

      await _localDataSource.saveAuthData(userId, sessionToken);

      if (kDebugMode) {
        print('AuthRepository: Authentication data saved successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error saving authentication data - $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      if (kDebugMode) {
        print('AuthRepository: Clearing authentication data');
      }

      await _localDataSource.clearAuthData();

      if (kDebugMode) {
        print('AuthRepository: Authentication data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthRepository: Error clearing authentication data - $e');
      }
      rethrow;
    }
  }
}
