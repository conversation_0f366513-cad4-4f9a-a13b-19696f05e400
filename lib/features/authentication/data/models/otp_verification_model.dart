/// OTP Verification Model
///
/// Data model for OTP verification with serialization capabilities
/// Extends the domain entity with JSON serialization for API communication
library otp_verification_model;

import 'package:towasl/features/authentication/domain/entities/otp_verification.dart';

/// OTP Verification Data Model
///
/// Extends OtpVerification entity with serialization capabilities
/// Used for converting between domain entities and API request formats
class OtpVerificationModel extends OtpVerification {
  /// Creates an OtpVerificationModel
  ///
  /// @param mobileNumber The mobile number that received the OTP
  /// @param otpCode The OTP code to verify
  /// @param userId The user ID associated with the verification
  const OtpVerificationModel({
    required super.mobileNumber,
    required super.otpCode,
    required super.userId,
  });

  /// Creates an OtpVerificationModel from domain entity
  ///
  /// @param entity The domain entity to convert
  /// @return OtpVerificationModel instance
  factory OtpVerificationModel.fromEntity(OtpVerification entity) {
    return OtpVerificationModel(
      mobileNumber: entity.mobileNumber,
      otpCode: entity.otpCode,
      userId: entity.userId,
    );
  }

  /// Creates an OtpVerificationModel from JSON
  ///
  /// @param json Map containing the OTP verification data
  /// @return OtpVerificationModel instance
  factory OtpVerificationModel.fromJson(Map<String, dynamic> json) {
    return OtpVerificationModel(
      mobileNumber: json['mobileNumber'] as String? ?? '',
      otpCode: json['otpCode'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
    );
  }

  /// Converts the model to JSON
  ///
  /// @return Map containing the OTP verification data for API calls
  Map<String, dynamic> toJson() {
    return {
      'mobileNumber': mobileNumber,
      'otpCode': otpCode,
      'userId': userId,
      'verificationCode': otpCode, // Alternative field name for API compatibility
    };
  }

  /// Converts the model to domain entity
  ///
  /// @return OtpVerification domain entity
  OtpVerification toEntity() {
    return OtpVerification(
      mobileNumber: mobileNumber,
      otpCode: otpCode,
      userId: userId,
    );
  }

  /// Creates a copy of the model with updated values
  ///
  /// @param mobileNumber New mobile number
  /// @param otpCode New OTP code
  /// @param userId New user ID
  /// @return New OtpVerificationModel instance
  @override
  OtpVerificationModel copyWith({
    String? mobileNumber,
    String? otpCode,
    String? userId,
  }) {
    return OtpVerificationModel(
      mobileNumber: mobileNumber ?? this.mobileNumber,
      otpCode: otpCode ?? this.otpCode,
      userId: userId ?? this.userId,
    );
  }

  @override
  String toString() {
    return 'OtpVerificationModel(mobileNumber: $mobileNumber, otpCode: $otpCode, userId: $userId)';
  }
}
