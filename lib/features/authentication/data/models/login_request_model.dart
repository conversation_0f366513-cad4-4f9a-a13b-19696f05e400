/// Login Request Model
///
/// Data model for login requests with serialization capabilities
/// Extends the domain entity with JSON serialization for API communication
library login_request_model;

import 'package:towasl/features/authentication/domain/entities/login_request.dart';

/// Login Request Data Model
///
/// Extends LoginRequest entity with serialization capabilities
/// Used for converting between domain entities and API request formats
class LoginRequestModel extends LoginRequest {
  /// Creates a LoginRequestModel
  ///
  /// @param mobileNumber The mobile number for authentication
  /// @param countryCode The country code for the mobile number
  const LoginRequestModel({
    required super.mobileNumber,
    required super.countryCode,
  });

  /// Creates a LoginRequestModel from domain entity
  ///
  /// @param entity The domain entity to convert
  /// @return LoginRequestModel instance
  factory LoginRequestModel.fromEntity(LoginRequest entity) {
    return LoginRequestModel(
      mobileNumber: entity.mobileNumber,
      countryCode: entity.countryCode,
    );
  }

  /// Creates a LoginRequestModel from JSON
  ///
  /// @param json Map containing the login request data
  /// @return LoginRequestModel instance
  factory LoginRequestModel.fromJson(Map<String, dynamic> json) {
    return LoginRequestModel(
      mobileNumber: json['mobileNumber'] as String? ?? '',
      countryCode: json['countryCode'] as String? ?? '',
    );
  }

  /// Converts the model to JSON
  ///
  /// @return Map containing the login request data for API calls
  Map<String, dynamic> toJson() {
    return {
      'mobileNumber': mobileNumber,
      'countryCode': countryCode,
      'phoneNumber': '$countryCode$mobileNumber', // Combined format for API
    };
  }

  /// Converts the model to domain entity
  ///
  /// @return LoginRequest domain entity
  LoginRequest toEntity() {
    return LoginRequest(
      mobileNumber: mobileNumber,
      countryCode: countryCode,
    );
  }

  /// Creates a copy of the model with updated values
  ///
  /// @param mobileNumber New mobile number
  /// @param countryCode New country code
  /// @return New LoginRequestModel instance
  @override
  LoginRequestModel copyWith({
    String? mobileNumber,
    String? countryCode,
  }) {
    return LoginRequestModel(
      mobileNumber: mobileNumber ?? this.mobileNumber,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  @override
  String toString() {
    return 'LoginRequestModel(mobileNumber: $mobileNumber, countryCode: $countryCode)';
  }
}
