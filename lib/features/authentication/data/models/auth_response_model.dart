/// Authentication Response Model
///
/// Data model for authentication responses with serialization capabilities
/// Extends the domain entity with JSON serialization for API communication
library auth_response_model;

import 'package:towasl/features/authentication/domain/entities/auth_response.dart';

/// Authentication Response Data Model
///
/// Extends AuthResponse entity with serialization capabilities
/// Used for converting between domain entities and API response formats
class AuthResponseModel extends AuthResponse {
  /// Creates an AuthResponseModel
  ///
  /// @param isSuccess Whether the authentication was successful
  /// @param userId User ID if authentication was successful
  /// @param errorMessage Error message if authentication failed
  /// @param sessionToken Session token for authenticated user
  /// @param isNewUser Whether this is a new user registration
  const AuthResponseModel({
    required super.isSuccess,
    super.userId,
    super.errorMessage,
    super.sessionToken,
    super.isNewUser = false,
  });

  /// Creates an AuthResponseModel from domain entity
  ///
  /// @param entity The domain entity to convert
  /// @return AuthResponseModel instance
  factory AuthResponseModel.fromEntity(AuthResponse entity) {
    return AuthResponseModel(
      isSuccess: entity.isSuccess,
      userId: entity.userId,
      errorMessage: entity.errorMessage,
      sessionToken: entity.sessionToken,
      isNewUser: entity.isNewUser,
    );
  }

  /// Creates an AuthResponseModel from JSON
  ///
  /// @param json Map containing the authentication response data
  /// @return AuthResponseModel instance
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) {
    return AuthResponseModel(
      isSuccess: json['isSuccess'] as bool? ?? false,
      userId: json['userId'] as String?,
      errorMessage: json['errorMessage'] as String?,
      sessionToken: json['sessionToken'] as String? ?? json['token'] as String?,
      isNewUser: json['isNewUser'] as bool? ?? false,
    );
  }

  /// Creates a successful authentication response model
  ///
  /// @param userId The authenticated user's ID
  /// @param sessionToken The session token
  /// @param isNewUser Whether this is a new user
  /// @return AuthResponseModel with success state
  factory AuthResponseModel.success({
    required String userId,
    String? sessionToken,
    bool isNewUser = false,
  }) {
    return AuthResponseModel(
      isSuccess: true,
      userId: userId,
      sessionToken: sessionToken,
      isNewUser: isNewUser,
    );
  }

  /// Creates a failed authentication response model
  ///
  /// @param errorMessage The error message
  /// @return AuthResponseModel with failure state
  factory AuthResponseModel.failure(String errorMessage) {
    return AuthResponseModel(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  /// Converts the model to JSON
  ///
  /// @return Map containing the authentication response data
  Map<String, dynamic> toJson() {
    return {
      'isSuccess': isSuccess,
      'userId': userId,
      'errorMessage': errorMessage,
      'sessionToken': sessionToken,
      'token': sessionToken, // Alternative field name for API compatibility
      'isNewUser': isNewUser,
    };
  }

  /// Converts the model to domain entity
  ///
  /// @return AuthResponse domain entity
  AuthResponse toEntity() {
    return AuthResponse(
      isSuccess: isSuccess,
      userId: userId,
      errorMessage: errorMessage,
      sessionToken: sessionToken,
      isNewUser: isNewUser,
    );
  }

  /// Creates a copy of the model with updated values
  ///
  /// @param isSuccess New success status
  /// @param userId New user ID
  /// @param errorMessage New error message
  /// @param sessionToken New session token
  /// @param isNewUser New user status
  /// @return New AuthResponseModel instance
  @override
  AuthResponseModel copyWith({
    bool? isSuccess,
    String? userId,
    String? errorMessage,
    String? sessionToken,
    bool? isNewUser,
  }) {
    return AuthResponseModel(
      isSuccess: isSuccess ?? this.isSuccess,
      userId: userId ?? this.userId,
      errorMessage: errorMessage ?? this.errorMessage,
      sessionToken: sessionToken ?? this.sessionToken,
      isNewUser: isNewUser ?? this.isNewUser,
    );
  }

  @override
  String toString() {
    return 'AuthResponseModel(isSuccess: $isSuccess, userId: $userId, '
           'errorMessage: $errorMessage, sessionToken: $sessionToken, '
           'isNewUser: $isNewUser)';
  }
}
