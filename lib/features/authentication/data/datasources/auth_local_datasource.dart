/// Authentication Local Data Source
///
/// Handles authentication data storage and retrieval from local storage
/// Provides concrete implementation for local authentication data operations
library auth_local_datasource;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/storage_service.dart';

/// Abstract interface for local authentication data source
abstract class AuthLocalDataSource {
  /// Save authentication data locally
  Future<void> saveAuthData(String userId, String? sessionToken);

  /// Get current user ID from local storage
  Future<String?> getCurrentUserId();

  /// Get current session token from local storage
  Future<String?> getCurrentSessionToken();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Clear all authentication data
  Future<void> clearAuthData();
}

/// Implementation of AuthLocalDataSource using StorageService
///
/// Handles authentication data persistence through local storage
/// Provides concrete implementation for local authentication operations
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  /// Storage service for local data operations
  final StorageService _storageService;

  /// Storage keys for authentication data
  static const String _userIdKey = 'auth_user_id';
  static const String _sessionTokenKey = 'auth_session_token';
  static const String _isAuthenticatedKey = 'auth_is_authenticated';

  /// Creates an AuthLocalDataSourceImpl
  ///
  /// @param storageService Storage service for local data operations
  const AuthLocalDataSourceImpl(this._storageService);

  @override
  Future<void> saveAuthData(String userId, String? sessionToken) async {
    try {
      if (kDebugMode) {
        print(
            'AuthLocalDataSource: Saving authentication data for user: $userId');
      }

      // Save user ID
      await _storageService.write(_userIdKey, userId);

      // Save session token if provided
      if (sessionToken != null) {
        await _storageService.write(_sessionTokenKey, sessionToken);
      }

      // Mark as authenticated
      await _storageService.write(_isAuthenticatedKey, true);

      // Also update legacy storage service for backward compatibility
      _storageService.setLoginData(userId);

      if (kDebugMode) {
        print(
            'AuthLocalDataSource: Authentication data saved successfully (including legacy storage)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthLocalDataSource: Error saving authentication data - $e');
      }
      rethrow;
    }
  }

  @override
  Future<String?> getCurrentUserId() async {
    try {
      final userId = await _storageService.read(_userIdKey);

      if (kDebugMode) {
        print('AuthLocalDataSource: Retrieved user ID: ${userId ?? "null"}');
      }

      return userId;
    } catch (e) {
      if (kDebugMode) {
        print('AuthLocalDataSource: Error getting current user ID - $e');
      }
      return null;
    }
  }

  @override
  Future<String?> getCurrentSessionToken() async {
    try {
      final sessionToken = await _storageService.read(_sessionTokenKey);

      if (kDebugMode) {
        print(
            'AuthLocalDataSource: Retrieved session token: ${sessionToken != null ? "present" : "null"}');
      }

      return sessionToken;
    } catch (e) {
      if (kDebugMode) {
        print('AuthLocalDataSource: Error getting current session token - $e');
      }
      return null;
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final isAuthenticated =
          await _storageService.read(_isAuthenticatedKey) ?? false;
      final userId = await getCurrentUserId();

      // User is authenticated if both flag is true and user ID exists
      final result = isAuthenticated && userId != null && userId.isNotEmpty;

      if (kDebugMode) {
        print('AuthLocalDataSource: Authentication status: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('AuthLocalDataSource: Error checking authentication status - $e');
      }
      return false;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      if (kDebugMode) {
        print('AuthLocalDataSource: Clearing authentication data');
      }

      // Remove all authentication-related data
      await _storageService.remove(_userIdKey);
      await _storageService.remove(_sessionTokenKey);
      await _storageService.remove(_isAuthenticatedKey);

      // Also clear legacy storage data for backward compatibility
      await _storageService.remove('loggedIn');
      await _storageService.remove('userID');

      if (kDebugMode) {
        print(
            'AuthLocalDataSource: Authentication data cleared successfully (including legacy storage)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthLocalDataSource: Error clearing authentication data - $e');
      }
      rethrow;
    }
  }
}
