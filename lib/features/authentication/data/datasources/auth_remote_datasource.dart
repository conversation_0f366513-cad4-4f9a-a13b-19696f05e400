/// Authentication Remote Data Source
///
/// Handles authentication operations with remote services (Firebase, API)
/// Provides concrete implementation for remote authentication data operations
library auth_remote_datasource;

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:towasl/features/authentication/data/models/login_request_model.dart';
import 'package:towasl/features/authentication/data/models/otp_verification_model.dart';
import 'package:towasl/features/authentication/data/models/auth_response_model.dart';
import 'package:towasl/features/authentication/domain/services/user_id_generator_service.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// Abstract interface for remote authentication data source
abstract class AuthRemoteDataSource {
  /// Send OTP to mobile number
  Future<AuthResponseModel> sendOtp(LoginRequestModel loginRequest);

  /// Verify OTP code
  Future<AuthResponseModel> verifyOtp(OtpVerificationModel otpVerification);

  /// Resend OTP code
  Future<AuthResponseModel> resendOtp(String mobileNumber);

  /// Sign out user
  Future<AuthResponseModel> signOut();
}

/// Implementation of AuthRemoteDataSource using Firebase
///
/// Handles authentication operations through Firebase services
/// Provides concrete implementation for remote authentication operations
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  /// Firebase service for authentication operations
  final FirebaseService _firebaseService;

  /// User ID generator service for creating unique user IDs
  final UserIdGeneratorService _userIdGeneratorService;

  /// Creates an AuthRemoteDataSourceImpl
  ///
  /// @param firebaseService Firebase service for authentication operations
  /// @param userIdGeneratorService Service for generating unique user IDs
  const AuthRemoteDataSourceImpl(
    this._firebaseService,
    this._userIdGeneratorService,
  );

  @override
  Future<AuthResponseModel> sendOtp(LoginRequestModel loginRequest) async {
    try {
      if (kDebugMode) {
        print(
            'AuthRemoteDataSource: Sending OTP to ${loginRequest.mobileNumber}');
      }

      // First check if a user with this mobile number already exists
      if (kDebugMode) {
        print(
            'AuthRemoteDataSource: Checking for existing user with mobile: ${loginRequest.mobileNumber}');
      }

      final existingUserId =
          await _getUserIdByMobile(loginRequest.mobileNumber);

      String userId;
      bool isNewUser;

      if (existingUserId != null) {
        // User exists, use existing user ID
        userId = existingUserId;
        isNewUser = false;

        if (kDebugMode) {
          print(
              'AuthRemoteDataSource: ✅ EXISTING USER FOUND with mobile ${loginRequest.mobileNumber}, userId: $userId');
        }
      } else {
        // New user, generate a unique user ID
        userId = await _userIdGeneratorService.generateUniqueUserId(
          (id) => _checkUserIdExists(id),
        );
        isNewUser = true;

        if (kDebugMode) {
          print(
              'AuthRemoteDataSource: ❌ NO EXISTING USER with mobile ${loginRequest.mobileNumber}, generated NEW userId: $userId');
        }
      }

      // In a real implementation, this would call Firebase Auth or SMS service
      // For now, we simulate the OTP sending process
      await Future.delayed(const Duration(milliseconds: 500));

      if (kDebugMode) {
        print(
            'AuthRemoteDataSource: OTP sent successfully, userId: $userId, isNewUser: $isNewUser');
      }

      return AuthResponseModel.success(
        userId: userId,
        isNewUser: isNewUser,
      );
    } catch (e) {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Error sending OTP - $e');
      }
      return AuthResponseModel.failure('Failed to send OTP. Please try again.');
    }
  }

  @override
  Future<AuthResponseModel> verifyOtp(
      OtpVerificationModel otpVerification) async {
    try {
      if (kDebugMode) {
        print(
            'AuthRemoteDataSource: Verifying OTP for ${otpVerification.mobileNumber}');
      }

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 800));

      // For demo purposes, any 4-digit code is accepted
      // In a real app, this would validate against Firebase or server
      if (otpVerification.otpCode.length == 4) {
        // Generate session token
        final sessionToken = _generateSessionToken();

        // Check if user exists to determine isNewUser status and get proper user ID
        final existingUserId =
            await _getUserIdByMobile(otpVerification.mobileNumber);

        String userId;
        bool isNewUser;

        if (existingUserId != null) {
          // User exists, use existing user ID
          userId = existingUserId;
          isNewUser = false;
        } else {
          // New user, generate a unique user ID
          userId = await _userIdGeneratorService.generateUniqueUserId(
            (id) => _checkUserIdExists(id),
          );
          isNewUser = true;
        }

        if (kDebugMode) {
          print('AuthRemoteDataSource: OTP verified successfully');
          print('AuthRemoteDataSource: User ID for verification: $userId');
          print(
              'AuthRemoteDataSource: User status for verification: ${isNewUser ? "new user" : "existing user"}');
        }

        return AuthResponseModel.success(
          userId: userId,
          sessionToken: sessionToken,
          isNewUser: isNewUser,
        );
      } else {
        if (kDebugMode) {
          print('AuthRemoteDataSource: Invalid OTP code');
        }
        return AuthResponseModel.failure('Invalid OTP code');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Error verifying OTP - $e');
      }
      return AuthResponseModel.failure(
          'Failed to verify OTP. Please try again.');
    }
  }

  @override
  Future<AuthResponseModel> resendOtp(String mobileNumber) async {
    try {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Resending OTP to $mobileNumber');
      }

      // First check if a user with this mobile number already exists
      final existingUserId = await _getUserIdByMobile(mobileNumber);

      String userId;
      bool isNewUser;

      if (existingUserId != null) {
        // User exists, use existing user ID
        userId = existingUserId;
        isNewUser = false;

        if (kDebugMode) {
          print(
              'AuthRemoteDataSource: Resending OTP for existing user with mobile $mobileNumber, userId: $userId');
        }
      } else {
        // New user, generate a unique user ID
        userId = await _userIdGeneratorService.generateUniqueUserId(
          (id) => _checkUserIdExists(id),
        );
        isNewUser = true;

        if (kDebugMode) {
          print(
              'AuthRemoteDataSource: Resending OTP for new user with mobile $mobileNumber, generated userId: $userId');
        }
      }

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      if (kDebugMode) {
        print(
            'AuthRemoteDataSource: OTP resent successfully, userId: $userId, isNewUser: $isNewUser');
      }

      return AuthResponseModel.success(
        userId: userId,
        isNewUser: isNewUser,
      );
    } catch (e) {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Error resending OTP - $e');
      }
      return AuthResponseModel.failure(
          'Failed to resend OTP. Please try again.');
    }
  }

  @override
  Future<AuthResponseModel> signOut() async {
    try {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Signing out user');
      }

      // In a real implementation, this would call Firebase Auth signOut
      await Future.delayed(const Duration(milliseconds: 300));

      if (kDebugMode) {
        print('AuthRemoteDataSource: User signed out successfully');
      }

      return AuthResponseModel.success(
        userId: '',
        isNewUser: false,
      );
    } catch (e) {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Error signing out - $e');
      }
      return AuthResponseModel.failure('Failed to sign out. Please try again.');
    }
  }

  /// Check if a user ID already exists in the database
  ///
  /// @param userId The user ID to check
  /// @return True if the user ID exists, false otherwise
  Future<bool> _checkUserIdExists(String userId) async {
    try {
      final userDoc = await _firebaseService.getDocument('users', userId);
      return userDoc.exists;
    } catch (e) {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Error checking user ID existence - $e');
      }
      // If there's an error checking, assume it doesn't exist to avoid infinite loops
      return false;
    }
  }

  /// Get user ID by mobile number
  ///
  /// @param mobile The mobile number to search for
  /// @return The user ID if found, null otherwise
  Future<String?> _getUserIdByMobile(String mobile) async {
    try {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Searching for user with mobile: $mobile');
      }

      // Normalize the mobile number to core format (5xxxxxxxx)
      final normalizedMobile = _normalizeMobileToCore(mobile);
      const countryCode = '+966'; // Default Saudi Arabia country code

      if (kDebugMode) {
        print('AuthRemoteDataSource: Normalized mobile: $normalizedMobile');
        print(
            'AuthRemoteDataSource: Searching with mobile="$normalizedMobile" and countryCode="$countryCode"');
      }

      // Search for user with both mobile and countryCode fields
      var snapshot = await _firebaseService.firestore
          .collection("users")
          .where("mobile", isEqualTo: normalizedMobile)
          .where("countryCode", isEqualTo: countryCode)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        var userData = snapshot.docs.first.data();
        final userId = userData['user_id'] as String?;
        final storedMobile = userData['mobile'] as String?;
        final storedCountryCode = userData['countryCode'] as String?;

        if (kDebugMode) {
          print(
              'AuthRemoteDataSource: ✅ MATCH FOUND! mobile="$storedMobile", countryCode="$storedCountryCode", userId: $userId');
        }

        return userId;
      } else {
        if (kDebugMode) {
          print(
              'AuthRemoteDataSource: ❌ No match for mobile="$normalizedMobile" and countryCode="$countryCode"');
        }
      }

      // If no results with mobile+countryCode, try legacy email field (for backward compatibility)
      final searchFormats = _generateMobileSearchFormats(mobile);
      for (final format in searchFormats) {
        var snapshot = await _firebaseService.firestore
            .collection("users")
            .where("email", isEqualTo: format)
            .limit(1)
            .get();

        if (snapshot.docs.isNotEmpty) {
          var userData = snapshot.docs.first.data();
          final userId = userData['user_id'] as String?;

          if (kDebugMode) {
            print(
                'AuthRemoteDataSource: Found user with legacy email format "$format", userId: $userId');
          }

          return userId;
        }
      }

      if (kDebugMode) {
        print(
            'AuthRemoteDataSource: No user found with mobile="$normalizedMobile" and countryCode="$countryCode"');
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('AuthRemoteDataSource: Error searching for user by mobile - $e');
      }
      return null;
    }
  }

  /// Normalize mobile number to core format for searching
  ///
  /// Converts various mobile number formats to the core digits format (5xxxxxxxx)
  /// Examples:
  /// - +9660564254457 -> 564254457
  /// - 9660564254457 -> 564254457
  /// - 0564254457 -> 564254457
  /// - 564254457 -> 564254457
  ///
  /// @param mobile The input mobile number in any format
  /// @return The normalized mobile number (core digits only)
  String _normalizeMobileToCore(String mobile) {
    // Remove all non-digit characters
    final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

    // Handle different formats
    if (cleanMobile.startsWith('966') && cleanMobile.length >= 12) {
      // International format: 9660564254457 -> 564254457
      // Remove '966' country code first, then check if it starts with '05'
      final withoutCountryCode = cleanMobile.substring(3); // Remove '966'
      if (withoutCountryCode.startsWith('05') &&
          withoutCountryCode.length == 10) {
        return withoutCountryCode
            .substring(1); // Remove '0' prefix -> 564254457
      }
      return withoutCountryCode; // Fallback
    } else if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
      // Local format: 0564254457 -> 564254457
      return cleanMobile.substring(1); // Remove '0' prefix only
    } else if (cleanMobile.length == 9 && cleanMobile.startsWith('5')) {
      // Already in core format: 564254457
      return cleanMobile;
    }

    // If none of the above, return as-is (fallback)
    return cleanMobile;
  }

  /// Generate different mobile number formats for searching
  ///
  /// @param mobile The input mobile number
  /// @return List of possible mobile number formats
  List<String> _generateMobileSearchFormats(String mobile) {
    final formats = <String>[];

    // Add the original format
    formats.add(mobile);

    // Clean the mobile number (remove non-digits)
    final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanMobile.isNotEmpty) {
      // Add clean format
      formats.add(cleanMobile);

      // Handle Saudi mobile number format (05xxxxxxxx -> 5xxxxxxxx)
      // This is the key fix: when user enters 05xxxxxxxx, we need to search for 5xxxxxxxx
      if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
        final coreNumber = cleanMobile.substring(1); // Remove '0' prefix
        formats.add(coreNumber); // Add core number (e.g., "564254457")
      }

      // If it starts with 966 (Saudi country code), also try without it
      if (cleanMobile.startsWith('966') && cleanMobile.length > 3) {
        final withoutCountryCode = cleanMobile.substring(3);
        formats.add(withoutCountryCode);
        formats.add('0$withoutCountryCode'); // Add leading 0

        // For Saudi numbers with country code (96605xxxxxxxx), extract core number
        if (withoutCountryCode.startsWith('05') &&
            withoutCountryCode.length == 10) {
          final coreNumber =
              withoutCountryCode.substring(1); // Remove '0' prefix
          formats.add(coreNumber); // Add core number
        }
      }

      // If it starts with 0, also try with country code and core number
      if (cleanMobile.startsWith('0') && cleanMobile.length > 1) {
        final withoutLeadingZero = cleanMobile.substring(1);
        formats.add(withoutLeadingZero);
        formats.add('966$withoutLeadingZero');
        formats.add('+966$withoutLeadingZero');

        // For Saudi numbers starting with 05, also add the core number
        if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
          final coreNumber = cleanMobile.substring(1); // Remove '0' prefix
          formats.add(coreNumber); // This is the stored format
        }
      }

      // Add international format variations
      if (!cleanMobile.startsWith('+')) {
        if (cleanMobile.startsWith('966')) {
          formats.add('+$cleanMobile');
        } else if (cleanMobile.startsWith('0')) {
          final withoutLeadingZero = cleanMobile.substring(1);
          formats.add('+966$withoutLeadingZero');
        } else {
          formats.add('+966$cleanMobile');
        }
      }
    }

    // Remove duplicates and return
    return formats.toSet().toList();
  }

  /// Generate a secure session token
  ///
  /// @return A secure session token string
  String _generateSessionToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random.secure();
    final randomPart = List.generate(
            16, (_) => random.nextInt(256).toRadixString(16).padLeft(2, '0'))
        .join();
    return 'session_${timestamp}_$randomPart';
  }
}
