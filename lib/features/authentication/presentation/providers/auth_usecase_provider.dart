/// Authentication Use Case Provider
///
/// Provides Riverpod providers for authentication use cases
/// Replaces GetX dependency injection for authentication use cases
library auth_usecase_provider;

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_repository_provider.dart';
import 'package:towasl/features/authentication/domain/usecases/login_user_usecase.dart';
import 'package:towasl/features/authentication/domain/usecases/send_otp_usecase.dart';
import 'package:towasl/features/authentication/domain/usecases/verify_otp_usecase.dart';

part 'auth_usecase_provider.g.dart';

// ============================================================================
// USE CASE PROVIDERS
// ============================================================================

/// Login User Use Case Provider
///
/// Provides the main login use case for authentication
@riverpod
LoginUserUseCase loginUserUseCase(LoginUserUseCaseRef ref) {
  return LoginUserUseCase(
    ref.read(authRepositoryProvider),
  );
}

/// Send OTP Use Case Provider
///
/// Provides use case for sending OTP
@riverpod
SendOtpUseCase sendOtpUseCase(SendOtpUseCaseRef ref) {
  return SendOtpUseCase(
    ref.read(authRepositoryProvider),
  );
}

/// Verify OTP Use Case Provider
///
/// Provides use case for verifying OTP
@riverpod
VerifyOtpUseCase verifyOtpUseCase(VerifyOtpUseCaseRef ref) {
  return VerifyOtpUseCase(
    ref.read(authRepositoryProvider),
  );
}
