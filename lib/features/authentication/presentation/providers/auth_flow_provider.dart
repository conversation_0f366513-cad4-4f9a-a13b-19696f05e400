/// Auth Flow Provider
///
/// Provides Riverpod state management for authentication flow
/// Replaces AuthFlowViewModel with Riverpod architecture
library auth_flow_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/routes.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/features/core/presentation/providers/user_session_provider.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';
import 'package:towasl/shared/models/user_model.dart';

part 'auth_flow_provider.g.dart';

/// State class for authentication flow
class AuthFlowState {
  /// Whether authentication is in progress
  final bool isAuthenticating;

  /// Current user ID if authenticated
  final String? userId;

  /// Whether user is new (first time login)
  final bool isNewUser;

  /// Error message if any
  final String? errorMessage;

  const AuthFlowState({
    this.isAuthenticating = false,
    this.userId,
    this.isNewUser = false,
    this.errorMessage,
  });

  AuthFlowState copyWith({
    bool? isAuthenticating,
    String? userId,
    bool? isNewUser,
    String? errorMessage,
  }) {
    return AuthFlowState(
      isAuthenticating: isAuthenticating ?? this.isAuthenticating,
      userId: userId ?? this.userId,
      isNewUser: isNewUser ?? this.isNewUser,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  AuthFlowState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// Auth Flow Notifier
///
/// Manages the complete authentication flow including OTP verification
/// and post-authentication navigation logic
@riverpod
class AuthFlow extends _$AuthFlow {
  @override
  AuthFlowState build() {
    if (kDebugMode) {
      print('AuthFlowNotifier: Initialized');
    }
    return const AuthFlowState();
  }

  /// Handle successful authentication
  ///
  /// @param mobile The mobile number that was authenticated
  /// @param userId The user ID from authentication
  /// @param userIsNew Whether this is a new user
  Future<void> handleAuthenticationSuccess(
    String mobile,
    String userId,
    bool userIsNew,
  ) async {
    if (kDebugMode) {
      print('AuthFlowNotifier: Handling authentication success');
      print('  - Mobile: $mobile');
      print('  - User ID: $userId');
      print('  - Is new user: $userIsNew');
    }

    try {
      state = state.copyWith(
        isAuthenticating: true,
        userId: userId,
        isNewUser: userIsNew,
      );

      // Update app state with user ID
      await ref.read(appStateProvider.notifier).setUserId(userId);

      // Create user session
      if (kDebugMode) {
        print('AuthFlowNotifier: Creating user session for: $userId');
      }
      await ref.read(userSessionProvider.notifier).createSession(userId);

      // Load user data to ensure providers are initialized
      if (kDebugMode) {
        print('AuthFlowNotifier: Loading user data for: $userId');
      }
      await ref.read(userProvider.notifier).loadUserData(userId);

      if (!userIsNew) {
        if (kDebugMode) {
          print('AuthFlowNotifier: Existing user detected - $userId');
        }
        // Existing user - check profile completion and navigate accordingly
        await _handleExistingUser(userId);
      } else {
        if (kDebugMode) {
          print('AuthFlowNotifier: New user detected - $userId');
        }
        // New user - create user record and start onboarding
        await _handleNewUser(mobile, userId);
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthFlowNotifier: Error handling authentication success - $e');
      }

      state =
          state.copyWith(errorMessage: 'An error occurred. Please try again.');
      ToastCustom.errorToast('An error occurred. Please try again.');
    } finally {
      state = state.copyWith(isAuthenticating: false);
    }
  }

  /// Handle existing user flow
  ///
  /// @param userId The existing user's ID
  Future<void> _handleExistingUser(String userId) async {
    try {
      if (kDebugMode) {
        print('AuthFlowNotifier: Handling existing user - $userId');
      }

      // Check profile completion using the profile checker
      final hasInterests = await ref.read(hasInterestsProvider(userId).future);
      final hasLocation = await ref.read(hasLocationProvider(userId).future);
      final hasPersonalInfo =
          await ref.read(hasPersonalInfoProvider(userId).future);

      if (kDebugMode) {
        print('AuthFlowNotifier: Profile completion status:');
        print('  - Has interests: $hasInterests');
        print('  - Has location: $hasLocation');
        print('  - Has personal info: $hasPersonalInfo');
      }

      // Navigate based on profile completion in the correct order
      // New order: Interests → Personal Info → Location → Home
      if (!hasInterests) {
        if (kDebugMode) {
          print(
              'AuthFlowNotifier: Missing interests, navigating to interests page');
        }
        _navigateToInterests();
      } else if (!hasPersonalInfo) {
        if (kDebugMode) {
          print(
              'AuthFlowNotifier: Missing personal info, navigating to personal info page');
        }
        _navigateToPersonalInfo();
      } else if (!hasLocation) {
        if (kDebugMode) {
          print(
              'AuthFlowNotifier: Missing location, navigating to location page');
        }
        _navigateToLocation();
      } else {
        if (kDebugMode) {
          print('AuthFlowNotifier: Profile complete, navigating to home page');
        }
        _navigateToHome();
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthFlowNotifier: Error handling existing user - $e');
      }
      rethrow;
    }
  }

  /// Handle new user flow
  ///
  /// @param mobile The new user's mobile number
  /// @param userId The new user's ID
  Future<void> _handleNewUser(String mobile, String userId) async {
    try {
      if (kDebugMode) {
        print('AuthFlowNotifier: Handling new user - $userId');
        print('AuthFlowNotifier: Input mobile format: $mobile');
      }

      // Normalize mobile number to core format (remove country code and leading 0)
      final normalizedMobile = _normalizeMobileNumber(mobile);

      if (kDebugMode) {
        print('AuthFlowNotifier: Normalized mobile format: $normalizedMobile');
      }

      // Create initial user model with country code
      final userModel = UserModel(
        userId: userId,
        mobile: normalizedMobile, // Store normalized mobile number
        countryCode: '+966', // Saudi Arabia country code
        // Other fields will be filled during onboarding
      );

      // Update app state with user model
      await ref.read(appStateProvider.notifier).setUserModel(userModel);

      // User is automatically saved through setUserModel which uses the use case

      if (kDebugMode) {
        print('AuthFlowNotifier: New user created, starting onboarding');
      }

      // Start onboarding flow with interests
      _navigateToInterests();
    } catch (e) {
      if (kDebugMode) {
        print('AuthFlowNotifier: Error handling new user - $e');
      }
      rethrow;
    }
  }

  /// Normalize mobile number to core format for consistent storage
  ///
  /// Converts various mobile number formats to the core digits format
  /// Examples:
  /// - +966********** -> *********
  /// - 966********** -> *********
  /// - ********** -> *********
  /// - ********* -> *********
  ///
  /// @param mobile The input mobile number in any format
  /// @return The normalized mobile number (core digits only)
  String _normalizeMobileNumber(String mobile) {
    // Remove all non-digit characters
    final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

    // Handle different formats
    if (cleanMobile.startsWith('966') && cleanMobile.length >= 12) {
      // International format: 966********** -> *********
      // Remove '966' country code first, then check if it starts with '05'
      final withoutCountryCode = cleanMobile.substring(3); // Remove '966'
      if (withoutCountryCode.startsWith('05') &&
          withoutCountryCode.length == 10) {
        return withoutCountryCode
            .substring(1); // Remove '0' prefix -> *********
      }
      return withoutCountryCode; // Fallback
    } else if (cleanMobile.startsWith('05') && cleanMobile.length == 10) {
      // Local format: ********** -> *********
      return cleanMobile.substring(1); // Remove '0' prefix only
    } else if (cleanMobile.length == 9 && cleanMobile.startsWith('5')) {
      // Already in core format: *********
      return cleanMobile;
    }

    // If none of the above, return as-is (fallback)
    return cleanMobile;
  }

  /// Navigate to interests page
  void _navigateToInterests() {
    if (kDebugMode) {
      print('AuthFlowNotifier: Navigating to interests page');
    }

    // Use navigation provider for routing
    ref
        .read(navigationProvider.notifier)
        .navigateToAndClearAll(AppRoutes.interests);
  }

  /// Navigate to location page
  void _navigateToLocation() {
    if (kDebugMode) {
      print('AuthFlowNotifier: Navigating to location page');
    }

    // Use navigation provider for routing
    ref
        .read(navigationProvider.notifier)
        .navigateToAndClearAll(AppRoutes.location);
  }

  /// Navigate to personal info page
  void _navigateToPersonalInfo() {
    if (kDebugMode) {
      print('AuthFlowNotifier: Navigating to personal info page');
    }

    // Use navigation provider for routing
    ref
        .read(navigationProvider.notifier)
        .navigateToAndClearAll(AppRoutes.personalInfo);
  }

  /// Navigate to home page
  void _navigateToHome() {
    if (kDebugMode) {
      print('AuthFlowNotifier: Navigating to home page');
    }

    // Use navigation provider for routing
    ref.read(navigationProvider.notifier).navigateToAndClearAll(AppRoutes.home);
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for authentication status
@riverpod
bool isAuthenticating(IsAuthenticatingRef ref) {
  return ref.watch(authFlowProvider).isAuthenticating;
}

/// Provider for current user ID
@riverpod
String? currentUserId(CurrentUserIdRef ref) {
  return ref.watch(authFlowProvider).userId;
}

/// Provider for new user status
@riverpod
bool isNewUser(IsNewUserRef ref) {
  return ref.watch(authFlowProvider).isNewUser;
}

/// Provider for auth flow error message
@riverpod
String? authFlowErrorMessage(AuthFlowErrorMessageRef ref) {
  return ref.watch(authFlowProvider).errorMessage;
}
