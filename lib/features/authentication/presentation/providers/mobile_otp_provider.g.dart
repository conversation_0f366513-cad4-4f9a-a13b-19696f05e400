// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mobile_otp_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$otpCodeHash() => r'f9a31bcc8bb11c74530d4b2a32136333348a6bd4';

/// Provider for OTP code
///
/// Copied from [otpCode].
@ProviderFor(otpCode)
final otpCodeProvider = AutoDisposeProvider<String>.internal(
  otpCode,
  name: r'otpCodeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$otpCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef OtpCodeRef = AutoDisposeProviderRef<String>;
String _$isVerifyingHash() => r'b3fe77c52ae18e08660d7c2d6c89931435efc2fc';

/// Provider for verification loading state
///
/// Copied from [isVerifying].
@ProviderFor(isVerifying)
final isVerifyingProvider = AutoDisposeProvider<bool>.internal(
  isVerifying,
  name: r'isVerifyingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isVerifyingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsVerifyingRef = AutoDisposeProviderRef<bool>;
String _$isResendingHash() => r'775e8c34d6272328852cb8e522ce6d1bcfa448a1';

/// Provider for resend loading state
///
/// Copied from [isResending].
@ProviderFor(isResending)
final isResendingProvider = AutoDisposeProvider<bool>.internal(
  isResending,
  name: r'isResendingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isResendingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsResendingRef = AutoDisposeProviderRef<bool>;
String _$mobileNumberHash() => r'235fa2ea34340ab7d606374e438e648c6ea55858';

/// Provider for mobile number
///
/// Copied from [mobileNumber].
@ProviderFor(mobileNumber)
final mobileNumberProvider = AutoDisposeProvider<String>.internal(
  mobileNumber,
  name: r'mobileNumberProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mobileNumberHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MobileNumberRef = AutoDisposeProviderRef<String>;
String _$remainingTimeHash() => r'48ccbe9b568462891f3a453265ab122f0eb7cc78';

/// Provider for remaining time
///
/// Copied from [remainingTime].
@ProviderFor(remainingTime)
final remainingTimeProvider = AutoDisposeProvider<int>.internal(
  remainingTime,
  name: r'remainingTimeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$remainingTimeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RemainingTimeRef = AutoDisposeProviderRef<int>;
String _$canResendHash() => r'a48fb5b1f175e3610403cff4d5f7ee6025f4f9ab';

/// Provider for can resend status
///
/// Copied from [canResend].
@ProviderFor(canResend)
final canResendProvider = AutoDisposeProvider<bool>.internal(
  canResend,
  name: r'canResendProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$canResendHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CanResendRef = AutoDisposeProviderRef<bool>;
String _$otpErrorMessageHash() => r'ba49522e427ada77e7875c7d625cc7551f427b8f';

/// Provider for OTP error message
///
/// Copied from [otpErrorMessage].
@ProviderFor(otpErrorMessage)
final otpErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  otpErrorMessage,
  name: r'otpErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$otpErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef OtpErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$mobileOtpHash() => r'1629798af65dca3be35d727eabf4deab86445911';

/// Mobile OTP Notifier
///
/// Manages state and business logic for the OTP verification screen
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [MobileOtp].
@ProviderFor(MobileOtp)
final mobileOtpProvider =
    AutoDisposeNotifierProvider<MobileOtp, MobileOtpState>.internal(
  MobileOtp.new,
  name: r'mobileOtpProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mobileOtpHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MobileOtp = AutoDisposeNotifier<MobileOtpState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
