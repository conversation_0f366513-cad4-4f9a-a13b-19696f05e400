// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_flow_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isAuthenticatingHash() => r'e4d8adfd3760e0faaa60457fae81e450bce23b36';

/// Provider for authentication status
///
/// Copied from [isAuthenticating].
@ProviderFor(isAuthenticating)
final isAuthenticatingProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticating,
  name: r'isAuthenticatingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsAuthenticatingRef = AutoDisposeProviderRef<bool>;
String _$currentUserIdHash() => r'f792deeb7ce83b3c8dc9a9b71e3f1c58548c2f1b';

/// Provider for current user ID
///
/// Copied from [currentUserId].
@ProviderFor(currentUserId)
final currentUserIdProvider = AutoDisposeProvider<String?>.internal(
  currentUserId,
  name: r'currentUserIdProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserIdHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentUserIdRef = AutoDisposeProviderRef<String?>;
String _$isNewUserHash() => r'0f0d6b1cd48e2d331228eb5e78018983b7c46c3f';

/// Provider for new user status
///
/// Copied from [isNewUser].
@ProviderFor(isNewUser)
final isNewUserProvider = AutoDisposeProvider<bool>.internal(
  isNewUser,
  name: r'isNewUserProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isNewUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsNewUserRef = AutoDisposeProviderRef<bool>;
String _$authFlowErrorMessageHash() =>
    r'632cc21ba3944b40c90074530c201013cf92fc71';

/// Provider for auth flow error message
///
/// Copied from [authFlowErrorMessage].
@ProviderFor(authFlowErrorMessage)
final authFlowErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  authFlowErrorMessage,
  name: r'authFlowErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authFlowErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AuthFlowErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$authFlowHash() => r'8347b96b4ba93644cc7459c0e879008af005c98f';

/// Auth Flow Notifier
///
/// Manages the complete authentication flow including OTP verification
/// and post-authentication navigation logic
///
/// Copied from [AuthFlow].
@ProviderFor(AuthFlow)
final authFlowProvider =
    AutoDisposeNotifierProvider<AuthFlow, AuthFlowState>.internal(
  AuthFlow.new,
  name: r'authFlowProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authFlowHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AuthFlow = AutoDisposeNotifier<AuthFlowState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
