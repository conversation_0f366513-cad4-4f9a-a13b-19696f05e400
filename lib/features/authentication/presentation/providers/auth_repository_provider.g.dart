// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_repository_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userIdGeneratorServiceHash() =>
    r'eef36d0ecdeb5a43597f5fb875e180fd4bf11a2e';

/// User ID Generator Service Provider
///
/// Provides user ID generation functionality for authentication
///
/// Copied from [userIdGeneratorService].
@ProviderFor(userIdGeneratorService)
final userIdGeneratorServiceProvider =
    AutoDisposeProvider<UserIdGeneratorService>.internal(
  userIdGeneratorService,
  name: r'userIdGeneratorServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userIdGeneratorServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserIdGeneratorServiceRef
    = AutoDisposeProviderRef<UserIdGeneratorService>;
String _$authRemoteDataSourceHash() =>
    r'05a3c0bd0cadaf4b2c378603276413beef5401d9';

/// Auth Remote Data Source Provider
///
/// Provides remote data source for authentication operations
///
/// Copied from [authRemoteDataSource].
@ProviderFor(authRemoteDataSource)
final authRemoteDataSourceProvider =
    AutoDisposeProvider<AuthRemoteDataSource>.internal(
  authRemoteDataSource,
  name: r'authRemoteDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authRemoteDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AuthRemoteDataSourceRef = AutoDisposeProviderRef<AuthRemoteDataSource>;
String _$authLocalDataSourceHash() =>
    r'f7b3a62b46c075ce806beece82f580a0ae185af4';

/// Auth Local Data Source Provider
///
/// Provides local data source for authentication data persistence
///
/// Copied from [authLocalDataSource].
@ProviderFor(authLocalDataSource)
final authLocalDataSourceProvider =
    AutoDisposeProvider<AuthLocalDataSource>.internal(
  authLocalDataSource,
  name: r'authLocalDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authLocalDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AuthLocalDataSourceRef = AutoDisposeProviderRef<AuthLocalDataSource>;
String _$authRepositoryHash() => r'754e498eef0bdd17193fba00f6674259d97747d1';

/// Auth Repository Provider
///
/// Provides authentication repository implementation
///
/// Copied from [authRepository].
@ProviderFor(authRepository)
final authRepositoryProvider = AutoDisposeProvider<AuthRepository>.internal(
  authRepository,
  name: r'authRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AuthRepositoryRef = AutoDisposeProviderRef<AuthRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
