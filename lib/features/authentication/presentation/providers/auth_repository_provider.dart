/// Authentication Repository Provider
///
/// Provides Riverpod providers for authentication repositories and data sources
/// Replaces GetX dependency injection for authentication feature
library auth_repository_provider;

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/authentication/domain/repositories/auth_repository.dart';
import 'package:towasl/features/authentication/data/repositories/auth_repository_impl.dart';
import 'package:towasl/features/authentication/data/datasources/auth_remote_datasource.dart';
import 'package:towasl/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:towasl/features/authentication/domain/services/user_id_generator_service.dart';

part 'auth_repository_provider.g.dart';

// ============================================================================
// DATA SOURCE PROVIDERS
// ============================================================================

/// User ID Generator Service Provider
///
/// Provides user ID generation functionality for authentication
@riverpod
UserIdGeneratorService userIdGeneratorService(UserIdGeneratorServiceRef ref) {
  return UserIdGeneratorServiceImpl();
}

/// Auth Remote Data Source Provider
///
/// Provides remote data source for authentication operations
@riverpod
AuthRemoteDataSource authRemoteDataSource(AuthRemoteDataSourceRef ref) {
  return AuthRemoteDataSourceImpl(
    ref.read(firebaseServiceProvider),
    ref.read(userIdGeneratorServiceProvider),
  );
}

/// Auth Local Data Source Provider
///
/// Provides local data source for authentication data persistence
@riverpod
AuthLocalDataSource authLocalDataSource(AuthLocalDataSourceRef ref) {
  return AuthLocalDataSourceImpl(
    ref.read(storageServiceProvider),
  );
}

// ============================================================================
// REPOSITORY PROVIDERS
// ============================================================================

/// Auth Repository Provider
///
/// Provides authentication repository implementation
@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  return AuthRepositoryImpl(
    ref.read(authRemoteDataSourceProvider),
    ref.read(authLocalDataSourceProvider),
  );
}
