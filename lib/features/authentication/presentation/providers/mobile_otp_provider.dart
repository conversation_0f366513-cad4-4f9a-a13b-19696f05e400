/// Mobile OTP Provider
///
/// Provides Riverpod state management for OTP verification screen
/// Replaces MobileOtpViewModel with Riverpod architecture
library mobile_otp_provider;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_usecase_provider.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_flow_provider.dart';
import 'package:towasl/features/authentication/domain/entities/otp_verification.dart';
import 'package:towasl/features/authentication/domain/entities/login_request.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

part 'mobile_otp_provider.g.dart';

/// State class for OTP verification screen
class MobileOtpState {
  /// Current OTP value
  final String otpCode;

  /// Whether OTP verification is in progress
  final bool isVerifying;

  /// Whether resending OTP is in progress
  final bool isResending;

  /// Mobile number for OTP verification
  final String mobileNumber;

  /// Country code
  final String countryCode;

  /// Remaining time for OTP expiry (in seconds)
  final int remainingTime;

  /// Whether OTP can be resent
  final bool canResend;

  /// Error message if any
  final String? errorMessage;

  const MobileOtpState({
    this.otpCode = '',
    this.isVerifying = false,
    this.isResending = false,
    this.mobileNumber = '',
    this.countryCode = '966',
    this.remainingTime = 120, // 2 minutes
    this.canResend = false,
    this.errorMessage,
  });

  MobileOtpState copyWith({
    String? otpCode,
    bool? isVerifying,
    bool? isResending,
    String? mobileNumber,
    String? countryCode,
    int? remainingTime,
    bool? canResend,
    String? errorMessage,
  }) {
    return MobileOtpState(
      otpCode: otpCode ?? this.otpCode,
      isVerifying: isVerifying ?? this.isVerifying,
      isResending: isResending ?? this.isResending,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      countryCode: countryCode ?? this.countryCode,
      remainingTime: remainingTime ?? this.remainingTime,
      canResend: canResend ?? this.canResend,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  MobileOtpState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// Mobile OTP Notifier
///
/// Manages state and business logic for the OTP verification screen
/// Follows MVVM pattern with Riverpod state management
@riverpod
class MobileOtp extends _$MobileOtp {
  /// Text controllers for OTP input fields
  late final List<TextEditingController> otpControllers;

  /// Focus nodes for OTP input fields
  late final List<FocusNode> focusNodes;

  @override
  MobileOtpState build() {
    // Initialize controllers and focus nodes
    otpControllers = List.generate(4, (index) => TextEditingController());
    focusNodes = List.generate(4, (index) => FocusNode());

    // Clean up when provider is disposed
    ref.onDispose(() {
      try {
        for (var controller in otpControllers) {
          controller.dispose();
        }
        for (var focusNode in focusNodes) {
          focusNode.dispose();
        }
      } catch (e) {
        // Ignore errors during disposal
        if (kDebugMode) {
          print('MobileOtpNotifier: Error during disposal - $e');
        }
      }
    });

    if (kDebugMode) {
      print('MobileOtpNotifier: Initialized');
    }

    return const MobileOtpState();
  }

  /// Initialize with mobile number and country code
  void initialize(String mobileNumber, String countryCode) {
    state = state.copyWith(
      mobileNumber: mobileNumber,
      countryCode: countryCode,
    );

    // Start countdown timer
    _startCountdownTimer();

    if (kDebugMode) {
      print('MobileOtpNotifier: Initialized with mobile: $mobileNumber');
    }
  }

  /// Update OTP code
  void updateOtpCode(String otpCode) {
    state = state.copyWith(otpCode: otpCode);

    // Auto-verify if OTP is complete
    if (otpCode.length == 4) {
      verifyOtp();
    }

    if (kDebugMode) {
      print('MobileOtpNotifier: OTP updated: ${otpCode.length}/4 digits');
    }
  }

  /// Handle OTP input field changes
  void onOtpChanged(int index, String value) {
    // Update the specific controller
    if (index < otpControllers.length) {
      otpControllers[index].text = value;
    }

    // Build complete OTP code
    final otpCode = otpControllers.map((controller) => controller.text).join();
    updateOtpCode(otpCode);

    // Move focus to next field if value is entered
    if (value.isNotEmpty && index < focusNodes.length - 1) {
      focusNodes[index + 1].requestFocus();
    }

    // Move focus to previous field if value is deleted
    if (value.isEmpty && index > 0) {
      focusNodes[index - 1].requestFocus();
    }
  }

  /// Verify OTP
  Future<void> verifyOtp() async {
    final otpCode = state.otpCode.trim();

    if (kDebugMode) {
      print('MobileOtpNotifier: Verifying OTP: $otpCode');
    }

    // Validate OTP
    if (otpCode.length != 4) {
      state = state.copyWith(errorMessage: 'Please enter complete OTP');
      return;
    }

    try {
      // Set loading state
      state = state.copyWith(isVerifying: true);

      // Get verify OTP use case
      final verifyOtpUseCase = ref.read(verifyOtpUseCaseProvider);

      // Create OTP verification entity
      final otpVerification = OtpVerification(
        mobileNumber: state.mobileNumber,
        otpCode: otpCode,
        // userId is optional and will be generated/retrieved during verification
      );

      // Verify OTP through use case
      final result = await verifyOtpUseCase.execute(otpVerification);

      if (result.isSuccess) {
        if (kDebugMode) {
          print('MobileOtpNotifier: OTP verified successfully');
        }

        // Clear any previous errors
        state = state.clearError();

        // Handle successful authentication through AuthFlow
        await ref.read(authFlowProvider.notifier).handleAuthenticationSuccess(
              state.mobileNumber,
              result.userId!,
              result.isNewUser,
            );

        ToastCustom.successToast('تم التحقق من الرمز بنجاح!');
      } else {
        if (kDebugMode) {
          print(
              'MobileOtpNotifier: OTP verification failed - ${result.errorMessage}');
        }

        state = state.copyWith(errorMessage: result.errorMessage);
        ToastCustom.errorToast(result.errorMessage ?? 'فشل التحقق من الرمز');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MobileOtpNotifier: Error during OTP verification - $e');
      }

      state =
          state.copyWith(errorMessage: 'An error occurred. Please try again.');
      ToastCustom.errorToast('حدث خطأ. يرجى المحاولة مرة أخرى.');
    } finally {
      state = state.copyWith(isVerifying: false);
    }
  }

  /// Resend OTP
  Future<void> resendOtp() async {
    if (!state.canResend) {
      ToastCustom.errorToast('يرجى الانتظار قبل طلب رمز تحقق جديد');
      return;
    }

    if (kDebugMode) {
      print('MobileOtpNotifier: Resending OTP to: ${state.mobileNumber}');
    }

    try {
      // Set loading state
      state = state.copyWith(isResending: true);

      // Get send OTP use case
      final sendOtpUseCase = ref.read(sendOtpUseCaseProvider);

      // Create login request for resending OTP
      final loginRequest = LoginRequest(
        mobileNumber: state.mobileNumber,
        countryCode: state.countryCode,
      );

      // Resend OTP through use case
      final result = await sendOtpUseCase.execute(loginRequest);

      if (result.isSuccess) {
        if (kDebugMode) {
          print('MobileOtpNotifier: OTP resent successfully');
        }

        // Clear OTP fields
        _clearOtpFields();

        // Reset timer
        state = state.copyWith(
          remainingTime: 120,
          canResend: false,
        );
        _startCountdownTimer();

        // Clear any previous errors
        state = state.clearError();

        ToastCustom.successToast('تم إرسال رمز التحقق بنجاح!');
      } else {
        if (kDebugMode) {
          print(
              'MobileOtpNotifier: OTP resend failed - ${result.errorMessage}');
        }

        state = state.copyWith(errorMessage: result.errorMessage);
        ToastCustom.errorToast(
            result.errorMessage ?? 'فشل في إعادة إرسال رمز التحقق');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MobileOtpNotifier: Error during OTP resend - $e');
      }

      state =
          state.copyWith(errorMessage: 'An error occurred. Please try again.');
      ToastCustom.errorToast('حدث خطأ. يرجى المحاولة مرة أخرى.');
    } finally {
      state = state.copyWith(isResending: false);
    }
  }

  /// Clear OTP input fields
  void _clearOtpFields() {
    for (var controller in otpControllers) {
      controller.clear();
    }
    state = state.copyWith(otpCode: '');
  }

  /// Start countdown timer for OTP expiry
  void _startCountdownTimer() {
    // This would typically use a Timer.periodic
    // For now, we'll simulate the countdown
    // In a real implementation, you'd use a timer that updates every second
    if (kDebugMode) {
      print('MobileOtpNotifier: Starting countdown timer');
    }
  }

  /// Navigate back to signup/login screen
  void navigateBack() {
    if (kDebugMode) {
      print('MobileOtpNotifier: Navigating back to signup/login');
    }

    ref.read(navigationProvider.notifier).goBack();
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Focus on the first OTP input field
  void focusFirstInput() {
    if (focusNodes.isNotEmpty) {
      focusNodes[0].requestFocus();
    }
  }

  /// Unfocus all OTP input fields
  void unfocusAllInputs() {
    for (var focusNode in focusNodes) {
      focusNode.unfocus();
    }
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for OTP code
@riverpod
String otpCode(OtpCodeRef ref) {
  return ref.watch(mobileOtpProvider).otpCode;
}

/// Provider for verification loading state
@riverpod
bool isVerifying(IsVerifyingRef ref) {
  return ref.watch(mobileOtpProvider).isVerifying;
}

/// Provider for resend loading state
@riverpod
bool isResending(IsResendingRef ref) {
  return ref.watch(mobileOtpProvider).isResending;
}

/// Provider for mobile number
@riverpod
String mobileNumber(MobileNumberRef ref) {
  return ref.watch(mobileOtpProvider).mobileNumber;
}

/// Provider for remaining time
@riverpod
int remainingTime(RemainingTimeRef ref) {
  return ref.watch(mobileOtpProvider).remainingTime;
}

/// Provider for can resend status
@riverpod
bool canResend(CanResendRef ref) {
  return ref.watch(mobileOtpProvider).canResend;
}

/// Provider for OTP error message
@riverpod
String? otpErrorMessage(OtpErrorMessageRef ref) {
  return ref.watch(mobileOtpProvider).errorMessage;
}
