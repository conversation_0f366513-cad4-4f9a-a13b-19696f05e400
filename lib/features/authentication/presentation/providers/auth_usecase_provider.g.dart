// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_usecase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loginUserUseCaseHash() => r'a7447c6969325489747ae912c28a8e0498a6da68';

/// Login User Use Case Provider
///
/// Provides the main login use case for authentication
///
/// Copied from [loginUserUseCase].
@ProviderFor(loginUserUseCase)
final loginUserUseCaseProvider = AutoDisposeProvider<LoginUserUseCase>.internal(
  loginUserUseCase,
  name: r'loginUserUseCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginUserUseCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef LoginUserUseCaseRef = AutoDisposeProviderRef<LoginUserUseCase>;
String _$sendOtpUseCaseHash() => r'3b734b0070817e555f360b3e37c4693b95971ad2';

/// Send OTP Use Case Provider
///
/// Provides use case for sending OTP
///
/// Copied from [sendOtpUseCase].
@ProviderFor(sendOtpUseCase)
final sendOtpUseCaseProvider = AutoDisposeProvider<SendOtpUseCase>.internal(
  sendOtpUseCase,
  name: r'sendOtpUseCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sendOtpUseCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SendOtpUseCaseRef = AutoDisposeProviderRef<SendOtpUseCase>;
String _$verifyOtpUseCaseHash() => r'7d83c9c8733a9c71aa74e0d66074a3f368129a69';

/// Verify OTP Use Case Provider
///
/// Provides use case for verifying OTP
///
/// Copied from [verifyOtpUseCase].
@ProviderFor(verifyOtpUseCase)
final verifyOtpUseCaseProvider = AutoDisposeProvider<VerifyOtpUseCase>.internal(
  verifyOtpUseCase,
  name: r'verifyOtpUseCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$verifyOtpUseCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef VerifyOtpUseCaseRef = AutoDisposeProviderRef<VerifyOtpUseCase>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
