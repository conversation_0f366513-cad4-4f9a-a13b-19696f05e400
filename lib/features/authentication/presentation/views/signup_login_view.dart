/// Signup Login View (Riverpod Version)
///
/// Riverpod version of the signup/login screen following MVVM pattern
/// Demonstrates migration from GetX to Riverpod for authentication
library signup_login_view_riverpod;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/constants.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/features/authentication/presentation/providers/signup_login_provider.dart';
import 'package:towasl/features/authentication/presentation/views/mobile_otp_view.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/widgets/bottom_action_bar.dart';
import 'package:towasl/shared/widgets/input_formatters/input_formatters.dart';

/// Signup Login View (Riverpod Version)
///
/// Authentication screen that allows users to enter their mobile number
/// and initiate the login/signup process with OTP verification
/// Follows MVVM pattern with Riverpod providers instead of GetX
class SignupLoginView extends ConsumerStatefulWidget {
  /// Creates a SignupLoginView
  const SignupLoginView({super.key});

  @override
  ConsumerState<SignupLoginView> createState() => _SignupLoginViewState();
}

/// State for the SignupLoginView
class _SignupLoginViewState extends ConsumerState<SignupLoginView> {
  bool _hasNavigated = false;
  bool _termsAccepted = false;

  @override
  void initState() {
    super.initState();

    // Focus on mobile input when view opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(signupLoginProvider.notifier).focusMobileInput();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final signupLoginState = ref.watch(signupLoginProvider);
    final isLoggingIn = ref.watch(isLoggingInProvider);

    // Listen for successful login and navigate to OTP view
    ref.listen<SignupLoginState>(signupLoginProvider, (previous, current) {
      if (kDebugMode) {
        print('🔥 SignupLoginView: State changed');
        print(
            '  Previous: isLoggingIn=${previous?.isLoggingIn}, error=${previous?.errorMessage}');
        print(
            '  Current: isLoggingIn=${current.isLoggingIn}, error=${current.errorMessage}');
        print('  _hasNavigated: $_hasNavigated');
        print('  Phone: ${current.phoneNumber}');
      }

      // Reset navigation flag if there's an error (so we can navigate again after fixing the error)
      if (current.errorMessage != null) {
        if (kDebugMode) {
          print(
              '🔥 SignupLoginView: Error detected, resetting navigation flag');
        }
        _hasNavigated = false;
      }

      // Check if login was successful (was loading, now not loading, no error)
      if (previous?.isLoggingIn == true &&
          !current.isLoggingIn &&
          current.errorMessage == null &&
          !_hasNavigated) {
        if (kDebugMode) {
          print(
              '🔥 SignupLoginView: Navigation conditions met, navigating to OTP view');
        }

        _hasNavigated = true;

        // Navigate to OTP verification
        final phoneNumber = current.phoneNumber.startsWith('0')
            ? current.phoneNumber.substring(1) // Remove leading 0
            : current.phoneNumber;

        if (kDebugMode) {
          print('🔥 SignupLoginView: Navigating with phone: $phoneNumber');
        }

        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (context) => MobileOtpView(
              mobileNumber: phoneNumber,
              countryCode: current.countryCode,
            ),
          ),
        )
            .then((_) {
          // Reset navigation flag when returning from OTP view
          _hasNavigated = false;
          if (kDebugMode) {
            print(
                '🔥 SignupLoginView: Returned from OTP view, reset navigation flag');
          }
        });
      }
    });

    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content wrapped in GestureDetector for tap outside handling
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                // Unfocus mobile input when tapping outside
                ref.read(signupLoginProvider.notifier).unfocusMobileInput();
              },
              child: _buildMainContent(signupLoginState),
            ),

            // Loading overlay
            if (isLoggingIn)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: BottomActionBar(
        buttonText: AppLocalizations.of(context).continueText,
        isEnabled: _termsAccepted && signupLoginState.phoneNumber.isNotEmpty,
        isLoading: isLoggingIn,
        onPressed: (_termsAccepted && signupLoginState.phoneNumber.isNotEmpty)
            ? () {
                ref.read(signupLoginProvider.notifier).initiateLogin();
              }
            : null,
      ),
    );
  }

  /// Build the main content of the screen
  Widget _buildMainContent(SignupLoginState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 40),

          // Header section
          _buildHeader(),

          const SizedBox(height: 40),

          // Mobile input section
          _buildMobileInputSection(state),

          const SizedBox(height: 24),

          // Terms and conditions section
          _buildTermsSection(),

          const Spacer(),
        ],
      ),
    );
  }

  /// Build the header section
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).welcomeToTowasI,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryPurple,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context).enterMobileNumberToGetStarted,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.greyDark,
          ),
        ),
      ],
    );
  }

  /// Build the mobile input section
  Widget _buildMobileInputSection(SignupLoginState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).mobileNumber,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.greyDark,
          ),
        ),
        const SizedBox(height: 12),

        // Mobile input field
        Directionality(
          textDirection: TextDirection.ltr,
          child: TextField(
            controller: ref.read(signupLoginProvider.notifier).mobileController,
            focusNode: ref.read(signupLoginProvider.notifier).mobileFocusNode,
            enabled: state.isMobileEnabled,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              const ArabicToEnglishNumeralFormatter(),
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(10),
            ],
            decoration: const InputDecoration(
              hintText: '05xxxxxxxx',
              border: OutlineInputBorder(),
              // prefixText: '+966 ',
            ),
            onChanged: (value) {
              ref.read(signupLoginProvider.notifier).updatePhoneNumber(value);
              // Clear error when user starts typing
              if (state.errorMessage != null) {
                ref.read(signupLoginProvider.notifier).clearError();
              }
            },
          ),
        ),

        // Error message
        if (state.errorMessage != null) ...[
          const SizedBox(height: 8),
          Text(
            state.errorMessage!,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
          ),
        ],
      ],
    );
  }

  /// Build the terms and conditions section
  Widget _buildTermsSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _termsAccepted,
          onChanged: (value) {
            setState(() {
              _termsAccepted = value ?? false;
            });
          },
          activeColor: AppColors.primaryPurple,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _termsAccepted = !_termsAccepted;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.greyDark,
                    height: 1.4,
                  ),
                  children: [
                    TextSpan(text: AppLocalizations.of(context).iAcceptThe),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => _launchUrl(Constants.termsUrl),
                        child: Text(
                          AppLocalizations.of(context).termsAndConditions,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.primaryPurple,
                            decoration: TextDecoration.underline,
                            decorationColor: AppColors.primaryPurple,
                          ),
                        ),
                      ),
                    ),
                    TextSpan(text: AppLocalizations.of(context).and),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => _launchUrl(Constants.privacyUrl),
                        child: Text(
                          AppLocalizations.of(context).privacyPolicy,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.primaryPurple,
                            decoration: TextDecoration.underline,
                            decorationColor: AppColors.primaryPurple,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Launch URL in browser
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${AppLocalizations.of(context).couldNotLaunch} $urlString'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Migration Demo Widget
///
/// Shows side-by-side comparison of GetX vs Riverpod implementation
class AuthMigrationDemoWidget extends StatelessWidget {
  const AuthMigrationDemoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentication Migration Demo'),
        backgroundColor: Colors.blue,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Authentication Feature Migration',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),

            // Migration Benefits
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🎯 Migration Benefits',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text('✅ Better type safety with AsyncValue<T>'),
                    Text('✅ Automatic error handling'),
                    Text('✅ Improved testing capabilities'),
                    Text('✅ Better performance with fine-grained reactivity'),
                    Text('✅ Compile-time dependency injection'),
                    Text('✅ Automatic disposal of resources'),
                  ],
                ),
              ),
            ),

            SizedBox(height: 10),

            // Implementation Details
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🔧 Implementation Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text('• State: SignupLoginState class'),
                    Text('• Notifier: SignupLogin extends _\$SignupLogin'),
                    Text('• Providers: Auto-generated with @riverpod'),
                    Text('• UI: ConsumerStatefulWidget'),
                    Text('• Reactivity: ref.watch() and ref.read()'),
                  ],
                ),
              ),
            ),

            SizedBox(height: 10),

            // Next Steps
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📋 Next Steps',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text('1. Integrate with navigation system'),
                    Text('2. Add OTP verification provider'),
                    Text('3. Connect with actual use cases'),
                    Text('4. Migrate remaining authentication views'),
                    Text('5. Update tests to use ProviderContainer'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
