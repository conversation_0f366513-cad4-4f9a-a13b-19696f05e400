/// Authentication Test Widget
///
/// A test widget to demonstrate the Riverpod authentication providers
/// Shows the migration from GetX to Riverpod for authentication features
library auth_test_widget;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/features/authentication/presentation/providers/signup_login_provider.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_flow_provider.dart';

/// Test widget to demonstrate Riverpod authentication providers
class AuthTestWidget extends ConsumerStatefulWidget {
  const AuthTestWidget({super.key});

  @override
  ConsumerState<AuthTestWidget> createState() => _AuthTestWidgetState();
}

class _AuthTestWidgetState extends ConsumerState<AuthTestWidget> {
  @override
  Widget build(BuildContext context) {
    final signupLoginState = ref.watch(signupLoginProvider);
    final authFlowState = ref.watch(authFlowProvider);
    final phoneNumber = ref.watch(phoneNumberProvider);
    final isLoggingIn = ref.watch(isLoggingInProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentication Migration Test'),
        backgroundColor: AppColors.primaryPurple,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Authentication Riverpod Providers Test',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),

              // Signup/Login Provider Test
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Signup/Login Provider:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text('Phone Number: "$phoneNumber"'),
                      Text('Is Logging In: $isLoggingIn'),
                      Text(
                          'Mobile Enabled: ${signupLoginState.isMobileEnabled}'),
                      Text('Country Code: ${signupLoginState.countryCode}'),
                      if (signupLoginState.errorMessage != null)
                        Text(
                          'Error: ${signupLoginState.errorMessage}',
                          style: const TextStyle(color: Colors.red),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 10),

              // Phone Number Input Test
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Phone Number Input Test:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      TextField(
                        controller: ref
                            .read(signupLoginProvider.notifier)
                            .mobileController,
                        decoration: const InputDecoration(
                          labelText: 'Mobile Number',
                          hintText: '05xxxxxxxx',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.phone,
                        enabled: signupLoginState.isMobileEnabled,
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: isLoggingIn
                                ? null
                                : () {
                                    ref
                                        .read(signupLoginProvider.notifier)
                                        .initiateLogin();
                                  },
                            child: isLoggingIn
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  )
                                : const Text('Login'),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                            onPressed: () {
                              ref
                                  .read(signupLoginProvider.notifier)
                                  .clearError();
                            },
                            child: const Text('Clear Error'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 10),

              // Auth Flow Provider Test
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Auth Flow Provider:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                          'Is Authenticating: ${authFlowState.isAuthenticating}'),
                      Text('User ID: ${authFlowState.userId ?? "Not set"}'),
                      Text('Is New User: ${authFlowState.isNewUser}'),
                      if (authFlowState.errorMessage != null)
                        Text(
                          'Error: ${authFlowState.errorMessage}',
                          style: const TextStyle(color: Colors.red),
                        ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              ref
                                  .read(authFlowProvider.notifier)
                                  .handleAuthenticationSuccess(
                                    '+966501234567',
                                    'test_user_123',
                                    false, // Existing user
                                  );
                            },
                            child: const Text('Test Existing User'),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                            onPressed: () {
                              ref
                                  .read(authFlowProvider.notifier)
                                  .handleAuthenticationSuccess(
                                    '+966501234567',
                                    'new_user_456',
                                    true, // New user
                                  );
                            },
                            child: const Text('Test New User'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Migration Comparison
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Migration Status:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text('✅ Authentication providers created'),
                      Text('✅ SignupLogin provider working'),
                      Text('✅ AuthFlow provider working'),
                      Text('✅ State management functional'),
                      Text('✅ Form validation working'),
                      Text('✅ Error handling implemented'),
                      Text('⏳ Navigation integration pending'),
                      Text('⏳ OTP verification pending'),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Code Comparison
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Code Comparison:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '❌ GetX (Old):',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.red),
                            ),
                            Text('• Get.find<SignupLoginViewModel>()'),
                            Text('• Obx(() => widget)'),
                            Text('• RxString phoneNumber = "".obs'),
                            Text('• Get.to(() => NextView())'),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          border: Border.all(color: Colors.green.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '✅ Riverpod (New):',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green),
                            ),
                            Text('• ref.read(signupLoginProvider.notifier)'),
                            Text('• ref.watch(signupLoginProvider)'),
                            Text('• AsyncValue<SignupLoginState>'),
                            Text('• Provider-based navigation'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
