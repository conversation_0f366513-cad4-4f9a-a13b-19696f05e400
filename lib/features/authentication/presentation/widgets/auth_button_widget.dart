/// Authentication Button Widget
///
/// Reusable button widget for authentication screens
/// Provides consistent styling and behavior for auth-related actions
library auth_button_widget;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Authentication Button Widget
///
/// Provides a consistent button design for authentication screens
/// Supports different button types and states
class AuthButtonWidget extends StatelessWidget {
  /// Button text
  final String text;

  /// Button press callback
  final VoidCallback? onPressed;

  /// Whether the button is loading
  final bool isLoading;

  /// Whether the button is enabled
  final bool enabled;

  /// Button type
  final AuthButtonType type;

  /// Button height
  final double height;

  /// Button width (null for full width)
  final double? width;

  /// Button margin
  final EdgeInsetsGeometry? margin;

  /// Creates an AuthButtonWidget
  ///
  /// @param text Button text
  /// @param onPressed Button press callback
  /// @param isLoading Whether the button is loading
  /// @param enabled Whether the button is enabled
  /// @param type Button type
  /// @param height Button height
  /// @param width Button width (null for full width)
  /// @param margin Button margin
  const AuthButtonWidget({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.type = AuthButtonType.primary,
    this.height = 50,
    this.width,
    this.margin,
  });

  /// Get button colors based on type and state
  ButtonStyle get _buttonStyle {
    final isDisabled = !enabled || isLoading || onPressed == null;

    switch (type) {
      case AuthButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor:
              isDisabled ? AppColors.greyMedium : AppColors.primaryPurple,
          foregroundColor: AppColors.whitePure,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          minimumSize: Size(width ?? double.infinity, height),
        );

      case AuthButtonType.secondary:
        return OutlinedButton.styleFrom(
          backgroundColor: AppColors.whitePure,
          foregroundColor:
              isDisabled ? AppColors.greyMedium : AppColors.greyDark,
          side: BorderSide(
            color: isDisabled ? AppColors.greyMedium : AppColors.greyDark,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          minimumSize: Size(width ?? double.infinity, height),
        );

      case AuthButtonType.text:
        return TextButton.styleFrom(
          foregroundColor:
              isDisabled ? AppColors.greyMedium : AppColors.primaryPurple,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          minimumSize: Size(width ?? double.infinity, height),
        );
    }
  }

  /// Get text style based on button type
  TextStyle get _textStyle {
    switch (type) {
      case AuthButtonType.primary:
        return styleWhiteLarge.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        );

      case AuthButtonType.secondary:
        return styleBlackNormal.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        );

      case AuthButtonType.text:
        return stylePrimaryNormal.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget button;

    switch (type) {
      case AuthButtonType.primary:
        button = ElevatedButton(
          onPressed: enabled && !isLoading ? onPressed : null,
          style: _buttonStyle,
          child: _buildButtonContent(),
        );
        break;

      case AuthButtonType.secondary:
        button = OutlinedButton(
          onPressed: enabled && !isLoading ? onPressed : null,
          style: _buttonStyle,
          child: _buildButtonContent(),
        );
        break;

      case AuthButtonType.text:
        button = TextButton(
          onPressed: enabled && !isLoading ? onPressed : null,
          style: _buttonStyle,
          child: _buildButtonContent(),
        );
        break;
    }

    if (margin != null) {
      return Container(
        margin: margin,
        child: button,
      );
    }

    return button;
  }

  /// Build button content with loading indicator
  Widget _buildButtonContent() {
    if (isLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                type == AuthButtonType.primary
                    ? AppColors.whitePure
                    : AppColors.primaryPurple,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: _textStyle,
          ),
        ],
      );
    }

    return Text(
      text,
      style: _textStyle,
    );
  }
}

/// Authentication button types
enum AuthButtonType {
  /// Primary button (filled background)
  primary,

  /// Secondary button (outlined)
  secondary,

  /// Text button (no background)
  text,
}
