/// Welcome Repository Interface
///
/// Defines the contract for welcome screen data operations
/// Provides methods for storing and retrieving welcome screen state
library welcome_repository;

import 'package:towasl/features/welcome/domain/entities/welcome_state.dart';

/// Abstract repository for welcome screen operations
///
/// Defines the interface for managing welcome screen state
/// Implementations should handle data source specifics (storage, etc.)
abstract class WelcomeRepository {
  /// Get current welcome state
  ///
  /// Retrieves the user's current welcome screen state from storage
  /// Used to check if user has seen the welcome screen before
  ///
  /// @return A Future that resolves to a WelcomeState entity
  Future<WelcomeState> getWelcomeState();

  /// Save welcome state
  ///
  /// Stores the user's welcome screen state with interaction data
  /// Used when user views or completes the welcome screen
  ///
  /// @param welcomeState The welcome state data to save
  /// @return A Future that completes when data is saved
  Future<void> saveWelcomeState(WelcomeState welcomeState);

  /// Mark welcome as completed
  ///
  /// Updates the welcome state to indicate user has completed viewing
  /// Used when user proceeds from welcome to authentication
  ///
  /// @return A Future that completes when state is updated
  Future<void> markWelcomeAsCompleted();

  /// Clear welcome state
  ///
  /// Removes stored welcome state information
  /// Used for testing or when resetting user experience
  ///
  /// @return A Future that completes when data is cleared
  Future<void> clearWelcomeState();

  /// Check if user has seen welcome
  ///
  /// Simple check for welcome completion status without full details
  /// Used for quick navigation decisions
  ///
  /// @return A Future that resolves to true if user has seen welcome
  Future<bool> hasSeenWelcome();

  /// Increment view count
  ///
  /// Increases the count of how many times user has seen welcome
  /// Used for analytics and user experience tracking
  ///
  /// @return A Future that completes when count is updated
  Future<void> incrementViewCount();
}
