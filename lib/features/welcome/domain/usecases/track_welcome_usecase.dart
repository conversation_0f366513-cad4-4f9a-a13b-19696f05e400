/// Track Welcome Use Case
///
/// Business logic for tracking welcome screen interactions
/// Encapsulates the rules for welcome screen state management
library track_welcome_usecase;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/welcome/domain/entities/welcome_state.dart';
import 'package:towasl/features/welcome/domain/repositories/welcome_repository.dart';

/// Use case for tracking welcome screen interactions
///
/// Handles the business logic for welcome screen state including:
/// - Tracking when user views the welcome screen
/// - Managing view counts and timestamps
/// - Determining navigation flow
class TrackWelcomeUseCase {
  /// Welcome repository for data operations
  final WelcomeRepository _welcomeRepository;

  /// Creates a TrackWelcomeUseCase
  ///
  /// @param welcomeRepository Repository for welcome operations
  const TrackWelcomeUseCase(this._welcomeRepository);

  /// Track welcome screen view
  ///
  /// Records that the user has viewed the welcome screen
  /// Updates view count and timestamps appropriately
  ///
  /// @return A Future that resolves to the updated WelcomeState entity
  Future<WelcomeState> trackWelcomeView() async {
    try {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Tracking welcome screen view');
      }

      // Get current welcome state
      final currentState = await _welcomeRepository.getWelcomeState();

      if (kDebugMode) {
        print('TrackWelcomeUseCase: Current state retrieved');
        print('  - Has seen welcome: ${currentState.hasSeenWelcome}');
        print('  - View count: ${currentState.viewCount}');
        print('  - Is first time: ${currentState.isFirstTime}');
      }

      // Create updated state based on current state
      final WelcomeState updatedState;
      
      if (currentState.isFirstTime) {
        // First time viewing welcome
        updatedState = WelcomeState.firstTime();
        if (kDebugMode) {
          print('TrackWelcomeUseCase: First time welcome view');
        }
      } else {
        // Returning user viewing welcome
        updatedState = WelcomeState.returning(
          firstShownAt: currentState.firstShownAt ?? DateTime.now(),
          viewCount: currentState.viewCount,
        );
        if (kDebugMode) {
          print('TrackWelcomeUseCase: Returning user welcome view');
        }
      }

      // Save updated state
      await _welcomeRepository.saveWelcomeState(updatedState);

      if (kDebugMode) {
        print('TrackWelcomeUseCase: Welcome view tracked successfully');
        print('  - Updated view count: ${updatedState.viewCount}');
      }

      return updatedState;
    } catch (e) {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Error tracking welcome view: $e');
      }
      
      // Return first time state on error
      return WelcomeState.firstTime();
    }
  }

  /// Complete welcome screen
  ///
  /// Marks the welcome screen as completed when user proceeds to authentication
  /// Updates the state to indicate successful completion
  ///
  /// @return A Future that resolves to the completed WelcomeState entity
  Future<WelcomeState> completeWelcome() async {
    try {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Completing welcome screen');
      }

      // Get current state and mark as completed
      final currentState = await _welcomeRepository.getWelcomeState();
      final completedState = currentState.markAsCompleted();

      // Save completed state
      await _welcomeRepository.saveWelcomeState(completedState);

      if (kDebugMode) {
        print('TrackWelcomeUseCase: Welcome completed successfully');
        print('  - Has seen welcome: ${completedState.hasSeenWelcome}');
      }

      return completedState;
    } catch (e) {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Error completing welcome: $e');
      }
      rethrow;
    }
  }

  /// Get current welcome state
  ///
  /// Retrieves the current welcome screen state for display purposes
  /// Used to determine UI state and navigation options
  ///
  /// @return A Future that resolves to the current WelcomeState entity
  Future<WelcomeState> getCurrentState() async {
    try {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Getting current welcome state');
      }

      final state = await _welcomeRepository.getWelcomeState();

      if (kDebugMode) {
        print('TrackWelcomeUseCase: Current state retrieved');
        print('  - State: $state');
      }

      return state;
    } catch (e) {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Error getting current state: $e');
      }
      
      // Return first time state on error
      return WelcomeState.firstTime();
    }
  }

  /// Check if user has seen welcome
  ///
  /// Simple check to determine if user has previously seen the welcome screen
  /// Used for navigation decisions
  ///
  /// @return A Future that resolves to true if user has seen welcome
  Future<bool> hasSeenWelcome() async {
    try {
      return await _welcomeRepository.hasSeenWelcome();
    } catch (e) {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Error checking if user has seen welcome: $e');
      }
      return false;
    }
  }

  /// Clear welcome state
  ///
  /// Removes stored welcome state data
  /// Used for testing or when resetting user experience
  ///
  /// @return A Future that completes when data is cleared
  Future<void> clearWelcomeState() async {
    try {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Clearing welcome state');
      }

      await _welcomeRepository.clearWelcomeState();

      if (kDebugMode) {
        print('TrackWelcomeUseCase: Welcome state cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TrackWelcomeUseCase: Error clearing welcome state: $e');
      }
      rethrow;
    }
  }
}
