/// Welcome State Entity
///
/// Represents the welcome screen state in the domain layer
/// This is a pure business entity without any framework dependencies
library welcome_state_entity;

/// Welcome State Entity
///
/// Contains information about the welcome screen state and user interaction
/// Used by use cases to track welcome screen completion
class WelcomeState {
  /// Whether the user has seen the welcome screen
  final bool hasSeenWelcome;

  /// Timestamp when welcome screen was first shown
  final DateTime? firstShownAt;

  /// Timestamp when user last interacted with welcome screen
  final DateTime? lastInteractionAt;

  /// Number of times user has seen the welcome screen
  final int viewCount;

  /// Creates a WelcomeState entity
  ///
  /// @param hasSeenWelcome Whether the user has seen the welcome screen
  /// @param firstShownAt Timestamp when welcome screen was first shown
  /// @param lastInteractionAt Timestamp when user last interacted with welcome screen
  /// @param viewCount Number of times user has seen the welcome screen
  const WelcomeState({
    required this.hasSeenWelcome,
    this.firstShownAt,
    this.lastInteractionAt,
    required this.viewCount,
  });

  /// Creates a WelcomeState for first-time user
  ///
  /// Used when user hasn't seen the welcome screen before
  factory WelcomeState.firstTime() {
    return WelcomeState(
      hasSeenWelcome: false,
      firstShownAt: DateTime.now(),
      lastInteractionAt: DateTime.now(),
      viewCount: 1,
    );
  }

  /// Creates a WelcomeState for returning user
  ///
  /// Used when user has already seen the welcome screen
  factory WelcomeState.returning({
    required DateTime firstShownAt,
    required int viewCount,
  }) {
    return WelcomeState(
      hasSeenWelcome: true,
      firstShownAt: firstShownAt,
      lastInteractionAt: DateTime.now(),
      viewCount: viewCount + 1,
    );
  }

  /// Creates a WelcomeState with completed interaction
  ///
  /// Used when user completes the welcome screen
  WelcomeState markAsCompleted() {
    return WelcomeState(
      hasSeenWelcome: true,
      firstShownAt: firstShownAt ?? DateTime.now(),
      lastInteractionAt: DateTime.now(),
      viewCount: viewCount,
    );
  }

  /// Checks if this is the user's first time seeing welcome
  ///
  /// @return True if this is the first time
  bool get isFirstTime => viewCount <= 1 && !hasSeenWelcome;

  /// Gets the duration since first shown
  ///
  /// @return Duration since first shown, or null if never shown
  Duration? get timeSinceFirstShown {
    if (firstShownAt == null) return null;
    return DateTime.now().difference(firstShownAt!);
  }

  /// Gets the duration since last interaction
  ///
  /// @return Duration since last interaction, or null if never interacted
  Duration? get timeSinceLastInteraction {
    if (lastInteractionAt == null) return null;
    return DateTime.now().difference(lastInteractionAt!);
  }

  /// Creates a copy of the entity with updated values
  ///
  /// @param hasSeenWelcome New seen status
  /// @param firstShownAt New first shown timestamp
  /// @param lastInteractionAt New last interaction timestamp
  /// @param viewCount New view count
  /// @return New WelcomeState instance
  WelcomeState copyWith({
    bool? hasSeenWelcome,
    DateTime? firstShownAt,
    DateTime? lastInteractionAt,
    int? viewCount,
  }) {
    return WelcomeState(
      hasSeenWelcome: hasSeenWelcome ?? this.hasSeenWelcome,
      firstShownAt: firstShownAt ?? this.firstShownAt,
      lastInteractionAt: lastInteractionAt ?? this.lastInteractionAt,
      viewCount: viewCount ?? this.viewCount,
    );
  }

  @override
  String toString() {
    return 'WelcomeState(hasSeenWelcome: $hasSeenWelcome, firstShownAt: $firstShownAt, '
           'lastInteractionAt: $lastInteractionAt, viewCount: $viewCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is WelcomeState &&
        other.hasSeenWelcome == hasSeenWelcome &&
        other.firstShownAt == firstShownAt &&
        other.lastInteractionAt == lastInteractionAt &&
        other.viewCount == viewCount;
  }

  @override
  int get hashCode {
    return hasSeenWelcome.hashCode ^
        firstShownAt.hashCode ^
        lastInteractionAt.hashCode ^
        viewCount.hashCode;
  }
}
