/// Feature Description Widget
///
/// Reusable widget for displaying app feature descriptions
/// Shows an icon, title, and description for each app feature
library feature_description_widget;

import 'package:flutter/material.dart';

/// Widget for displaying app feature descriptions
///
/// Shows a feature with icon, title, and description
/// Used in the welcome screen to highlight app capabilities
class FeatureDescriptionWidget extends StatelessWidget {
  /// Icon to display for the feature (emoji or icon)
  final String icon;

  /// Title of the feature
  final String title;

  /// Description of the feature
  final String desc;

  /// Custom text style for the title (optional)
  final TextStyle? titleStyle;

  /// Custom text style for the description (optional)
  final TextStyle? descriptionStyle;

  /// Custom text style for the icon (optional)
  final TextStyle? iconStyle;

  /// Padding around the entire widget
  final EdgeInsetsGeometry? padding;

  /// Spacing between icon and text content
  final double iconSpacing;

  /// Spacing between title and description
  final double titleDescriptionSpacing;

  /// Creates a FeatureDescriptionWidget
  ///
  /// @param icon Icon to display (emoji or icon character)
  /// @param title Title of the feature
  /// @param desc Description of the feature
  /// @param titleStyle Custom text style for the title
  /// @param descriptionStyle Custom text style for the description
  /// @param iconStyle Custom text style for the icon
  /// @param padding Padding around the widget
  /// @param iconSpacing Spacing between icon and text content
  /// @param titleDescriptionSpacing Spacing between title and description
  const FeatureDescriptionWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.desc,
    this.titleStyle,
    this.descriptionStyle,
    this.iconStyle,
    this.padding,
    this.iconSpacing = 16.0,
    this.titleDescriptionSpacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 35),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Feature icon
          Text(
            icon,
            style: iconStyle ??
                const TextStyle(
                  fontSize: 24,
                ),
          ),

          SizedBox(width: iconSpacing),

          // Feature content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Feature title
                Text(
                  title,
                  style: titleStyle ??
                      const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.purple,
                      ),
                  textAlign: TextAlign.right,
                ),

                SizedBox(height: titleDescriptionSpacing),

                // Feature description
                Text(
                  desc,
                  style: descriptionStyle ??
                      const TextStyle(
                        fontSize: 14,
                        height: 1.4,
                        color: Colors.grey,
                      ),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact version of FeatureDescriptionWidget
///
/// Shows feature information in a more compact layout
/// Useful for smaller spaces or summary views
class CompactFeatureDescriptionWidget extends StatelessWidget {
  /// Icon to display for the feature
  final String icon;

  /// Title of the feature
  final String title;

  /// Description of the feature
  final String desc;

  /// Whether to show the description
  final bool showDescription;

  /// Creates a CompactFeatureDescriptionWidget
  ///
  /// @param icon Icon to display
  /// @param title Title of the feature
  /// @param desc Description of the feature
  /// @param showDescription Whether to show the description
  const CompactFeatureDescriptionWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.desc,
    this.showDescription = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          // Feature icon
          Text(
            icon,
            style: const TextStyle(fontSize: 20),
          ),

          const SizedBox(width: 12),

          // Feature content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Feature title
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.purple,
                  ),
                  textAlign: TextAlign.right,
                ),

                if (showDescription) ...[
                  const SizedBox(height: 4),
                  // Feature description
                  Text(
                    desc,
                    style: const TextStyle(
                      fontSize: 12,
                      height: 1.3,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.right,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Horizontal feature description widget
///
/// Shows feature information in a horizontal card layout
/// Useful for feature highlights or promotional sections
class HorizontalFeatureDescriptionWidget extends StatelessWidget {
  /// Icon to display for the feature
  final String icon;

  /// Title of the feature
  final String title;

  /// Description of the feature
  final String desc;

  /// Background color for the card
  final Color? backgroundColor;

  /// Border radius for the card
  final double borderRadius;

  /// Creates a HorizontalFeatureDescriptionWidget
  ///
  /// @param icon Icon to display
  /// @param title Title of the feature
  /// @param desc Description of the feature
  /// @param backgroundColor Background color for the card
  /// @param borderRadius Border radius for the card
  const HorizontalFeatureDescriptionWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.desc,
    this.backgroundColor,
    this.borderRadius = 12.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey[50],
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Feature icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Feature content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Feature title
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.purple,
                  ),
                  textAlign: TextAlign.right,
                ),

                const SizedBox(height: 4),

                // Feature description
                Text(
                  desc,
                  style: const TextStyle(
                    fontSize: 13,
                    height: 1.3,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
