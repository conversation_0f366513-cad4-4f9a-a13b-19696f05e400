/// Welcome View (Riverpod Version)
///
/// Riverpod version of the welcome screen following MVVM pattern
/// Single page welcome screen with Get Started button
library welcome_view_riverpod;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/features/welcome/presentation/providers/welcome_provider.dart';
import 'package:towasl/features/authentication/presentation/views/signup_login_view.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/mixins/analytics_mixin.dart';

/// Welcome View (Riverpod Version)
///
/// Single page welcome screen that introduces users to the app
/// Follows MVVM pattern with Riverpod providers instead of GetX
class WelcomeView extends ConsumerStatefulWidget {
  /// Creates a WelcomeView
  const WelcomeView({super.key});

  @override
  ConsumerState<WelcomeView> createState() => _WelcomeViewState();
}

/// State for the WelcomeView
class _WelcomeViewState extends ConsumerState<WelcomeView>
    with TickerProviderStateMixin, AnalyticsMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Track screen view
    trackScreenView(screenName: AnalyticsScreenNames.welcome);

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start animation
    _animationController.forward();

    // Start welcome animation in provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(welcomeProvider.notifier).startAnimation();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Welcome content
                _buildWelcomeContent(),

                const SizedBox(height: 60),

                // Get Started button
                _buildGetStartedButton(),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build welcome content
  Widget _buildWelcomeContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // App logo/icon placeholder
        Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: AppColors.primaryPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(100),
          ),
          child: const Icon(
            Icons.favorite,
            size: 80,
            color: AppColors.primaryPurple,
          ),
        ),

        const SizedBox(height: 50),

        // Welcome title
        Text(
          AppLocalizations.of(context).welcomeToTowasl,
          style: const TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: AppColors.greyDark,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 20),

        // Welcome description
        Text(
          AppLocalizations.of(context).welcomeDescription,
          style: const TextStyle(
            fontSize: 18,
            color: AppColors.greyMedium,
            height: 1.6,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build Get Started button
  Widget _buildGetStartedButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () {
          // Log button click
          logButtonClick(
            buttonName: 'get_started',
            screenName: AnalyticsScreenNames.welcome,
          );

          // Log navigation
          logNavigation(
            from: AnalyticsScreenNames.welcome,
            to: AnalyticsScreenNames.signupLogin,
          );

          // Navigate directly to signup/login
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const SignupLoginView(),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryPurple,
          foregroundColor: AppColors.whitePure,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Text(
          AppLocalizations.of(context).getStarted,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
