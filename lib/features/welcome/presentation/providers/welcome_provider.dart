/// Welcome Provider
///
/// Provides Riverpod state management for welcome screen functionality
/// Replaces WelcomeViewModel with Riverpod architecture
library welcome_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/routes.dart';

part 'welcome_provider.g.dart';

/// State class for welcome screen
class WelcomeState {
  /// Whether welcome animation is playing
  final bool isAnimating;

  /// Error message if any
  final String? errorMessage;

  const WelcomeState({
    this.isAnimating = false,
    this.errorMessage,
  });

  WelcomeState copyWith({
    bool? isAnimating,
    String? errorMessage,
  }) {
    return WelcomeState(
      isAnimating: isAnimating ?? this.isAnimating,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  WelcomeState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// Welcome Notifier
///
/// Manages welcome screen state and business logic
/// Follows MVVM pattern with Riverpod state management
@riverpod
class Welcome extends _$Welcome {
  @override
  WelcomeState build() {
    if (kDebugMode) {
      print('WelcomeNotifier: Initialized');
    }

    return const WelcomeState();
  }

  /// Start welcome animation
  void startAnimation() {
    state = state.copyWith(isAnimating: true);

    if (kDebugMode) {
      print('WelcomeNotifier: Animation started');
    }
  }

  /// Stop welcome animation
  void stopAnimation() {
    state = state.copyWith(isAnimating: false);

    if (kDebugMode) {
      print('WelcomeNotifier: Animation stopped');
    }
  }

  /// Navigate to signup/login screen
  void navigateToSignupLogin() {
    if (kDebugMode) {
      print('WelcomeNotifier: Navigating to signup/login');
    }

    ref.read(navigationProvider.notifier).navigateTo(AppRoutes.signupLogin);
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for animation state
@riverpod
bool isWelcomeAnimating(IsWelcomeAnimatingRef ref) {
  return ref.watch(welcomeProvider).isAnimating;
}

/// Provider for welcome error message
@riverpod
String? welcomeErrorMessage(WelcomeErrorMessageRef ref) {
  return ref.watch(welcomeProvider).errorMessage;
}
