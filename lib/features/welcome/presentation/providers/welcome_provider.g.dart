// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'welcome_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isWelcomeAnimatingHash() =>
    r'c7fe5d1bf41f74904c37fec40e754790c9b98fd9';

/// Provider for animation state
///
/// Copied from [isWelcomeAnimating].
@ProviderFor(isWelcomeAnimating)
final isWelcomeAnimatingProvider = AutoDisposeProvider<bool>.internal(
  isWelcomeAnimating,
  name: r'isWelcomeAnimatingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isWelcomeAnimatingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsWelcomeAnimatingRef = AutoDisposeProviderRef<bool>;
String _$welcomeErrorMessageHash() =>
    r'd1fd481d4669ce69e8e00f93171daa5e157fb40b';

/// Provider for welcome error message
///
/// Copied from [welcomeErrorMessage].
@ProviderFor(welcomeErrorMessage)
final welcomeErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  welcomeErrorMessage,
  name: r'welcomeErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$welcomeErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WelcomeErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$welcomeHash() => r'e3d108c6bf50795cc67f1f1fd9c449582fe06d4b';

/// Welcome Notifier
///
/// Manages welcome screen state and business logic
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [Welcome].
@ProviderFor(Welcome)
final welcomeProvider =
    AutoDisposeNotifierProvider<Welcome, WelcomeState>.internal(
  Welcome.new,
  name: r'welcomeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$welcomeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Welcome = AutoDisposeNotifier<WelcomeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
