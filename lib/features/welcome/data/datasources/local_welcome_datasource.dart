/// Local Welcome Data Source
///
/// Handles local storage operations for welcome screen data
/// Provides methods for storing and retrieving welcome state
library local_welcome_datasource;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/storage_service.dart';
import 'package:towasl/features/welcome/data/models/welcome_state_model.dart';

/// Local data source for welcome screen operations
///
/// Handles reading and writing welcome state data to local storage
/// Integrates with existing storage services
abstract class LocalWelcomeDataSource {
  /// Get welcome state from local storage
  ///
  /// @return A Future that resolves to a WelcomeStateModel
  Future<WelcomeStateModel> getWelcomeState();

  /// Save welcome state to local storage
  ///
  /// @param welcomeState The welcome state to save
  /// @return A Future that completes when data is saved
  Future<void> saveWelcomeState(WelcomeStateModel welcomeState);

  /// Clear welcome state from local storage
  ///
  /// @return A Future that completes when data is cleared
  Future<void> clearWelcomeState();

  /// Check if user has seen welcome
  ///
  /// @return A Future that resolves to true if user has seen welcome
  Future<bool> hasSeenWelcome();

  /// Increment view count
  ///
  /// @return A Future that completes when count is updated
  Future<void> incrementViewCount();
}

/// Implementation of LocalWelcomeDataSource
///
/// Concrete implementation that uses StorageService for local storage operations
class LocalWelcomeDataSourceImpl implements LocalWelcomeDataSource {
  /// Storage service for local data operations
  final StorageService _storageService;

  /// Storage key for welcome state data
  static const String _welcomeStateKey = 'welcome_state';

  /// Creates a LocalWelcomeDataSourceImpl
  ///
  /// @param storageService Service for local storage operations
  const LocalWelcomeDataSourceImpl(this._storageService);

  @override
  Future<WelcomeStateModel> getWelcomeState() async {
    try {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Getting welcome state');
      }

      // Get welcome state data from storage
      final data = _storageService.read(_welcomeStateKey);

      if (kDebugMode) {
        print('LocalWelcomeDataSource: Raw data retrieved: $data');
      }

      // Convert storage data to model
      final welcomeState = WelcomeStateModel.fromStorageData(data);

      if (kDebugMode) {
        print('LocalWelcomeDataSource: Welcome state retrieved');
        print('  - Has seen welcome: ${welcomeState.hasSeenWelcome}');
        print('  - View count: ${welcomeState.viewCount}');
        print('  - Is first time: ${welcomeState.isFirstTime}');
      }

      return welcomeState;
    } catch (e) {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Error getting welcome state: $e');
      }

      // Return first time state on error
      return WelcomeStateModel.firstTime();
    }
  }

  @override
  Future<void> saveWelcomeState(WelcomeStateModel welcomeState) async {
    try {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Saving welcome state');
        print('  - State: $welcomeState');
      }

      // Convert model to storage data and save
      final storageData = welcomeState.toStorageData();
      await _storageService.write(_welcomeStateKey, storageData);

      if (kDebugMode) {
        print('LocalWelcomeDataSource: Welcome state saved successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Error saving welcome state: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> clearWelcomeState() async {
    try {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Clearing welcome state');
      }

      await _storageService.remove(_welcomeStateKey);

      if (kDebugMode) {
        print('LocalWelcomeDataSource: Welcome state cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Error clearing welcome state: $e');
      }
      rethrow;
    }
  }

  @override
  Future<bool> hasSeenWelcome() async {
    try {
      final welcomeState = await getWelcomeState();
      return welcomeState.hasSeenWelcome;
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocalWelcomeDataSource: Error checking if user has seen welcome: $e');
      }
      return false;
    }
  }

  @override
  Future<void> incrementViewCount() async {
    try {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Incrementing view count');
      }

      // Get current state and increment view count
      final currentState = await getWelcomeState();
      final updatedState = currentState.copyWith(
        viewCount: currentState.viewCount + 1,
        lastInteractionAt: DateTime.now(),
      );

      // Save updated state
      await saveWelcomeState(updatedState);

      if (kDebugMode) {
        print(
            'LocalWelcomeDataSource: View count incremented to ${updatedState.viewCount}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LocalWelcomeDataSource: Error incrementing view count: $e');
      }
      rethrow;
    }
  }
}
