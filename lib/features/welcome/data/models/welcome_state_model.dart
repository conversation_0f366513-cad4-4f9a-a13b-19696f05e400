/// Welcome State Model
///
/// Data model for welcome state with serialization capabilities
/// Extends the domain entity with JSON serialization for data persistence
library welcome_state_model;

import 'package:towasl/features/welcome/domain/entities/welcome_state.dart';

/// Welcome State Data Model
///
/// Extends WelcomeState entity with serialization capabilities
/// Used for converting between domain entities and data storage formats
class WelcomeStateModel extends WelcomeState {
  /// Creates a WelcomeStateModel
  ///
  /// @param hasSeenWelcome Whether the user has seen the welcome screen
  /// @param firstShownAt Timestamp when welcome screen was first shown
  /// @param lastInteractionAt Timestamp when user last interacted with welcome screen
  /// @param viewCount Number of times user has seen the welcome screen
  const WelcomeStateModel({
    required super.hasSeenWelcome,
    super.firstShownAt,
    super.lastInteractionAt,
    required super.viewCount,
  });

  /// Creates a WelcomeStateModel from domain entity
  ///
  /// @param entity The domain entity to convert
  /// @return WelcomeStateModel instance
  factory WelcomeStateModel.fromEntity(WelcomeState entity) {
    return WelcomeStateModel(
      hasSeenWelcome: entity.hasSeenWelcome,
      firstShownAt: entity.firstShownAt,
      lastInteractionAt: entity.lastInteractionAt,
      viewCount: entity.viewCount,
    );
  }

  /// Creates a WelcomeStateModel from JSON
  ///
  /// @param json Map containing the welcome state data
  /// @return WelcomeStateModel instance
  factory WelcomeStateModel.fromJson(Map<String, dynamic> json) {
    return WelcomeStateModel(
      hasSeenWelcome: json['hasSeenWelcome'] as bool? ?? false,
      firstShownAt: json['firstShownAt'] != null
          ? DateTime.parse(json['firstShownAt'] as String)
          : null,
      lastInteractionAt: json['lastInteractionAt'] != null
          ? DateTime.parse(json['lastInteractionAt'] as String)
          : null,
      viewCount: json['viewCount'] as int? ?? 0,
    );
  }

  /// Converts the model to JSON
  ///
  /// @return Map containing the welcome state data
  Map<String, dynamic> toJson() {
    return {
      'hasSeenWelcome': hasSeenWelcome,
      'firstShownAt': firstShownAt?.toIso8601String(),
      'lastInteractionAt': lastInteractionAt?.toIso8601String(),
      'viewCount': viewCount,
    };
  }

  /// Creates a WelcomeStateModel for first-time user
  ///
  /// @return WelcomeStateModel with first-time state
  factory WelcomeStateModel.firstTime() {
    return WelcomeStateModel(
      hasSeenWelcome: false,
      firstShownAt: DateTime.now(),
      lastInteractionAt: DateTime.now(),
      viewCount: 1,
    );
  }

  /// Creates a WelcomeStateModel for returning user
  ///
  /// @param firstShownAt When welcome was first shown
  /// @param viewCount Current view count
  /// @return WelcomeStateModel with returning user state
  factory WelcomeStateModel.returning({
    required DateTime firstShownAt,
    required int viewCount,
  }) {
    return WelcomeStateModel(
      hasSeenWelcome: true,
      firstShownAt: firstShownAt,
      lastInteractionAt: DateTime.now(),
      viewCount: viewCount + 1,
    );
  }

  /// Converts the model to domain entity
  ///
  /// @return WelcomeState domain entity
  WelcomeState toEntity() {
    return WelcomeState(
      hasSeenWelcome: hasSeenWelcome,
      firstShownAt: firstShownAt,
      lastInteractionAt: lastInteractionAt,
      viewCount: viewCount,
    );
  }

  /// Creates a copy of the model with updated values
  ///
  /// @param hasSeenWelcome New seen status
  /// @param firstShownAt New first shown timestamp
  /// @param lastInteractionAt New last interaction timestamp
  /// @param viewCount New view count
  /// @return New WelcomeStateModel instance
  @override
  WelcomeStateModel copyWith({
    bool? hasSeenWelcome,
    DateTime? firstShownAt,
    DateTime? lastInteractionAt,
    int? viewCount,
  }) {
    return WelcomeStateModel(
      hasSeenWelcome: hasSeenWelcome ?? this.hasSeenWelcome,
      firstShownAt: firstShownAt ?? this.firstShownAt,
      lastInteractionAt: lastInteractionAt ?? this.lastInteractionAt,
      viewCount: viewCount ?? this.viewCount,
    );
  }

  /// Creates a WelcomeStateModel from storage data
  ///
  /// Handles legacy storage formats and provides defaults
  ///
  /// @param data Raw data from storage
  /// @return WelcomeStateModel instance
  factory WelcomeStateModel.fromStorageData(dynamic data) {
    if (data == null) {
      return WelcomeStateModel.firstTime();
    }

    // Handle boolean legacy format (just tracking if seen)
    if (data is bool) {
      return WelcomeStateModel(
        hasSeenWelcome: data,
        firstShownAt: data ? DateTime.now() : null,
        lastInteractionAt: data ? DateTime.now() : null,
        viewCount: data ? 1 : 0,
      );
    }

    // Handle integer legacy format (just view count)
    if (data is int) {
      return WelcomeStateModel(
        hasSeenWelcome: data > 0,
        firstShownAt: data > 0 ? DateTime.now() : null,
        lastInteractionAt: data > 0 ? DateTime.now() : null,
        viewCount: data,
      );
    }

    // Handle map format
    if (data is Map<String, dynamic>) {
      return WelcomeStateModel.fromJson(data);
    }

    // Default to first time for unknown formats
    return WelcomeStateModel.firstTime();
  }

  /// Converts the model to storage data
  ///
  /// @return Data suitable for storage
  Map<String, dynamic> toStorageData() {
    return toJson();
  }

  @override
  String toString() {
    return 'WelcomeStateModel(hasSeenWelcome: $hasSeenWelcome, firstShownAt: $firstShownAt, '
        'lastInteractionAt: $lastInteractionAt, viewCount: $viewCount)';
  }
}
