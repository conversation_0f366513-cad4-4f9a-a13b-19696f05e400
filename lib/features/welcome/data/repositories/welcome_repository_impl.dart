/// Welcome Repository Implementation
///
/// Implements the WelcomeRepository interface using local data sources
/// Provides concrete implementation for welcome data operations
library welcome_repository_impl;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/welcome/domain/entities/welcome_state.dart';
import 'package:towasl/features/welcome/domain/repositories/welcome_repository.dart';
import 'package:towasl/features/welcome/data/datasources/local_welcome_datasource.dart';
import 'package:towasl/features/welcome/data/models/welcome_state_model.dart';

/// Implementation of WelcomeRepository
///
/// Concrete implementation that uses LocalWelcomeDataSource
/// to provide welcome data operations
class WelcomeRepositoryImpl implements WelcomeRepository {
  /// Local data source for welcome operations
  final LocalWelcomeDataSource _localDataSource;

  /// Creates a WelcomeRepositoryImpl
  ///
  /// @param localDataSource Data source for local welcome operations
  const WelcomeRepositoryImpl(this._localDataSource);

  @override
  Future<WelcomeState> getWelcomeState() async {
    try {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Getting welcome state');
      }

      // Get welcome state from local data source
      final welcomeStateModel = await _localDataSource.getWelcomeState();

      // Convert model to domain entity
      final welcomeState = welcomeStateModel.toEntity();

      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Welcome state retrieved');
        print('  - State: $welcomeState');
      }

      return welcomeState;
    } catch (e) {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Error getting welcome state: $e');
      }

      // Return first time state on error
      return WelcomeState.firstTime();
    }
  }

  @override
  Future<void> saveWelcomeState(WelcomeState welcomeState) async {
    try {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Saving welcome state');
        print('  - State: $welcomeState');
      }

      // Convert domain entity to model and save
      final welcomeStateModel = WelcomeStateModel.fromEntity(welcomeState);
      await _localDataSource.saveWelcomeState(welcomeStateModel);

      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Welcome state saved successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Error saving welcome state: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> markWelcomeAsCompleted() async {
    try {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Marking welcome as completed');
      }

      // Get current state and mark as completed
      final currentState = await getWelcomeState();
      final completedState = currentState.markAsCompleted();

      // Save completed state
      await saveWelcomeState(completedState);

      if (kDebugMode) {
        print(
            'WelcomeRepositoryImpl: Welcome marked as completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Error marking welcome as completed: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> clearWelcomeState() async {
    try {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Clearing welcome state');
      }

      await _localDataSource.clearWelcomeState();

      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Welcome state cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Error clearing welcome state: $e');
      }
      rethrow;
    }
  }

  @override
  Future<bool> hasSeenWelcome() async {
    try {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Checking if user has seen welcome');
      }

      final hasSeenWelcome = await _localDataSource.hasSeenWelcome();

      if (kDebugMode) {
        print('WelcomeRepositoryImpl: User has seen welcome: $hasSeenWelcome');
      }

      return hasSeenWelcome;
    } catch (e) {
      if (kDebugMode) {
        print(
            'WelcomeRepositoryImpl: Error checking if user has seen welcome: $e');
      }
      return false;
    }
  }

  @override
  Future<void> incrementViewCount() async {
    try {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Incrementing view count');
      }

      await _localDataSource.incrementViewCount();

      if (kDebugMode) {
        print('WelcomeRepositoryImpl: View count incremented successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WelcomeRepositoryImpl: Error incrementing view count: $e');
      }
      rethrow;
    }
  }
}
