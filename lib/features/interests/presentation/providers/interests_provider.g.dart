// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interests_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$displayInterestsHash() => r'ae3d945832c34a1375232dde9a673547fd3c99f7';

/// Provider for display interests model
///
/// Copied from [displayInterests].
@ProviderFor(displayInterests)
final displayInterestsProvider = Provider<InterestsDisplayModel?>.internal(
  displayInterests,
  name: r'displayInterestsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$displayInterestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DisplayInterestsRef = ProviderRef<InterestsDisplayModel?>;
String _$selectedInterestsHash() => r'1e0aa7cb18d64084f503dac90e295790ef101140';

/// Provider for selected interests
///
/// Copied from [selectedInterests].
@ProviderFor(selectedInterests)
final selectedInterestsProvider = Provider<List<SelectedInterest>>.internal(
  selectedInterests,
  name: r'selectedInterestsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedInterestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SelectedInterestsRef = ProviderRef<List<SelectedInterest>>;
String _$isInterestsLoadingHash() =>
    r'6616321ed400991d0526894bd7e2940f3fdc92ec';

/// Provider for interests loading state
///
/// Copied from [isInterestsLoading].
@ProviderFor(isInterestsLoading)
final isInterestsLoadingProvider = Provider<bool>.internal(
  isInterestsLoading,
  name: r'isInterestsLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isInterestsLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsInterestsLoadingRef = ProviderRef<bool>;
String _$isInterestsSavingHash() => r'292540466bb8bf5888dbfd22f1121e11aa4fd88a';

/// Provider for interests saving state
///
/// Copied from [isInterestsSaving].
@ProviderFor(isInterestsSaving)
final isInterestsSavingProvider = Provider<bool>.internal(
  isInterestsSaving,
  name: r'isInterestsSavingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isInterestsSavingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsInterestsSavingRef = ProviderRef<bool>;
String _$totalSelectedInterestsHash() =>
    r'9d565591d544834e7077392686d0d91499cd01a7';

/// Provider for total selected interests count
///
/// Copied from [totalSelectedInterests].
@ProviderFor(totalSelectedInterests)
final totalSelectedInterestsProvider = Provider<int>.internal(
  totalSelectedInterests,
  name: r'totalSelectedInterestsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$totalSelectedInterestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef TotalSelectedInterestsRef = ProviderRef<int>;
String _$hasMinimumInterestsHash() =>
    r'6c3f6fa6beb8ad90ed84e6821974ad818c25e7b9';

/// Provider for minimum interests validation
///
/// Copied from [hasMinimumInterests].
@ProviderFor(hasMinimumInterests)
final hasMinimumInterestsProvider = Provider<bool>.internal(
  hasMinimumInterests,
  name: r'hasMinimumInterestsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasMinimumInterestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasMinimumInterestsRef = ProviderRef<bool>;
String _$interestsErrorMessageHash() =>
    r'70681fe84cf427ca36f71d1b0d2da1f6ef9002a8';

/// Provider for interests error message
///
/// Copied from [interestsErrorMessage].
@ProviderFor(interestsErrorMessage)
final interestsErrorMessageProvider = Provider<String?>.internal(
  interestsErrorMessage,
  name: r'interestsErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$interestsErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InterestsErrorMessageRef = ProviderRef<String?>;
String _$interestsHash() => r'88c971085f62f7df49444e561a1c249ca395ee48';

/// Interests Notifier
///
/// Manages interests selection state and business logic
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [Interests].
@ProviderFor(Interests)
final interestsProvider = NotifierProvider<Interests, InterestsState>.internal(
  Interests.new,
  name: r'interestsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$interestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Interests = Notifier<InterestsState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
