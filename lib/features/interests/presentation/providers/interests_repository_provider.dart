/// Interests Repository Provider
///
/// Provides Riverpod providers for interests repository
/// Replaces GetX dependency injection for interests repository
library interests_repository_provider;

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/interests/domain/repositories/interests_repository.dart';
import 'package:towasl/features/interests/data/repositories/interests_repository_impl.dart';

part 'interests_repository_provider.g.dart';

/// Interests Repository Provider
///
/// Provides the interests repository implementation
@riverpod
InterestsRepository interestsRepository(InterestsRepositoryRef ref) {
  return InterestsRepositoryImpl(
    firebaseService: ref.read(firebaseServiceProvider),
  );
}
