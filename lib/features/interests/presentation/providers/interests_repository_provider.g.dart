// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interests_repository_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$interestsRepositoryHash() =>
    r'2ac6014cffd79bcc186b9c9afe0e8c2dbf0fb8e2';

/// Interests Repository Provider
///
/// Provides the interests repository implementation
///
/// Copied from [interestsRepository].
@ProviderFor(interestsRepository)
final interestsRepositoryProvider =
    AutoDisposeProvider<InterestsRepository>.internal(
  interestsRepository,
  name: r'interestsRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$interestsRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InterestsRepositoryRef = AutoDisposeProviderRef<InterestsRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
