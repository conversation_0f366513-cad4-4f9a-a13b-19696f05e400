/// Interests View (Riverpod Version)
///
/// Riverpod version of the interests selection screen following MVVM pattern
/// Demonstrates migration from GetX to Riverpod for interests functionality
library interests_view_riverpod;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/interests/presentation/providers/interests_provider.dart';
import 'package:towasl/features/interests/data/models/interests_display_model.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/widgets/bottom_action_bar.dart';

/// Interests View (Riverpod Version)
///
/// Screen for selecting user interests during onboarding
/// Follows MVVM pattern with Riverpod providers instead of GetX
class InterestsView extends ConsumerStatefulWidget {
  /// Whether this view is accessed from settings
  final bool isFromProfile;

  /// Creates an InterestsView
  const InterestsView({super.key, this.isFromProfile = false});

  @override
  ConsumerState<InterestsView> createState() => _InterestsViewState();
}

/// State for the InterestsView
class _InterestsViewState extends ConsumerState<InterestsView> {
  @override
  void initState() {
    super.initState();
    // Initialize interests data when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeInterests();
    });
  }

  /// Initialize interests data
  void _initializeInterests() async {
    // Always load display interests first
    ref.read(interestsProvider.notifier).loadDisplayInterests();

    // Try to get user ID from multiple sources
    String userId = '';

    // First try from userIdProvider
    try {
      userId = ref.read(userIdProvider);
    } catch (e) {
      if (kDebugMode) {
        print('InterestsView: Could not get user ID from userIdProvider - $e');
      }
    }

    // If still empty, try from storage service directly
    if (userId.isEmpty) {
      try {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();
        if (kDebugMode) {
          print('InterestsView: Got user ID from storage: $userId');
        }
      } catch (e) {
        if (kDebugMode) {
          print('InterestsView: Could not get user ID from storage - $e');
        }
      }
    }

    // Load user interests if we have a user ID
    if (userId.isNotEmpty) {
      if (kDebugMode) {
        print('InterestsView: Loading user interests for: $userId');
      }
      ref.read(interestsProvider.notifier).loadUserInterests(userId);
    } else {
      if (kDebugMode) {
        print(
            'InterestsView: No user ID found, skipping user interests loading');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final displayModel = ref.watch(displayInterestsProvider);
    final isLoading = ref.watch(isInterestsLoadingProvider);
    final isSaving = ref.watch(isInterestsSavingProvider);
    final totalSelected = ref.watch(totalSelectedInterestsProvider);
    final hasMinimum = ref.watch(hasMinimumInterestsProvider);

    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Column(
              children: [
                // Header section
                _buildHeader(totalSelected, hasMinimum),

                // Interests grid
                Expanded(
                  child: isLoading
                      ? _buildLoadingState()
                      : _buildInterestsGrid(displayModel),
                ),
              ],
            ),

            // Loading overlay
            if (isSaving)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: BottomActionBar(
        buttonText: AppLocalizations.of(context).continueText,
        isEnabled: hasMinimum,
        isLoading: isSaving,
        onPressed: hasMinimum
            ? () {
                ref.read(interestsProvider.notifier).saveInterests(
                      isFromProfile: widget.isFromProfile,
                    );
              }
            : null,
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryPurple,
      elevation: 0,
      automaticallyImplyLeading: false,
      title: Text(
        AppLocalizations.of(context).yourInterests,
        style: const TextStyle(
          color: AppColors.whitePure,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      // Show back button when coming from settings/profile
      leading: widget.isFromProfile
          ? IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: AppColors.whitePure,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            )
          : null,
    );
  }

  /// Build header section
  Widget _buildHeader(int totalSelected, bool hasMinimum) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).selectAtLeastThreeInterests,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.greyMedium,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryPurple),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).loadingInterests,
            style: const TextStyle(
              color: AppColors.greyMedium,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Build interests grid
  Widget _buildInterestsGrid(InterestsDisplayModel? displayModel) {
    if (displayModel == null || !displayModel.hasCategories) {
      return Center(
        child: Text(
          AppLocalizations.of(context).noInterestsAvailable,
          style: const TextStyle(
            color: AppColors.greyMedium,
            fontSize: 16,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: displayModel.categories.length,
      itemBuilder: (context, index) {
        final category = displayModel.categories[index];
        return _buildCategorySection(category);
      },
    );
  }

  /// Build category section
  Widget _buildCategorySection(CategoryDisplayModel category) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category title
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            category.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.greyDark,
            ),
          ),
        ),

        // Subcategory chips
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: category.subCategories.map((subCategory) {
            final isSelected = ref
                .watch(interestsProvider)
                .isSubCategorySelected(category.id, subCategory.id);

            return _buildInterestChip(
              categoryId: category.id,
              subCategoryId: subCategory.id,
              title: subCategory.title,
              isSelected: isSelected,
            );
          }).toList(),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  /// Build interest chip
  Widget _buildInterestChip({
    required String categoryId,
    required String subCategoryId,
    required String title,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        ref
            .read(interestsProvider.notifier)
            .toggleSubCategory(categoryId, subCategoryId);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryPurple : AppColors.whitePure,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected ? AppColors.primaryPurple : AppColors.greyLight,
            width: 2,
          ),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: AppColors.primaryPurple.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                color: isSelected ? AppColors.whitePure : AppColors.greyDark,
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
