/// Interests Repository Implementation
///
/// Implements the InterestsRepository interface using Firebase services
/// Provides concrete implementation for interests data operations
library interests_repository_impl;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:towasl/features/interests/domain/repositories/interests_repository.dart';
import 'package:towasl/features/interests/data/models/interests_display_model.dart';
import 'package:towasl/shared/models/selected_interest_model.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// Interests Repository Implementation
///
/// Implements the InterestsRepository interface using FirebaseService
class InterestsRepositoryImpl implements InterestsRepository {
  final FirebaseService _firebaseService;

  /// Constructor that takes a FirebaseService instance
  ///
  /// @param firebaseService The Firebase service for database operations
  InterestsRepositoryImpl({
    required FirebaseService firebaseService,
  }) : _firebaseService = firebaseService;

  @override
  Future<InterestsDisplayModel> getDisplayInterests(String countryLang) async {
    try {
      if (kDebugMode) {
        print(
            "=== InterestsRepositoryImpl.getDisplayInterests called for $countryLang ===");
      }

      // Get data directly from the country/language document
      // This document contains the complete catalog for that locale
      final doc = await _firebaseService.getDocument('interests', countryLang);

      if (!doc.exists) {
        if (kDebugMode) {
          print("Interests document for $countryLang does not exist");
        }
        return InterestsDisplayModel.empty();
      }

      final data = doc.data() as Map<String, dynamic>;
      final categoriesData = data['categories'] as Map<String, dynamic>?;

      if (categoriesData == null) {
        if (kDebugMode) {
          print("No categories found in $countryLang document");
        }
        return InterestsDisplayModel.empty();
      }

      // Convert to display model
      final displayCategories = <CategoryDisplayModel>[];

      categoriesData.forEach((categoryId, categoryData) {
        final categoryMap = categoryData as Map<String, dynamic>;
        final title = categoryMap['title'] as String? ?? '';
        final orderNumber = categoryMap['order_number'] as int? ?? 0;
        final subCategoriesData =
            categoryMap['sub_categories'] as Map<String, dynamic>?;

        if (subCategoriesData == null) return;

        // Process subcategories
        final displaySubCategories = <SubCategoryDisplayModel>[];

        subCategoriesData.forEach((subCategoryId, subCategoryData) {
          final subCategoryMap = subCategoryData as Map<String, dynamic>;
          final subTitle = subCategoryMap['title'] as String? ?? '';
          final subOrderNumber = subCategoryMap['order_number'] as int? ?? 0;

          displaySubCategories.add(SubCategoryDisplayModel(
            id: subCategoryId,
            title: subTitle,
            orderNumber: subOrderNumber,
          ));
        });

        // Sort subcategories by order number
        displaySubCategories
            .sort((a, b) => a.orderNumber.compareTo(b.orderNumber));

        // Add category if it has subcategories
        if (displaySubCategories.isNotEmpty) {
          displayCategories.add(CategoryDisplayModel(
            id: categoryId,
            title: title,
            orderNumber: orderNumber,
            subCategories: displaySubCategories,
          ));
        }
      });

      // Sort categories by order number
      displayCategories.sort((a, b) => a.orderNumber.compareTo(b.orderNumber));

      final result = InterestsDisplayModel(categories: displayCategories);

      if (kDebugMode) {
        print(
            "Display interests created with ${result.categories.length} categories");
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print("Error getting display interests: $e");
      }
      rethrow;
    }
  }

  @override
  Future<void> saveUserInterests(
      String userId, List<SelectedInterest> selectedInterests) async {
    try {
      if (kDebugMode) {
        print(
            "=== InterestsRepositoryImpl.saveUserInterests called for user $userId ===");
        print("Saving ${selectedInterests.length} selected interests");
      }

      final data = {
        'selected_interests':
            selectedInterests.map((interest) => interest.toJson()).toList(),
        'updated_at': Timestamp.now(),
      };

      await _firebaseService.updateDocument('users', userId, data);

      if (kDebugMode) {
        print("User interests saved successfully");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error saving user interests: $e");
      }
      rethrow;
    }
  }

  @override
  Future<List<SelectedInterest>> getUserInterests(String userId) async {
    try {
      if (kDebugMode) {
        print(
            "=== InterestsRepositoryImpl.getUserInterests called for user $userId ===");
      }

      final doc = await _firebaseService.getDocument('users', userId);

      if (!doc.exists) {
        if (kDebugMode) {
          print("User document does not exist");
        }
        return [];
      }

      final data = doc.data() as Map<String, dynamic>;
      final selectedInterestsData =
          data['selected_interests'] as List<dynamic>?;

      if (selectedInterestsData == null) {
        if (kDebugMode) {
          print("No selected interests found for user");
        }
        return [];
      }

      final selectedInterests = selectedInterestsData
          .map(
              (item) => SelectedInterest.fromJson(item as Map<String, dynamic>))
          .toList();

      if (kDebugMode) {
        print("Loaded ${selectedInterests.length} selected interests for user");
      }

      return selectedInterests;
    } catch (e) {
      if (kDebugMode) {
        print("Error getting user interests: $e");
      }
      rethrow;
    }
  }
}
