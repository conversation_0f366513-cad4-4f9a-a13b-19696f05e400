/// Interests Display Model
///
/// Represents the merged interests data for display purposes
/// Combines catalog data with localized overrides and visibility settings
///
/// This model is used in the presentation layer for showing interests to users
library interests_display_model;

/// Interests Display Model Class
///
/// Contains the final merged data ready for display in the UI
/// Combines catalog structure with localized titles and visibility
class InterestsDisplayModel {
  /// List of visible categories sorted by order number
  final List<CategoryDisplayModel> categories;

  /// Creates an InterestsDisplayModel instance
  ///
  /// @param categories List of visible categories for display
  const InterestsDisplayModel({
    required this.categories,
  });

  /// Creates an empty InterestsDisplayModel
  ///
  /// @return Empty InterestsDisplayModel with no categories
  factory InterestsDisplayModel.empty() {
    return const InterestsDisplayModel(categories: []);
  }

  /// Check if the model has any categories
  ///
  /// @return True if there are categories to display
  bool get hasCategories => categories.isNotEmpty;

  /// Get total number of subcategories across all categories
  ///
  /// @return Total count of all subcategories
  int get totalSubCategories {
    return categories.fold(0, (sum, category) => sum + category.subCategories.length);
  }
}

/// Category Display Model Class
///
/// Represents a single category ready for display with merged data
class CategoryDisplayModel {
  /// Category ID (e.g., "playing_sport")
  final String id;

  /// Display title (localized if available, otherwise from catalog)
  final String title;

  /// Order number for sorting categories
  final int orderNumber;

  /// List of visible subcategories sorted by order number
  final List<SubCategoryDisplayModel> subCategories;

  /// Whether this category is currently expanded in the UI
  final bool isExpanded;

  /// Creates a CategoryDisplayModel instance
  ///
  /// @param id The category ID
  /// @param title The display title
  /// @param orderNumber The order number for sorting
  /// @param subCategories List of visible subcategories
  /// @param isExpanded Whether the category is expanded
  const CategoryDisplayModel({
    required this.id,
    required this.title,
    required this.orderNumber,
    required this.subCategories,
    this.isExpanded = false,
  });

  /// Creates a copy of this category with updated values
  ///
  /// @param id New category ID (optional)
  /// @param title New title (optional)
  /// @param orderNumber New order number (optional)
  /// @param subCategories New subcategories list (optional)
  /// @param isExpanded New expanded state (optional)
  /// @return New CategoryDisplayModel instance with updated values
  CategoryDisplayModel copyWith({
    String? id,
    String? title,
    int? orderNumber,
    List<SubCategoryDisplayModel>? subCategories,
    bool? isExpanded,
  }) {
    return CategoryDisplayModel(
      id: id ?? this.id,
      title: title ?? this.title,
      orderNumber: orderNumber ?? this.orderNumber,
      subCategories: subCategories ?? this.subCategories,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }

  /// Check if this category has any subcategories
  ///
  /// @return True if there are subcategories to display
  bool get hasSubCategories => subCategories.isNotEmpty;

  /// Get the number of subcategories in this category
  ///
  /// @return Count of subcategories
  int get subCategoryCount => subCategories.length;
}

/// SubCategory Display Model Class
///
/// Represents a single subcategory ready for display with merged data
class SubCategoryDisplayModel {
  /// Subcategory ID (e.g., "football")
  final String id;

  /// Display title (localized if available, otherwise from catalog)
  final String title;

  /// Order number for sorting subcategories
  final int orderNumber;

  /// Whether this subcategory is currently selected by the user
  final bool isSelected;

  /// Creates a SubCategoryDisplayModel instance
  ///
  /// @param id The subcategory ID
  /// @param title The display title
  /// @param orderNumber The order number for sorting
  /// @param isSelected Whether this subcategory is selected
  const SubCategoryDisplayModel({
    required this.id,
    required this.title,
    required this.orderNumber,
    this.isSelected = false,
  });

  /// Creates a copy of this subcategory with updated values
  ///
  /// @param id New subcategory ID (optional)
  /// @param title New title (optional)
  /// @param orderNumber New order number (optional)
  /// @param isSelected New selection state (optional)
  /// @return New SubCategoryDisplayModel instance with updated values
  SubCategoryDisplayModel copyWith({
    String? id,
    String? title,
    int? orderNumber,
    bool? isSelected,
  }) {
    return SubCategoryDisplayModel(
      id: id ?? this.id,
      title: title ?? this.title,
      orderNumber: orderNumber ?? this.orderNumber,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// Check if two SubCategoryDisplayModel instances are equal
  ///
  /// @param other The other SubCategoryDisplayModel to compare with
  /// @return True if both instances have the same ID
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubCategoryDisplayModel && other.id == id;
  }

  /// Generate hash code for this instance
  ///
  /// @return Hash code based on the subcategory ID
  @override
  int get hashCode => id.hashCode;

  /// String representation of this SubCategoryDisplayModel
  ///
  /// @return String describing this subcategory
  @override
  String toString() {
    return 'SubCategoryDisplayModel(id: $id, title: $title, isSelected: $isSelected)';
  }
}
