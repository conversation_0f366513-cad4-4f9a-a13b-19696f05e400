/// Interests Localized Model
///
/// Represents the localized interests structure from Firestore
/// Used for the interests/{COUNTRY_LANG} documents (e.g., SA_ar, US_en)
///
/// This model defines country/language-specific overrides and visibility
library interests_localized_model;

/// Interests Localized Model Class
///
/// Contains localized titles and visibility settings for categories and subcategories
/// Represents the interests/{COUNTRY_LANG} document structure
class InterestsLocalizedModel {
  /// Map of categories with localized data and visibility settings
  /// Key: category ID (e.g., "playing_sport")
  /// Value: CategoryLocalizedModel containing localized category details
  final Map<String, CategoryLocalizedModel> categories;

  /// Creates an InterestsLocalizedModel instance
  ///
  /// @param categories Map of category ID to CategoryLocalizedModel
  const InterestsLocalizedModel({
    required this.categories,
  });

  /// Factory constructor to create an InterestsLocalizedModel from JSON
  ///
  /// Parses a JSON map from the interests/{COUNTRY_LANG} document
  ///
  /// @param json Map containing localized data from Firestore
  /// @return InterestsLocalizedModel instance with data from the JSON map
  factory InterestsLocalizedModel.fromJson(Map<String, dynamic> json) {
    final categoriesMap = <String, CategoryLocalizedModel>{};

    if (json['categories'] != null) {
      final categoriesData = json['categories'] as Map<String, dynamic>;
      categoriesData.forEach((categoryId, categoryData) {
        categoriesMap[categoryId] = CategoryLocalizedModel.fromJson(
          categoryId,
          categoryData as Map<String, dynamic>,
        );
      });
    }

    return InterestsLocalizedModel(
      categories: categoriesMap,
    );
  }

  /// Converts the InterestsLocalizedModel to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing the localized data
  Map<String, dynamic> toJson() {
    final categoriesMap = <String, dynamic>{};
    categories.forEach((categoryId, categoryModel) {
      categoriesMap[categoryId] = categoryModel.toJson();
    });

    return {
      'categories': categoriesMap,
    };
  }
}

/// Category Localized Model Class
///
/// Represents a single category's localized data and visibility settings
class CategoryLocalizedModel {
  /// Category ID (e.g., "playing_sport")
  final String id;

  /// Localized category title (optional override)
  final String? title;

  /// Whether this category is visible in this locale
  final bool visible;

  /// Map of subcategories with localized data and visibility settings
  /// Key: subcategory ID (e.g., "football")
  /// Value: SubCategoryLocalizedModel containing localized subcategory details
  final Map<String, SubCategoryLocalizedModel> subCategories;

  /// Creates a CategoryLocalizedModel instance
  ///
  /// @param id The category ID
  /// @param title The localized category title (optional)
  /// @param visible Whether this category is visible
  /// @param subCategories Map of subcategory ID to SubCategoryLocalizedModel
  const CategoryLocalizedModel({
    required this.id,
    this.title,
    required this.visible,
    required this.subCategories,
  });

  /// Factory constructor to create a CategoryLocalizedModel from JSON
  ///
  /// @param categoryId The category ID
  /// @param json Map containing localized category data from Firestore
  /// @return CategoryLocalizedModel instance with data from the JSON map
  factory CategoryLocalizedModel.fromJson(
    String categoryId,
    Map<String, dynamic> json,
  ) {
    final subCategoriesMap = <String, SubCategoryLocalizedModel>{};

    if (json['sub_categories'] != null) {
      final subCategoriesData = json['sub_categories'] as Map<String, dynamic>;
      subCategoriesData.forEach((subCategoryId, subCategoryData) {
        subCategoriesMap[subCategoryId] = SubCategoryLocalizedModel.fromJson(
          subCategoryId,
          subCategoryData as Map<String, dynamic>,
        );
      });
    }

    return CategoryLocalizedModel(
      id: categoryId,
      title: json['title'],
      visible: json['visible'] ?? true, // Default to visible if not specified
      subCategories: subCategoriesMap,
    );
  }

  /// Converts the CategoryLocalizedModel to a JSON map
  ///
  /// @return Map containing the localized category data
  Map<String, dynamic> toJson() {
    final subCategoriesMap = <String, dynamic>{};
    subCategories.forEach((subCategoryId, subCategoryModel) {
      subCategoriesMap[subCategoryId] = subCategoryModel.toJson();
    });

    final result = <String, dynamic>{
      'visible': visible,
      'sub_categories': subCategoriesMap,
    };

    // Only include title if it's not null (optional override)
    if (title != null) {
      result['title'] = title;
    }

    return result;
  }
}

/// SubCategory Localized Model Class
///
/// Represents a single subcategory's localized data and visibility settings
class SubCategoryLocalizedModel {
  /// Subcategory ID (e.g., "football")
  final String id;

  /// Localized subcategory title (optional override)
  final String? title;

  /// Whether this subcategory is visible in this locale
  final bool visible;

  /// Creates a SubCategoryLocalizedModel instance
  ///
  /// @param id The subcategory ID
  /// @param title The localized subcategory title (optional)
  /// @param visible Whether this subcategory is visible
  const SubCategoryLocalizedModel({
    required this.id,
    this.title,
    required this.visible,
  });

  /// Factory constructor to create a SubCategoryLocalizedModel from JSON
  ///
  /// @param subCategoryId The subcategory ID
  /// @param json Map containing localized subcategory data from Firestore
  /// @return SubCategoryLocalizedModel instance with data from the JSON map
  factory SubCategoryLocalizedModel.fromJson(
    String subCategoryId,
    Map<String, dynamic> json,
  ) {
    return SubCategoryLocalizedModel(
      id: subCategoryId,
      title: json['title'],
      visible: json['visible'] ?? true, // Default to visible if not specified
    );
  }

  /// Converts the SubCategoryLocalizedModel to a JSON map
  ///
  /// @return Map containing the localized subcategory data
  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{
      'visible': visible,
    };

    // Only include title if it's not null (optional override)
    if (title != null) {
      result['title'] = title;
    }

    return result;
  }
}
