/// Interests Catalog Model
///
/// Represents the interests catalog structure from Firestore
/// Used for the interests/catalog document
///
/// This model defines the master catalog of all available interests
library interests_catalog_model;

/// Interests Catalog Model Class
///
/// Contains the default language and all categories with their subcategories
/// Represents the interests/catalog document structure
class InterestsCatalogModel {
  /// Default language for the catalog (e.g., "ar", "en")
  final String defaultLanguage;

  /// Map of all categories with their subcategories
  /// Key: category ID (e.g., "playing_sport")
  /// Value: CategoryCatalogModel containing category details
  final Map<String, CategoryCatalogModel> categories;

  /// Creates an InterestsCatalogModel instance
  ///
  /// @param defaultLanguage The default language code
  /// @param categories Map of category ID to CategoryCatalogModel
  const InterestsCatalogModel({
    required this.defaultLanguage,
    required this.categories,
  });

  /// Factory constructor to create an InterestsCatalogModel from JSON
  ///
  /// Parses a JSON map from the interests/catalog document
  ///
  /// @param json Map containing catalog data from Firestore
  /// @return InterestsCatalogModel instance with data from the JSON map
  factory InterestsCatalogModel.fromJson(Map<String, dynamic> json) {
    final categoriesMap = <String, CategoryCatalogModel>{};
    
    if (json['categories'] != null) {
      final categoriesData = json['categories'] as Map<String, dynamic>;
      categoriesData.forEach((categoryId, categoryData) {
        categoriesMap[categoryId] = CategoryCatalogModel.fromJson(
          categoryId,
          categoryData as Map<String, dynamic>,
        );
      });
    }

    return InterestsCatalogModel(
      defaultLanguage: json['default_language'] ?? 'ar',
      categories: categoriesMap,
    );
  }

  /// Converts the InterestsCatalogModel to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing the catalog data
  Map<String, dynamic> toJson() {
    final categoriesMap = <String, dynamic>{};
    categories.forEach((categoryId, categoryModel) {
      categoriesMap[categoryId] = categoryModel.toJson();
    });

    return {
      'default_language': defaultLanguage,
      'categories': categoriesMap,
    };
  }
}

/// Category Catalog Model Class
///
/// Represents a single category in the catalog with its subcategories
class CategoryCatalogModel {
  /// Category ID (e.g., "playing_sport")
  final String id;

  /// Category title in the default language
  final String title;

  /// Order number for sorting categories
  final int orderNumber;

  /// Map of subcategories within this category
  /// Key: subcategory ID (e.g., "football")
  /// Value: SubCategoryCatalogModel containing subcategory details
  final Map<String, SubCategoryCatalogModel> subCategories;

  /// Creates a CategoryCatalogModel instance
  ///
  /// @param id The category ID
  /// @param title The category title
  /// @param orderNumber The order number for sorting
  /// @param subCategories Map of subcategory ID to SubCategoryCatalogModel
  const CategoryCatalogModel({
    required this.id,
    required this.title,
    required this.orderNumber,
    required this.subCategories,
  });

  /// Factory constructor to create a CategoryCatalogModel from JSON
  ///
  /// @param categoryId The category ID
  /// @param json Map containing category data from Firestore
  /// @return CategoryCatalogModel instance with data from the JSON map
  factory CategoryCatalogModel.fromJson(
    String categoryId,
    Map<String, dynamic> json,
  ) {
    final subCategoriesMap = <String, SubCategoryCatalogModel>{};
    
    if (json['sub_categories'] != null) {
      final subCategoriesData = json['sub_categories'] as Map<String, dynamic>;
      subCategoriesData.forEach((subCategoryId, subCategoryData) {
        subCategoriesMap[subCategoryId] = SubCategoryCatalogModel.fromJson(
          subCategoryId,
          subCategoryData as Map<String, dynamic>,
        );
      });
    }

    return CategoryCatalogModel(
      id: categoryId,
      title: json['title'] ?? '',
      orderNumber: json['order_number'] ?? 0,
      subCategories: subCategoriesMap,
    );
  }

  /// Converts the CategoryCatalogModel to a JSON map
  ///
  /// @return Map containing the category data
  Map<String, dynamic> toJson() {
    final subCategoriesMap = <String, dynamic>{};
    subCategories.forEach((subCategoryId, subCategoryModel) {
      subCategoriesMap[subCategoryId] = subCategoryModel.toJson();
    });

    return {
      'title': title,
      'order_number': orderNumber,
      'sub_categories': subCategoriesMap,
    };
  }
}

/// SubCategory Catalog Model Class
///
/// Represents a single subcategory in the catalog
class SubCategoryCatalogModel {
  /// Subcategory ID (e.g., "football")
  final String id;

  /// Subcategory title in the default language
  final String title;

  /// Order number for sorting subcategories
  final int orderNumber;

  /// Creates a SubCategoryCatalogModel instance
  ///
  /// @param id The subcategory ID
  /// @param title The subcategory title
  /// @param orderNumber The order number for sorting
  const SubCategoryCatalogModel({
    required this.id,
    required this.title,
    required this.orderNumber,
  });

  /// Factory constructor to create a SubCategoryCatalogModel from JSON
  ///
  /// @param subCategoryId The subcategory ID
  /// @param json Map containing subcategory data from Firestore
  /// @return SubCategoryCatalogModel instance with data from the JSON map
  factory SubCategoryCatalogModel.fromJson(
    String subCategoryId,
    Map<String, dynamic> json,
  ) {
    return SubCategoryCatalogModel(
      id: subCategoryId,
      title: json['title'] ?? '',
      orderNumber: json['order_number'] ?? 0,
    );
  }

  /// Converts the SubCategoryCatalogModel to a JSON map
  ///
  /// @return Map containing the subcategory data
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'order_number': orderNumber,
    };
  }
}
