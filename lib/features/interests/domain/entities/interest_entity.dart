/// Interest Domain Entity
///
/// Pure business object representing interest categories and subcategories
/// Contains no external dependencies and represents core business logic
///
/// This entity defines the structure for interest data in the domain layer
library interest_entity;

/// Interest Category Entity
///
/// Represents a category of interests with its subcategories
/// Used in the domain layer for business logic operations
class InterestEntity {
  /// Unique identifier for the category
  final String id;

  /// Category name/title (e.g., "Sports", "Music", "Food")
  final String title;

  /// Order number for sorting categories
  final int orderNumber;

  /// List of subcategories within this interest category
  final List<SubCategoryEntity> subCategories;

  /// Whether this category is currently shown/expanded
  final bool isExpanded;

  /// Creates an InterestEntity
  ///
  /// @param id Unique identifier for the category
  /// @param title Category name/title
  /// @param orderNumber Order number for sorting
  /// @param subCategories List of subcategories within this category
  /// @param isExpanded Whether this category is shown/expanded
  const InterestEntity({
    required this.id,
    required this.title,
    required this.orderNumber,
    required this.subCategories,
    required this.isExpanded,
  });

  /// Creates a copy of this entity with updated values
  ///
  /// @param id New identifier (optional)
  /// @param title New title (optional)
  /// @param orderNumber New order number (optional)
  /// @param subCategories New subcategories list (optional)
  /// @param isExpanded New expanded state (optional)
  /// @return New InterestEntity instance with updated values
  InterestEntity copyWith({
    String? id,
    String? title,
    int? orderNumber,
    List<SubCategoryEntity>? subCategories,
    bool? isExpanded,
  }) {
    return InterestEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      orderNumber: orderNumber ?? this.orderNumber,
      subCategories: subCategories ?? this.subCategories,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }

  /// Checks if this category has any subcategories
  ///
  /// @return True if subcategories exist, false otherwise
  bool get hasSubCategories => subCategories.isNotEmpty;

  /// Gets the count of subcategories
  ///
  /// @return Number of subcategories in this category
  int get subCategoryCount => subCategories.length;

  /// Gets selected subcategories
  ///
  /// @return List of selected subcategory entities
  List<SubCategoryEntity> get selectedSubCategories =>
      subCategories.where((sub) => sub.isSelected).toList();

  /// Checks if any subcategories are selected
  ///
  /// @return True if at least one subcategory is selected
  bool get hasSelectedSubCategories => selectedSubCategories.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InterestEntity &&
        other.id == id &&
        other.title == title &&
        other.orderNumber == orderNumber &&
        other.isExpanded == isExpanded;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        orderNumber.hashCode ^
        isExpanded.hashCode;
  }

  @override
  String toString() {
    return 'InterestEntity(id: $id, title: $title, '
        'orderNumber: $orderNumber, isExpanded: $isExpanded, '
        'subCategories: ${subCategories.length})';
  }
}

/// Interest Subcategory Entity
///
/// Represents a specific interest within a category
/// Contains selection state for user interaction
class SubCategoryEntity {
  /// Unique identifier for the subcategory
  final String id;

  /// Name/title of the subcategory (e.g., "Football", "Jazz", "Italian Food")
  final String title;

  /// Order number for sorting subcategories
  final int orderNumber;

  /// Whether this subcategory is selected by the user
  final bool isSelected;

  /// Creates a SubCategoryEntity
  ///
  /// @param id Unique identifier for the subcategory
  /// @param title Name/title of the subcategory
  /// @param orderNumber Order number for sorting
  /// @param isSelected Whether this subcategory is selected
  const SubCategoryEntity({
    required this.id,
    required this.title,
    required this.orderNumber,
    required this.isSelected,
  });

  /// Creates a copy of this entity with updated values
  ///
  /// @param id New subcategory ID (optional)
  /// @param title New subcategory title (optional)
  /// @param orderNumber New order number (optional)
  /// @param isSelected New selection state (optional)
  /// @return New SubCategoryEntity instance with updated values
  SubCategoryEntity copyWith({
    String? id,
    String? title,
    int? orderNumber,
    bool? isSelected,
  }) {
    return SubCategoryEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      orderNumber: orderNumber ?? this.orderNumber,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// Toggles the selection state
  ///
  /// @return New SubCategoryEntity with opposite selection state
  SubCategoryEntity toggleSelection() {
    return copyWith(isSelected: !isSelected);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubCategoryEntity &&
        other.id == id &&
        other.title == title &&
        other.orderNumber == orderNumber &&
        other.isSelected == isSelected;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        orderNumber.hashCode ^
        isSelected.hashCode;
  }

  @override
  String toString() {
    return 'SubCategoryEntity(id: $id, title: $title, orderNumber: $orderNumber, isSelected: $isSelected)';
  }
}
