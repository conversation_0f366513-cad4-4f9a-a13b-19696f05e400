/// Interests Repository Interface
///
/// Defines the contract for interests data operations
/// Provides methods for fetching and managing interest categories and subcategories
library interests_repository;

import 'package:towasl/features/interests/data/models/interests_display_model.dart';
import 'package:towasl/shared/models/selected_interest_model.dart';

/// Abstract repository for interests data operations
///
/// Defines the interface for fetching interest categories and subcategories
/// Implementations should handle data source specifics (Firestore, API, etc.)
abstract class InterestsRepository {
  /// Get merged interests ready for display
  ///
  /// Combines catalog data with localized overrides and filters by visibility
  /// Returns data ready for presentation layer
  ///
  /// @param countryLang The country and language code for localization
  /// @return A Future that resolves to InterestsDisplayModel
  Future<InterestsDisplayModel> getDisplayInterests(String countryLang);

  /// Save user's selected interests
  ///
  /// Updates the user's selected_interests array in Firestore
  ///
  /// @param userId The user ID
  /// @param selectedInterests List of selected interests
  /// @return A Future that completes when the operation is done
  Future<void> saveUserInterests(
      String userId, List<SelectedInterest> selectedInterests);

  /// Get user's selected interests
  ///
  /// Fetches the user's selected_interests array from Firestore
  ///
  /// @param userId The user ID
  /// @return A Future that resolves to list of selected interests
  Future<List<SelectedInterest>> getUserInterests(String userId);
}
