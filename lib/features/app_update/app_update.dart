/// App Update Feature
///
/// Barrel file that exports all app update functionality
/// Provides a single import point for the entire app update feature
library app_update;

// Domain Layer Exports
export 'domain/entities/app_version_control_entity.dart';
export 'domain/repositories/app_update_repository.dart';
export 'domain/usecases/check_app_update_usecase.dart';

// Data Layer Exports
export 'data/models/app_version_control_model.dart';
export 'data/datasources/app_update_remote_datasource.dart';
export 'data/repositories/app_update_repository_impl.dart';

// Presentation Layer Exports
export 'presentation/views/app_update_view.dart';
export 'presentation/viewmodels/app_update_viewmodel.dart';
export 'presentation/widgets/update_dialog_widget.dart';
