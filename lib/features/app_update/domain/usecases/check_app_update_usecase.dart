/// Check App Update Use Case
///
/// Business logic for checking app updates
/// Handles version comparison and update status determination
library check_app_update_usecase;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';
import 'package:towasl/features/app_update/domain/repositories/app_update_repository.dart';
import 'package:towasl/shared/utils/version_comparator.dart';

/// Check App Update Use Case
///
/// Encapsulates the business logic for checking app updates
/// Determines update status based on version comparison
class CheckAppUpdateUseCase {
  /// App update repository for data operations
  final AppUpdateRepository _repository;

  /// Creates a CheckAppUpdateUseCase
  ///
  /// @param repository App update repository instance
  const CheckAppUpdateUseCase(this._repository);

  /// Execute the use case to check for app updates
  ///
  /// Retrieves version control data and compares with current version
  /// to determine if updates are available or required
  ///
  /// @param currentVersion Current app version string
  /// @return A Future that resolves to UpdateCheckResultEntity
  /// @throws Exception if unable to check for updates
  Future<UpdateCheckResultEntity> execute(String currentVersion) async {
    try {
      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Checking updates for version $currentVersion');
      }

      // Get version control data from repository
      final versionControl = await _repository.getVersionControl();

      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Retrieved version control data: $versionControl');
      }

      // Determine update status based on version comparison
      final updateStatus = _determineUpdateStatus(currentVersion, versionControl);

      final result = UpdateCheckResultEntity(
        status: updateStatus,
        versionControl: versionControl,
        currentVersion: currentVersion,
      );

      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Update check result: $result');
      }

      // Cache the version control data for future use
      await _repository.cacheVersionControl(versionControl);

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Error checking for updates - $e');
      }

      // Try to use cached data as fallback
      final cachedVersionControl = await _repository.getCachedVersionControl();
      if (cachedVersionControl != null) {
        if (kDebugMode) {
          print('CheckAppUpdateUseCase: Using cached version control data');
        }

        final updateStatus = _determineUpdateStatus(currentVersion, cachedVersionControl);
        return UpdateCheckResultEntity(
          status: updateStatus,
          versionControl: cachedVersionControl,
          currentVersion: currentVersion,
        );
      }

      // If no cached data available, rethrow the exception
      rethrow;
    }
  }

  /// Determine update status based on version comparison
  ///
  /// Compares current version with latest and minimum required versions
  /// to determine the appropriate update status
  ///
  /// @param currentVersion Current app version
  /// @param versionControl Version control data
  /// @return UpdateStatus enum value
  UpdateStatus _determineUpdateStatus(
    String currentVersion,
    AppVersionControlEntity versionControl,
  ) {
    try {
      // Get platform-specific versions
      final latestVersion = versionControl.currentPlatformLatestVersion;
      final minRequiredVersion = versionControl.currentPlatformMinRequiredVersion;

      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Version comparison:');
        print('  Current: $currentVersion');
        print('  Latest: $latestVersion');
        print('  Min Required: $minRequiredVersion');
      }

      // Validate version formats
      if (!VersionComparator.isValidVersion(currentVersion) ||
          !VersionComparator.isValidVersion(latestVersion) ||
          !VersionComparator.isValidVersion(minRequiredVersion)) {
        if (kDebugMode) {
          print('CheckAppUpdateUseCase: Invalid version format detected');
        }
        return UpdateStatus.noUpdate;
      }

      // Check if current version is below minimum required (mandatory update)
      if (VersionComparator.isLessThan(currentVersion, minRequiredVersion)) {
        if (kDebugMode) {
          print('CheckAppUpdateUseCase: Mandatory update required');
        }
        return UpdateStatus.mandatoryUpdate;
      }

      // Check if current version is below latest (optional update)
      if (VersionComparator.isLessThan(currentVersion, latestVersion)) {
        if (kDebugMode) {
          print('CheckAppUpdateUseCase: Optional update available');
        }
        return UpdateStatus.optionalUpdate;
      }

      // Current version is up to date
      if (kDebugMode) {
        print('CheckAppUpdateUseCase: No update needed');
      }
      return UpdateStatus.noUpdate;
    } catch (e) {
      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Error determining update status - $e');
      }
      // On error, assume no update to avoid blocking the user
      return UpdateStatus.noUpdate;
    }
  }

  /// Check for updates with cached fallback
  ///
  /// Attempts to check for updates using remote data first,
  /// falls back to cached data if remote check fails
  ///
  /// @param currentVersion Current app version string
  /// @return A Future that resolves to UpdateCheckResultEntity or null
  Future<UpdateCheckResultEntity?> executeWithFallback(String currentVersion) async {
    try {
      return await execute(currentVersion);
    } catch (e) {
      if (kDebugMode) {
        print('CheckAppUpdateUseCase: Remote check failed, trying cached data - $e');
      }

      try {
        final cachedVersionControl = await _repository.getCachedVersionControl();
        if (cachedVersionControl != null) {
          final updateStatus = _determineUpdateStatus(currentVersion, cachedVersionControl);
          return UpdateCheckResultEntity(
            status: updateStatus,
            versionControl: cachedVersionControl,
            currentVersion: currentVersion,
          );
        }
      } catch (cacheError) {
        if (kDebugMode) {
          print('CheckAppUpdateUseCase: Cache fallback also failed - $cacheError');
        }
      }

      // Return null if both remote and cache fail
      return null;
    }
  }
}
