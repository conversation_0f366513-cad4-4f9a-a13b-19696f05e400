/// App Version Control Entity
///
/// Domain entity representing version control information from Firestore
/// Contains version information for both Android and iOS platforms
library app_version_control_entity;

import 'dart:io';

/// App Version Control Entity
///
/// Represents version control information retrieved from Firestore
/// Contains latest versions, minimum required versions, and store links
class AppVersionControlEntity {
  /// Latest available version for Android
  final String latestVersionAndroid;

  /// Latest available version for iOS
  final String latestVersionIos;

  /// Minimum required version for Android
  final String minRequiredVersionAndroid;

  /// Minimum required version for iOS
  final String minRequiredVersionIos;

  /// Google Play Store link for Android updates
  final String androidLink;

  /// App Store link for iOS updates
  final String iosLink;

  /// Creates an AppVersionControlEntity
  ///
  /// @param latestVersionAndroid Latest Android version
  /// @param latestVersionIos Latest iOS version
  /// @param minRequiredVersionAndroid Minimum required Android version
  /// @param minRequiredVersionIos Minimum required iOS version
  /// @param androidLink Google Play Store link
  /// @param iosLink App Store link
  const AppVersionControlEntity({
    required this.latestVersionAndroid,
    required this.latestVersionIos,
    required this.minRequiredVersionAndroid,
    required this.minRequiredVersionIos,
    required this.androidLink,
    required this.iosLink,
  });

  /// Get the latest version for the current platform
  ///
  /// @return Latest version string for current platform
  String get currentPlatformLatestVersion {
    return Platform.isAndroid ? latestVersionAndroid : latestVersionIos;
  }

  /// Get the minimum required version for the current platform
  ///
  /// @return Minimum required version string for current platform
  String get currentPlatformMinRequiredVersion {
    return Platform.isAndroid ? minRequiredVersionAndroid : minRequiredVersionIos;
  }

  /// Get the store link for the current platform
  ///
  /// @return Store link for current platform
  String get currentPlatformStoreLink {
    return Platform.isAndroid ? androidLink : iosLink;
  }

  /// Create a copy with updated values
  ///
  /// @param latestVersionAndroid Updated Android latest version
  /// @param latestVersionIos Updated iOS latest version
  /// @param minRequiredVersionAndroid Updated Android minimum version
  /// @param minRequiredVersionIos Updated iOS minimum version
  /// @param androidLink Updated Android store link
  /// @param iosLink Updated iOS store link
  /// @return New AppVersionControlEntity with updated values
  AppVersionControlEntity copyWith({
    String? latestVersionAndroid,
    String? latestVersionIos,
    String? minRequiredVersionAndroid,
    String? minRequiredVersionIos,
    String? androidLink,
    String? iosLink,
  }) {
    return AppVersionControlEntity(
      latestVersionAndroid: latestVersionAndroid ?? this.latestVersionAndroid,
      latestVersionIos: latestVersionIos ?? this.latestVersionIos,
      minRequiredVersionAndroid: minRequiredVersionAndroid ?? this.minRequiredVersionAndroid,
      minRequiredVersionIos: minRequiredVersionIos ?? this.minRequiredVersionIos,
      androidLink: androidLink ?? this.androidLink,
      iosLink: iosLink ?? this.iosLink,
    );
  }

  @override
  String toString() {
    return 'AppVersionControlEntity('
        'latestVersionAndroid: $latestVersionAndroid, '
        'latestVersionIos: $latestVersionIos, '
        'minRequiredVersionAndroid: $minRequiredVersionAndroid, '
        'minRequiredVersionIos: $minRequiredVersionIos, '
        'androidLink: $androidLink, '
        'iosLink: $iosLink)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AppVersionControlEntity &&
        other.latestVersionAndroid == latestVersionAndroid &&
        other.latestVersionIos == latestVersionIos &&
        other.minRequiredVersionAndroid == minRequiredVersionAndroid &&
        other.minRequiredVersionIos == minRequiredVersionIos &&
        other.androidLink == androidLink &&
        other.iosLink == iosLink;
  }

  @override
  int get hashCode {
    return latestVersionAndroid.hashCode ^
        latestVersionIos.hashCode ^
        minRequiredVersionAndroid.hashCode ^
        minRequiredVersionIos.hashCode ^
        androidLink.hashCode ^
        iosLink.hashCode;
  }
}

/// Update Status Enum
///
/// Represents the different update statuses based on version comparison
enum UpdateStatus {
  /// No update available - current version is up to date
  noUpdate,

  /// Optional update available - newer version exists but not required
  optionalUpdate,

  /// Mandatory update required - current version is below minimum required
  mandatoryUpdate,
}

/// Update Check Result Entity
///
/// Contains the result of checking for app updates
class UpdateCheckResultEntity {
  /// The update status
  final UpdateStatus status;

  /// Version control information
  final AppVersionControlEntity versionControl;

  /// Current app version
  final String currentVersion;

  /// Creates an UpdateCheckResultEntity
  ///
  /// @param status Update status
  /// @param versionControl Version control information
  /// @param currentVersion Current app version
  const UpdateCheckResultEntity({
    required this.status,
    required this.versionControl,
    required this.currentVersion,
  });

  /// Check if update is available
  ///
  /// @return True if any update is available
  bool get hasUpdate {
    return status != UpdateStatus.noUpdate;
  }

  /// Check if update is mandatory
  ///
  /// @return True if update is mandatory
  bool get isMandatory {
    return status == UpdateStatus.mandatoryUpdate;
  }

  /// Check if update is optional
  ///
  /// @return True if update is optional
  bool get isOptional {
    return status == UpdateStatus.optionalUpdate;
  }

  @override
  String toString() {
    return 'UpdateCheckResultEntity('
        'status: $status, '
        'currentVersion: $currentVersion, '
        'versionControl: $versionControl)';
  }
}
