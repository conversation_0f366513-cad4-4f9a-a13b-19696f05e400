/// App Update Repository Interface
///
/// Defines the contract for app update operations
/// Provides methods for checking version control and update status
library app_update_repository;

import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';

/// App Update Repository Interface
///
/// Defines the contract for app update operations
/// Allows for easy mocking in tests and different implementations
abstract class AppUpdateRepository {
  /// Get version control information from remote source
  ///
  /// Retrieves the latest version control data from Firestore
  /// containing version information and store links
  ///
  /// @return A Future that resolves to AppVersionControlEntity
  /// @throws Exception if unable to fetch version control data
  Future<AppVersionControlEntity> getVersionControl();

  /// Check for app updates
  ///
  /// Compares current app version with remote version control data
  /// to determine if updates are available or required
  ///
  /// @param currentVersion Current app version string
  /// @return A Future that resolves to UpdateCheckResultEntity
  /// @throws Exception if unable to check for updates
  Future<UpdateCheckResultEntity> checkForUpdates(String currentVersion);

  /// Cache version control data locally
  ///
  /// Stores version control information locally for offline access
  /// and faster subsequent checks
  ///
  /// @param versionControl Version control data to cache
  /// @return A Future that completes when caching is done
  Future<void> cacheVersionControl(AppVersionControlEntity versionControl);

  /// Get cached version control data
  ///
  /// Retrieves previously cached version control information
  /// for offline functionality
  ///
  /// @return A Future that resolves to cached AppVersionControlEntity or null
  Future<AppVersionControlEntity?> getCachedVersionControl();

  /// Clear cached version control data
  ///
  /// Removes locally cached version control information
  ///
  /// @return A Future that completes when cache is cleared
  Future<void> clearVersionControlCache();
}
