/// App Update Repository Implementation
///
/// Concrete implementation of the app update repository
/// Handles data operations for app update functionality
library app_update_repository_impl;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';
import 'package:towasl/features/app_update/domain/repositories/app_update_repository.dart';
import 'package:towasl/features/app_update/data/datasources/app_update_remote_datasource.dart';
import 'package:towasl/features/app_update/data/models/app_version_control_model.dart';
import 'package:towasl/shared/services/storage_service.dart';
import 'package:towasl/shared/utils/version_comparator.dart';

/// App Update Repository Implementation
///
/// Implements the AppUpdateRepository interface
/// Coordinates between remote data source and local storage
class AppUpdateRepositoryImpl implements AppUpdateRepository {
  /// Remote data source for Firestore operations
  final AppUpdateRemoteDataSource _remoteDataSource;

  /// Storage service for local caching
  final StorageService _storageService;

  /// Storage key for cached version control data
  static const String _versionControlCacheKey = 'version_control_cache';

  /// Storage key for cache timestamp
  static const String _cacheTimestampKey = 'version_control_cache_timestamp';

  /// Cache validity duration (24 hours)
  static const Duration _cacheValidityDuration = Duration(hours: 24);

  /// Creates an AppUpdateRepositoryImpl
  ///
  /// @param remoteDataSource Remote data source for Firestore operations
  /// @param storageService Storage service for local caching
  const AppUpdateRepositoryImpl(
    this._remoteDataSource,
    this._storageService,
  );

  @override
  Future<AppVersionControlEntity> getVersionControl() async {
    try {
      if (kDebugMode) {
        print('AppUpdateRepository: Getting version control data');
      }

      // Try to get fresh data from remote source
      final versionControlModel = await _remoteDataSource.getVersionControl();
      final entity = versionControlModel.toEntity();

      if (kDebugMode) {
        print('AppUpdateRepository: Successfully retrieved version control: $entity');
      }

      return entity;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error getting version control - $e');
      }
      rethrow;
    }
  }

  @override
  Future<UpdateCheckResultEntity> checkForUpdates(String currentVersion) async {
    try {
      if (kDebugMode) {
        print('AppUpdateRepository: Checking for updates with current version: $currentVersion');
      }

      // Validate current version format
      if (!VersionComparator.isValidVersion(currentVersion)) {
        throw Exception('Invalid current version format: $currentVersion');
      }

      // Get version control data
      final versionControl = await getVersionControl();

      // Determine update status
      final updateStatus = _determineUpdateStatus(currentVersion, versionControl);

      final result = UpdateCheckResultEntity(
        status: updateStatus,
        versionControl: versionControl,
        currentVersion: currentVersion,
      );

      if (kDebugMode) {
        print('AppUpdateRepository: Update check completed: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error checking for updates - $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> cacheVersionControl(AppVersionControlEntity versionControl) async {
    try {
      if (kDebugMode) {
        print('AppUpdateRepository: Caching version control data');
      }

      // Convert entity to model for serialization
      final model = AppVersionControlModel.fromEntity(versionControl);

      // Store the data and timestamp
      await _storageService.write(_versionControlCacheKey, model.toJson());
      await _storageService.write(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);

      if (kDebugMode) {
        print('AppUpdateRepository: Version control data cached successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error caching version control - $e');
      }
      // Don't rethrow caching errors as they're not critical
    }
  }

  @override
  Future<AppVersionControlEntity?> getCachedVersionControl() async {
    try {
      if (kDebugMode) {
        print('AppUpdateRepository: Getting cached version control data');
      }

      // Check if cache exists and is valid
      if (!await _isCacheValid()) {
        if (kDebugMode) {
          print('AppUpdateRepository: Cache is invalid or expired');
        }
        return null;
      }

      // Get cached data
      final cachedData = _storageService.read<Map<String, dynamic>>(_versionControlCacheKey);
      if (cachedData == null) {
        if (kDebugMode) {
          print('AppUpdateRepository: No cached data found');
        }
        return null;
      }

      // Convert cached data to entity
      final model = AppVersionControlModel.fromJson(cachedData);
      final entity = model.toEntity();

      if (kDebugMode) {
        print('AppUpdateRepository: Retrieved cached version control: $entity');
      }

      return entity;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error getting cached version control - $e');
      }
      return null;
    }
  }

  @override
  Future<void> clearVersionControlCache() async {
    try {
      if (kDebugMode) {
        print('AppUpdateRepository: Clearing version control cache');
      }

      await _storageService.write(_versionControlCacheKey, null);
      await _storageService.write(_cacheTimestampKey, null);

      if (kDebugMode) {
        print('AppUpdateRepository: Version control cache cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error clearing cache - $e');
      }
      // Don't rethrow cache clearing errors
    }
  }

  /// Check if cached data is valid
  ///
  /// Verifies that cached data exists and is within validity period
  ///
  /// @return True if cache is valid
  Future<bool> _isCacheValid() async {
    try {
      final timestamp = _storageService.read<int>(_cacheTimestampKey);
      if (timestamp == null) return false;

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime);

      return difference <= _cacheValidityDuration;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error checking cache validity - $e');
      }
      return false;
    }
  }

  /// Determine update status based on version comparison
  ///
  /// @param currentVersion Current app version
  /// @param versionControl Version control data
  /// @return UpdateStatus enum value
  UpdateStatus _determineUpdateStatus(
    String currentVersion,
    AppVersionControlEntity versionControl,
  ) {
    try {
      final latestVersion = versionControl.currentPlatformLatestVersion;
      final minRequiredVersion = versionControl.currentPlatformMinRequiredVersion;

      // Check if current version is below minimum required (mandatory update)
      if (VersionComparator.isLessThan(currentVersion, minRequiredVersion)) {
        return UpdateStatus.mandatoryUpdate;
      }

      // Check if current version is below latest (optional update)
      if (VersionComparator.isLessThan(currentVersion, latestVersion)) {
        return UpdateStatus.optionalUpdate;
      }

      // Current version is up to date
      return UpdateStatus.noUpdate;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRepository: Error determining update status - $e');
      }
      // On error, assume no update to avoid blocking the user
      return UpdateStatus.noUpdate;
    }
  }
}
