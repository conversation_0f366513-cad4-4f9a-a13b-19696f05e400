/// App Version Control Model
///
/// Data model for version control information from Firestore
/// Handles serialization and deserialization of version control data
library app_version_control_model;

import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';

/// App Version Control Model
///
/// Data model that extends the domain entity
/// Provides JSON serialization/deserialization for Firestore operations
class AppVersionControlModel extends AppVersionControlEntity {
  /// Creates an AppVersionControlModel
  ///
  /// @param latestVersionAndroid Latest Android version
  /// @param latestVersionIos Latest iOS version
  /// @param minRequiredVersionAndroid Minimum required Android version
  /// @param minRequiredVersionIos Minimum required iOS version
  /// @param androidLink Google Play Store link
  /// @param iosLink App Store link
  const AppVersionControlModel({
    required super.latestVersionAndroid,
    required super.latestVersionIos,
    required super.minRequiredVersionAndroid,
    required super.minRequiredVersionIos,
    required super.androidLink,
    required super.iosLink,
  });

  /// Factory constructor to create model from JSON
  ///
  /// Parses Firestore document data into AppVersionControlModel
  /// Handles null values and provides default fallbacks
  ///
  /// @param json Map containing version control data from Firestore
  /// @return AppVersionControlModel instance
  factory AppVersionControlModel.fromJson(Map<String, dynamic> json) {
    return AppVersionControlModel(
      latestVersionAndroid: json['latest_version_android'] as String? ?? '1.0.0',
      latestVersionIos: json['latest_version_ios'] as String? ?? '1.0.0',
      minRequiredVersionAndroid: json['min_required_version_android'] as String? ?? '1.0.0',
      minRequiredVersionIos: json['min_required_version_ios'] as String? ?? '1.0.0',
      androidLink: json['android_link'] as String? ?? '',
      iosLink: json['ios_link'] as String? ?? '',
    );
  }

  /// Convert model to JSON for Firestore storage
  ///
  /// Serializes the model data into a format suitable for Firestore
  ///
  /// @return Map containing serialized version control data
  Map<String, dynamic> toJson() {
    return {
      'latest_version_android': latestVersionAndroid,
      'latest_version_ios': latestVersionIos,
      'min_required_version_android': minRequiredVersionAndroid,
      'min_required_version_ios': minRequiredVersionIos,
      'android_link': androidLink,
      'ios_link': iosLink,
    };
  }

  /// Factory constructor to create model from entity
  ///
  /// Converts domain entity to data model
  ///
  /// @param entity AppVersionControlEntity to convert
  /// @return AppVersionControlModel instance
  factory AppVersionControlModel.fromEntity(AppVersionControlEntity entity) {
    return AppVersionControlModel(
      latestVersionAndroid: entity.latestVersionAndroid,
      latestVersionIos: entity.latestVersionIos,
      minRequiredVersionAndroid: entity.minRequiredVersionAndroid,
      minRequiredVersionIos: entity.minRequiredVersionIos,
      androidLink: entity.androidLink,
      iosLink: entity.iosLink,
    );
  }

  /// Convert model to entity
  ///
  /// Converts data model to domain entity
  ///
  /// @return AppVersionControlEntity instance
  AppVersionControlEntity toEntity() {
    return AppVersionControlEntity(
      latestVersionAndroid: latestVersionAndroid,
      latestVersionIos: latestVersionIos,
      minRequiredVersionAndroid: minRequiredVersionAndroid,
      minRequiredVersionIos: minRequiredVersionIos,
      androidLink: androidLink,
      iosLink: iosLink,
    );
  }

  /// Create a copy with updated values
  ///
  /// @param latestVersionAndroid Updated Android latest version
  /// @param latestVersionIos Updated iOS latest version
  /// @param minRequiredVersionAndroid Updated Android minimum version
  /// @param minRequiredVersionIos Updated iOS minimum version
  /// @param androidLink Updated Android store link
  /// @param iosLink Updated iOS store link
  /// @return New AppVersionControlModel with updated values
  @override
  AppVersionControlModel copyWith({
    String? latestVersionAndroid,
    String? latestVersionIos,
    String? minRequiredVersionAndroid,
    String? minRequiredVersionIos,
    String? androidLink,
    String? iosLink,
  }) {
    return AppVersionControlModel(
      latestVersionAndroid: latestVersionAndroid ?? this.latestVersionAndroid,
      latestVersionIos: latestVersionIos ?? this.latestVersionIos,
      minRequiredVersionAndroid: minRequiredVersionAndroid ?? this.minRequiredVersionAndroid,
      minRequiredVersionIos: minRequiredVersionIos ?? this.minRequiredVersionIos,
      androidLink: androidLink ?? this.androidLink,
      iosLink: iosLink ?? this.iosLink,
    );
  }

  /// Factory constructor for default/empty model
  ///
  /// Creates a model with default values for testing or fallback scenarios
  ///
  /// @return AppVersionControlModel with default values
  factory AppVersionControlModel.empty() {
    return const AppVersionControlModel(
      latestVersionAndroid: '1.0.0',
      latestVersionIos: '1.0.0',
      minRequiredVersionAndroid: '1.0.0',
      minRequiredVersionIos: '1.0.0',
      androidLink: '',
      iosLink: '',
    );
  }

  /// Validate the model data
  ///
  /// Checks if all required fields are present and valid
  ///
  /// @return True if model data is valid
  bool isValid() {
    return latestVersionAndroid.isNotEmpty &&
        latestVersionIos.isNotEmpty &&
        minRequiredVersionAndroid.isNotEmpty &&
        minRequiredVersionIos.isNotEmpty &&
        androidLink.isNotEmpty &&
        iosLink.isNotEmpty;
  }

  @override
  String toString() {
    return 'AppVersionControlModel('
        'latestVersionAndroid: $latestVersionAndroid, '
        'latestVersionIos: $latestVersionIos, '
        'minRequiredVersionAndroid: $minRequiredVersionAndroid, '
        'minRequiredVersionIos: $minRequiredVersionIos, '
        'androidLink: $androidLink, '
        'iosLink: $iosLink)';
  }
}
