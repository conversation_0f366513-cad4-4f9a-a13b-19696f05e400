/// App Update Remote Data Source
///
/// Handles remote data operations for app update functionality
/// Provides access to version control data from Firestore
library app_update_remote_datasource;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/app_update/data/models/app_version_control_model.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// App Update Remote Data Source Interface
///
/// Defines the contract for remote app update data operations
/// Allows for easy mocking in tests
abstract class AppUpdateRemoteDataSource {
  /// Get version control data from Firestore
  ///
  /// Retrieves the version control document from app_settings collection
  ///
  /// @return A Future that resolves to AppVersionControlModel
  /// @throws Exception if unable to fetch data
  Future<AppVersionControlModel> getVersionControl();
}

/// App Update Remote Data Source Implementation
///
/// Implements remote data source using Firebase Firestore
/// Handles retrieval of version control information
class AppUpdateRemoteDataSourceImpl implements AppUpdateRemoteDataSource {
  /// Firebase service for Firestore operations
  final FirebaseService _firebaseService;

  /// Collection name in Firestore
  static const String _collectionName = 'app_settings';

  /// Document ID for version control
  static const String _documentId = 'version_control';

  /// Creates an AppUpdateRemoteDataSourceImpl
  ///
  /// @param firebaseService Firebase service instance
  const AppUpdateRemoteDataSourceImpl(this._firebaseService);

  @override
  Future<AppVersionControlModel> getVersionControl() async {
    try {
      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Fetching version control from Firestore');
      }

      // Get the version control document from Firestore
      final documentSnapshot = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      if (!documentSnapshot.exists) {
        if (kDebugMode) {
          print('AppUpdateRemoteDataSource: Version control document does not exist');
        }
        throw Exception('Version control document not found');
      }

      final data = documentSnapshot.data() as Map<String, dynamic>?;
      if (data == null) {
        if (kDebugMode) {
          print('AppUpdateRemoteDataSource: Version control document has no data');
        }
        throw Exception('Version control document has no data');
      }

      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Retrieved version control data: $data');
      }

      // Convert Firestore data to model
      final versionControlModel = AppVersionControlModel.fromJson(data);

      // Validate the retrieved data
      if (!versionControlModel.isValid()) {
        if (kDebugMode) {
          print('AppUpdateRemoteDataSource: Invalid version control data');
        }
        throw Exception('Invalid version control data');
      }

      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Successfully parsed version control: $versionControlModel');
      }

      return versionControlModel;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Error fetching version control - $e');
      }
      rethrow;
    }
  }

  /// Test connection to Firestore
  ///
  /// Verifies that the Firestore connection is working
  /// and the version control document exists
  ///
  /// @return A Future that resolves to true if connection is successful
  Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Testing Firestore connection');
      }

      final documentSnapshot = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      final isConnected = documentSnapshot.exists;

      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Connection test result: $isConnected');
      }

      return isConnected;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Connection test failed - $e');
      }
      return false;
    }
  }

  /// Get raw version control data
  ///
  /// Retrieves the raw document data without parsing
  /// Useful for debugging and testing
  ///
  /// @return A Future that resolves to raw document data
  Future<Map<String, dynamic>?> getRawVersionControlData() async {
    try {
      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Fetching raw version control data');
      }

      final documentSnapshot = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      if (!documentSnapshot.exists) {
        if (kDebugMode) {
          print('AppUpdateRemoteDataSource: Version control document does not exist');
        }
        return null;
      }

      final data = documentSnapshot.data() as Map<String, dynamic>?;

      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Raw version control data: $data');
      }

      return data;
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateRemoteDataSource: Error fetching raw version control data - $e');
      }
      return null;
    }
  }
}
