/// App Update View
///
/// Screen that displays update information and options to users
/// Handles both optional and mandatory update scenarios
library app_update_view;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';
import 'package:towasl/features/splash/presentation/widgets/splash_logo_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:towasl/l10n/app_localizations.dart';

/// App Update View
///
/// Displays update information and provides options for users
/// Supports both optional and mandatory update flows
class AppUpdateView extends ConsumerStatefulWidget {
  /// Update check result containing version information
  final UpdateCheckResultEntity updateResult;

  /// Creates an AppUpdateView
  ///
  /// @param updateResult Update check result with version information
  const AppUpdateView({
    super.key,
    required this.updateResult,
  });

  @override
  ConsumerState<AppUpdateView> createState() => _AppUpdateViewState();
}

/// State for the AppUpdateView
class _AppUpdateViewState extends ConsumerState<AppUpdateView> {
  /// Loading state for update actions
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      body: SafeArea(
        child: _buildBody(),
      ),
    );
  }

  /// Build the main body of the update screen
  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Logo section
          const Expanded(
            flex: 2,
            child: Center(
              child: SplashLogoStaticWidget(
                height: 120,
                width: double.infinity,
              ),
            ),
          ),

          // Update content section
          Expanded(
            flex: 3,
            child: _buildUpdateContent(),
          ),

          // Action buttons section
          _buildActionButtons(),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  /// Build the update content section
  Widget _buildUpdateContent() {
    final isMandatory = widget.updateResult.isMandatory;
    final latestVersion =
        widget.updateResult.versionControl.currentPlatformLatestVersion;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Update icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: isMandatory ? AppColors.red : AppColors.primaryPurple,
            borderRadius: BorderRadius.circular(40),
          ),
          child: Icon(
            isMandatory ? Icons.system_update_alt : Icons.update,
            color: AppColors.whiteIvory,
            size: 40,
          ),
        ),

        const SizedBox(height: 24),

        // Update title
        Text(
          isMandatory
              ? AppLocalizations.of(context).updateRequired
              : AppLocalizations.of(context).updateAvailable,
          style: stylePrimaryLarge.copyWith(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 16),

        // Update description
        Text(
          isMandatory
              ? AppLocalizations.of(context).mandatoryUpdateDescription
              : AppLocalizations.of(context).optionalUpdateDescription,
          style: styleGreyNormal.copyWith(
            fontSize: 16,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 24),

        // Version information
        _buildVersionInfo(latestVersion),
      ],
    );
  }

  /// Build version information section
  Widget _buildVersionInfo(String latestVersion) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.greyLight.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.greyLight.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context).currentVersion,
                style: styleGreyNormal.copyWith(fontSize: 14),
              ),
              Text(
                widget.updateResult.currentVersion,
                style: stylePrimaryNormal.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context).newVersion,
                style: styleGreyNormal.copyWith(fontSize: 14),
              ),
              Text(
                latestVersion,
                style: stylePrimaryNormal.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build action buttons section
  Widget _buildActionButtons() {
    final isMandatory = widget.updateResult.isMandatory;

    return Column(
      children: [
        // Update button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleUpdatePressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPurple,
              foregroundColor: AppColors.whiteIvory,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.whiteIvory),
                    ),
                  )
                : Text(
                    AppLocalizations.of(context).updateApp,
                    style: styleWhiteLarge.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),

        // Skip button (only for optional updates)
        if (!isMandatory) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: TextButton(
              onPressed: _isLoading ? null : _handleSkipPressed,
              style: TextButton.styleFrom(
                foregroundColor: AppColors.greyDark,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: AppColors.greyLight.withOpacity(0.5),
                    width: 1,
                  ),
                ),
              ),
              child: Text(
                AppLocalizations.of(context).skipNow,
                style: styleGreyNormal.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Handle update button press
  Future<void> _handleUpdatePressed() async {
    setState(() {
      _isLoading = true;
    });

    // Get localized strings before async operations
    final cannotOpenStoreMsg = AppLocalizations.of(context).cannotOpenAppStore;
    final updateLinkNotAvailableMsg =
        AppLocalizations.of(context).updateLinkNotAvailable;
    final errorOpeningStoreMsg =
        AppLocalizations.of(context).errorOpeningAppStore;

    try {
      final storeUrl =
          widget.updateResult.versionControl.currentPlatformStoreLink;

      if (storeUrl.isNotEmpty) {
        final uri = Uri.parse(storeUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          _showErrorSnackBar(cannotOpenStoreMsg);
        }
      } else {
        _showErrorSnackBar(updateLinkNotAvailableMsg);
      }
    } catch (e) {
      _showErrorSnackBar(errorOpeningStoreMsg);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Handle skip button press
  void _handleSkipPressed() {
    Navigator.of(context).pop(false); // Return false to indicate skip
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: styleWhiteLarge.copyWith(fontSize: 14),
            textAlign: TextAlign.right,
          ),
          backgroundColor: AppColors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }
}
