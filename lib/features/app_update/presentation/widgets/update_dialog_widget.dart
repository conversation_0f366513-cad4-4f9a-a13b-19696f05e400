/// Update Dialog Widget
///
/// Reusable dialog components for app update functionality
/// Provides different dialog types for various update scenarios
library update_dialog_widget;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';
import 'package:url_launcher/url_launcher.dart';

/// Update Dialog Widget
///
/// Provides static methods to show different types of update dialogs
class UpdateDialogWidget {
  /// Show optional update dialog
  ///
  /// Displays a dialog for optional updates with skip option
  ///
  /// @param context Build context
  /// @param updateResult Update check result
  /// @return Future that resolves to true if user chose to update
  static Future<bool?> showOptionalUpdateDialog(
    BuildContext context,
    UpdateCheckResultEntity updateResult,
  ) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (context) => _OptionalUpdateDialog(updateResult: updateResult),
    );
  }

  /// Show mandatory update dialog
  ///
  /// Displays a non-dismissible dialog for mandatory updates
  ///
  /// @param context Build context
  /// @param updateResult Update check result
  /// @return Future that resolves when dialog is dismissed
  static Future<void> showMandatoryUpdateDialog(
    BuildContext context,
    UpdateCheckResultEntity updateResult,
  ) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _MandatoryUpdateDialog(updateResult: updateResult),
    );
  }

  /// Show update error dialog
  ///
  /// Displays an error dialog when update check fails
  ///
  /// @param context Build context
  /// @param errorMessage Error message to display
  /// @return Future that resolves when dialog is dismissed
  static Future<void> showUpdateErrorDialog(
    BuildContext context,
    String errorMessage,
  ) {
    return showDialog<void>(
      context: context,
      builder: (context) => _UpdateErrorDialog(errorMessage: errorMessage),
    );
  }
}

/// Optional Update Dialog
///
/// Dialog for optional updates with skip option
class _OptionalUpdateDialog extends StatefulWidget {
  final UpdateCheckResultEntity updateResult;

  const _OptionalUpdateDialog({required this.updateResult});

  @override
  State<_OptionalUpdateDialog> createState() => _OptionalUpdateDialogState();
}

class _OptionalUpdateDialogState extends State<_OptionalUpdateDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.whiteIvory,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primaryPurple.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.update,
              color: AppColors.primaryPurple,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'تحديث متاح',
              style: stylePrimaryLarge.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إصدار جديد متاح من التطبيق مع تحسينات وميزات جديدة.',
            style: styleGreyNormal.copyWith(
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          _buildVersionInfo(),
        ],
      ),
      actions: [
        // Skip button
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: Text(
            'تخطي',
            style: styleGreyNormal.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        // Update button
        ElevatedButton(
          onPressed: _isLoading ? null : _handleUpdate,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryPurple,
            foregroundColor: AppColors.whiteIvory,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.whiteIvory),
                  ),
                )
              : Text(
                  'تحديث',
                  style: styleWhiteLarge.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildVersionInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.greyLight.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإصدار الحالي:',
                style: styleGreyNormal.copyWith(fontSize: 12),
              ),
              Text(
                widget.updateResult.currentVersion,
                style: stylePrimaryNormal.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإصدار الجديد:',
                style: styleGreyNormal.copyWith(fontSize: 12),
              ),
              Text(
                widget.updateResult.versionControl.currentPlatformLatestVersion,
                style: stylePrimaryNormal.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleUpdate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final storeUrl =
          widget.updateResult.versionControl.currentPlatformStoreLink;

      if (storeUrl.isNotEmpty) {
        final uri = Uri.parse(storeUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        } else {
          _showError('لا يمكن فتح متجر التطبيقات');
        }
      } else {
        _showError('رابط التحديث غير متاح');
      }
    } catch (e) {
      _showError('حدث خطأ أثناء فتح متجر التطبيقات');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message, textAlign: TextAlign.right),
          backgroundColor: AppColors.red,
        ),
      );
    }
  }
}

/// Mandatory Update Dialog
///
/// Non-dismissible dialog for mandatory updates
class _MandatoryUpdateDialog extends StatefulWidget {
  final UpdateCheckResultEntity updateResult;

  const _MandatoryUpdateDialog({required this.updateResult});

  @override
  State<_MandatoryUpdateDialog> createState() => _MandatoryUpdateDialogState();
}

class _MandatoryUpdateDialogState extends State<_MandatoryUpdateDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // Prevent back button
      child: AlertDialog(
        backgroundColor: AppColors.whiteIvory,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.system_update_alt,
                color: AppColors.red,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تحديث مطلوب',
                style: TextStyle(
                  color: AppColors.red,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'يجب تحديث التطبيق للمتابعة. الإصدار الحالي لم يعد مدعوماً.',
              style: styleGreyNormal.copyWith(
                fontSize: 14,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            _buildVersionInfo(),
          ],
        ),
        actions: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _handleUpdate,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.red,
                foregroundColor: AppColors.whiteIvory,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.whiteIvory),
                      ),
                    )
                  : Text(
                      'تحديث الآن',
                      style: styleWhiteLarge.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVersionInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.red.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.red.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإصدار الحالي:',
                style: styleGreyNormal.copyWith(fontSize: 12),
              ),
              Text(
                widget.updateResult.currentVersion,
                style: const TextStyle(
                  color: AppColors.red,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الحد الأدنى المطلوب:',
                style: styleGreyNormal.copyWith(fontSize: 12),
              ),
              Text(
                widget.updateResult.versionControl
                    .currentPlatformMinRequiredVersion,
                style: stylePrimaryNormal.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleUpdate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final storeUrl =
          widget.updateResult.versionControl.currentPlatformStoreLink;

      if (storeUrl.isNotEmpty) {
        final uri = Uri.parse(storeUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          _showError('لا يمكن فتح متجر التطبيقات');
        }
      } else {
        _showError('رابط التحديث غير متاح');
      }
    } catch (e) {
      _showError('حدث خطأ أثناء فتح متجر التطبيقات');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message, textAlign: TextAlign.right),
          backgroundColor: AppColors.red,
        ),
      );
    }
  }
}

/// Update Error Dialog
///
/// Dialog for displaying update check errors
class _UpdateErrorDialog extends StatelessWidget {
  final String errorMessage;

  const _UpdateErrorDialog({required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.whiteIvory,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.error_outline,
              color: AppColors.red,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'خطأ في التحديث',
              style: TextStyle(
                color: AppColors.red,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Text(
        errorMessage,
        style: styleGreyNormal.copyWith(
          fontSize: 14,
          height: 1.4,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'موافق',
            style: stylePrimaryNormal.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
