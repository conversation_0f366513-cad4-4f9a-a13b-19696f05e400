/// App Update ViewModel
///
/// ViewModel for managing app update state and business logic
/// Handles version checking and update flow coordination
library app_update_viewmodel;

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/features/app_update/domain/entities/app_version_control_entity.dart';
import 'package:towasl/features/app_update/domain/usecases/check_app_update_usecase.dart';

/// App Update State
///
/// Represents the current state of app update functionality
class AppUpdateState {
  /// Whether update check is in progress
  final bool isLoading;

  /// Update check result
  final UpdateCheckResultEntity? updateResult;

  /// Error message if update check failed
  final String? errorMessage;

  /// Whether update check has been completed
  final bool hasChecked;

  /// Creates an AppUpdateState
  ///
  /// @param isLoading Whether update check is in progress
  /// @param updateResult Update check result
  /// @param errorMessage Error message if check failed
  /// @param hasChecked Whether check has been completed
  const AppUpdateState({
    this.isLoading = false,
    this.updateResult,
    this.errorMessage,
    this.hasChecked = false,
  });

  /// Create a copy with updated values
  ///
  /// @param isLoading Updated loading state
  /// @param updateResult Updated update result
  /// @param errorMessage Updated error message
  /// @param hasChecked Updated check completion state
  /// @return New AppUpdateState with updated values
  AppUpdateState copyWith({
    bool? isLoading,
    UpdateCheckResultEntity? updateResult,
    String? errorMessage,
    bool? hasChecked,
  }) {
    return AppUpdateState(
      isLoading: isLoading ?? this.isLoading,
      updateResult: updateResult ?? this.updateResult,
      errorMessage: errorMessage ?? this.errorMessage,
      hasChecked: hasChecked ?? this.hasChecked,
    );
  }

  /// Clear error message
  ///
  /// @return New AppUpdateState with cleared error
  AppUpdateState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Check if update is available
  ///
  /// @return True if any update is available
  bool get hasUpdate {
    return updateResult?.hasUpdate ?? false;
  }

  /// Check if update is mandatory
  ///
  /// @return True if update is mandatory
  bool get isMandatoryUpdate {
    return updateResult?.isMandatory ?? false;
  }

  /// Check if update is optional
  ///
  /// @return True if update is optional
  bool get isOptionalUpdate {
    return updateResult?.isOptional ?? false;
  }

  @override
  String toString() {
    return 'AppUpdateState('
        'isLoading: $isLoading, '
        'hasUpdate: $hasUpdate, '
        'isMandatory: $isMandatoryUpdate, '
        'errorMessage: $errorMessage, '
        'hasChecked: $hasChecked)';
  }
}

/// App Update ViewModel
///
/// Manages app update state and coordinates update checking logic
class AppUpdateViewModel extends StateNotifier<AppUpdateState> {
  /// Use case for checking app updates
  final CheckAppUpdateUseCase _checkAppUpdateUseCase;

  /// Creates an AppUpdateViewModel
  ///
  /// @param checkAppUpdateUseCase Use case for checking updates
  AppUpdateViewModel(this._checkAppUpdateUseCase) : super(const AppUpdateState());

  /// Check for app updates
  ///
  /// Performs version checking and updates the state accordingly
  ///
  /// @param currentVersion Current app version string
  /// @return Future that completes when check is done
  Future<void> checkForUpdates(String currentVersion) async {
    try {
      if (kDebugMode) {
        print('AppUpdateViewModel: Checking for updates with version $currentVersion');
      }

      // Set loading state
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
      );

      // Perform update check
      final result = await _checkAppUpdateUseCase.execute(currentVersion);

      // Update state with result
      state = state.copyWith(
        isLoading: false,
        updateResult: result,
        hasChecked: true,
      );

      if (kDebugMode) {
        print('AppUpdateViewModel: Update check completed: ${state.toString()}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateViewModel: Error checking for updates - $e');
      }

      // Update state with error
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في التحقق من التحديثات. يرجى المحاولة مرة أخرى.',
        hasChecked: true,
      );
    }
  }

  /// Check for updates with fallback to cached data
  ///
  /// Attempts remote check first, falls back to cached data if needed
  ///
  /// @param currentVersion Current app version string
  /// @return Future that completes when check is done
  Future<void> checkForUpdatesWithFallback(String currentVersion) async {
    try {
      if (kDebugMode) {
        print('AppUpdateViewModel: Checking for updates with fallback for version $currentVersion');
      }

      // Set loading state
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
      );

      // Perform update check with fallback
      final result = await _checkAppUpdateUseCase.executeWithFallback(currentVersion);

      if (result != null) {
        // Update state with result
        state = state.copyWith(
          isLoading: false,
          updateResult: result,
          hasChecked: true,
        );

        if (kDebugMode) {
          print('AppUpdateViewModel: Update check with fallback completed: ${state.toString()}');
        }
      } else {
        // No result available (both remote and cache failed)
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'لا يمكن التحقق من التحديثات حالياً',
          hasChecked: true,
        );

        if (kDebugMode) {
          print('AppUpdateViewModel: Update check with fallback failed - no data available');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppUpdateViewModel: Error in update check with fallback - $e');
      }

      // Update state with error
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في التحقق من التحديثات',
        hasChecked: true,
      );
    }
  }

  /// Reset the update check state
  ///
  /// Clears all state data and resets to initial state
  void reset() {
    if (kDebugMode) {
      print('AppUpdateViewModel: Resetting state');
    }

    state = const AppUpdateState();
  }

  /// Clear error message
  ///
  /// Removes the current error message from state
  void clearError() {
    if (kDebugMode) {
      print('AppUpdateViewModel: Clearing error message');
    }

    state = state.clearError();
  }

  /// Retry update check
  ///
  /// Retries the last update check operation
  ///
  /// @param currentVersion Current app version string
  /// @return Future that completes when retry is done
  Future<void> retryUpdateCheck(String currentVersion) async {
    if (kDebugMode) {
      print('AppUpdateViewModel: Retrying update check');
    }

    // Clear previous error and retry
    clearError();
    await checkForUpdatesWithFallback(currentVersion);
  }

  /// Get update status summary
  ///
  /// Returns a human-readable summary of the current update status
  ///
  /// @return Status summary string
  String getUpdateStatusSummary() {
    if (state.isLoading) {
      return 'جاري التحقق من التحديثات...';
    }

    if (state.errorMessage != null) {
      return state.errorMessage!;
    }

    if (!state.hasChecked) {
      return 'لم يتم التحقق من التحديثات بعد';
    }

    if (state.updateResult == null) {
      return 'لا توجد معلومات تحديث متاحة';
    }

    final result = state.updateResult!;
    
    switch (result.status) {
      case UpdateStatus.noUpdate:
        return 'التطبيق محدث إلى أحدث إصدار';
      case UpdateStatus.optionalUpdate:
        return 'تحديث اختياري متاح';
      case UpdateStatus.mandatoryUpdate:
        return 'تحديث مطلوب';
    }
  }
}
