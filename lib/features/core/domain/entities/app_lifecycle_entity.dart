/// App Lifecycle Entity
///
/// Represents the application lifecycle state in the domain layer
/// Contains information about app foreground/background state and connectivity
library app_lifecycle_entity;

/// App Lifecycle Entity
///
/// Domain entity representing the application lifecycle state
class AppLifecycleEntity {
  /// Whether the app is currently in the foreground
  final bool isAppInForeground;
  
  /// Whether the device has network connectivity
  final bool isConnected;
  
  /// The last time the app was active
  final DateTime lastActiveTime;

  /// Constructor
  ///
  /// @param isAppInForeground Whether the app is in foreground
  /// @param isConnected Whether the device is connected to network
  /// @param lastActiveTime The last time the app was active
  const AppLifecycleEntity({
    required this.isAppInForeground,
    required this.isConnected,
    required this.lastActiveTime,
  });

  /// Create a default app lifecycle entity
  factory AppLifecycleEntity.initial() {
    return AppLifecycleEntity(
      isAppInForeground: true,
      isConnected: true,
      lastActiveTime: DateTime.now(),
    );
  }

  /// Create a copy of this entity with updated values
  ///
  /// @param isAppInForeground Optional new foreground state
  /// @param isConnected Optional new connectivity state
  /// @param lastActiveTime Optional new last active time
  /// @return New AppLifecycleEntity with updated values
  AppLifecycleEntity copyWith({
    bool? isAppInForeground,
    bool? isConnected,
    DateTime? lastActiveTime,
  }) {
    return AppLifecycleEntity(
      isAppInForeground: isAppInForeground ?? this.isAppInForeground,
      isConnected: isConnected ?? this.isConnected,
      lastActiveTime: lastActiveTime ?? this.lastActiveTime,
    );
  }

  /// Check if the app is in background
  bool get isAppInBackground => !isAppInForeground;

  /// Check if the device is offline
  bool get isOffline => !isConnected;

  /// Get the duration since the app was last active
  Duration get timeSinceLastActive {
    return DateTime.now().difference(lastActiveTime);
  }

  /// Check if the app has been inactive for a certain duration
  ///
  /// @param duration The duration to check against
  /// @return True if the app has been inactive for the specified duration
  bool hasBeenInactiveFor(Duration duration) {
    return timeSinceLastActive >= duration;
  }

  /// Check if the app state is stable (foreground and connected)
  bool get isStable => isAppInForeground && isConnected;

  /// Check if the app state is unstable (background or disconnected)
  bool get isUnstable => !isStable;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AppLifecycleEntity &&
           other.isAppInForeground == isAppInForeground &&
           other.isConnected == isConnected &&
           other.lastActiveTime == lastActiveTime;
  }

  @override
  int get hashCode {
    return isAppInForeground.hashCode ^
           isConnected.hashCode ^
           lastActiveTime.hashCode;
  }

  @override
  String toString() {
    return 'AppLifecycleEntity('
           'isAppInForeground: $isAppInForeground, '
           'isConnected: $isConnected, '
           'lastActiveTime: $lastActiveTime'
           ')';
  }
}
