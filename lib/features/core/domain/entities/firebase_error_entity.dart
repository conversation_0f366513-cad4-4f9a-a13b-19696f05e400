/// Firebase Error Entity
///
/// Represents Firebase error state in the domain layer
/// Contains information about Firebase errors and permission states
library firebase_error_entity;

/// Firebase Error Types
enum FirebaseErrorType {
  permissionDenied,
  serviceUnavailable,
  timeout,
  resourceExhausted,
  networkError,
  unknown,
}

/// Firebase Error Entity
///
/// Domain entity representing Firebase error state and information
class FirebaseErrorEntity {
  /// Whether permission has been denied
  final bool isPermissionDenied;
  
  /// The type of the last error that occurred
  final FirebaseErrorType? lastErrorType;
  
  /// The message of the last error
  final String? lastErrorMessage;
  
  /// The code of the last error (if available)
  final String? lastErrorCode;
  
  /// The timestamp when the last error occurred
  final DateTime? lastErrorTime;

  /// Constructor
  ///
  /// @param isPermissionDenied Whether permission is denied
  /// @param lastErrorType The type of the last error
  /// @param lastErrorMessage The message of the last error
  /// @param lastErrorCode The code of the last error
  /// @param lastErrorTime When the last error occurred
  const FirebaseErrorEntity({
    required this.isPermissionDenied,
    this.lastErrorType,
    this.lastErrorMessage,
    this.lastErrorCode,
    this.lastErrorTime,
  });

  /// Create an initial Firebase error entity with no errors
  factory FirebaseErrorEntity.initial() {
    return const FirebaseErrorEntity(
      isPermissionDenied: false,
      lastErrorType: null,
      lastErrorMessage: null,
      lastErrorCode: null,
      lastErrorTime: null,
    );
  }

  /// Create a Firebase error entity with permission denied
  factory FirebaseErrorEntity.permissionDenied({
    String? message,
    String? code,
  }) {
    return FirebaseErrorEntity(
      isPermissionDenied: true,
      lastErrorType: FirebaseErrorType.permissionDenied,
      lastErrorMessage: message ?? 'Permission denied',
      lastErrorCode: code ?? 'permission-denied',
      lastErrorTime: DateTime.now(),
    );
  }

  /// Create a Firebase error entity with a specific error
  factory FirebaseErrorEntity.withError({
    required FirebaseErrorType errorType,
    String? message,
    String? code,
    bool isPermissionDenied = false,
  }) {
    return FirebaseErrorEntity(
      isPermissionDenied: isPermissionDenied,
      lastErrorType: errorType,
      lastErrorMessage: message,
      lastErrorCode: code,
      lastErrorTime: DateTime.now(),
    );
  }

  /// Create a copy of this entity with updated values
  ///
  /// @param isPermissionDenied Optional new permission denied state
  /// @param lastErrorType Optional new error type
  /// @param lastErrorMessage Optional new error message
  /// @param lastErrorCode Optional new error code
  /// @param lastErrorTime Optional new error time
  /// @return New FirebaseErrorEntity with updated values
  FirebaseErrorEntity copyWith({
    bool? isPermissionDenied,
    FirebaseErrorType? lastErrorType,
    String? lastErrorMessage,
    String? lastErrorCode,
    DateTime? lastErrorTime,
  }) {
    return FirebaseErrorEntity(
      isPermissionDenied: isPermissionDenied ?? this.isPermissionDenied,
      lastErrorType: lastErrorType ?? this.lastErrorType,
      lastErrorMessage: lastErrorMessage ?? this.lastErrorMessage,
      lastErrorCode: lastErrorCode ?? this.lastErrorCode,
      lastErrorTime: lastErrorTime ?? this.lastErrorTime,
    );
  }

  /// Clear all error information
  FirebaseErrorEntity clearErrors() {
    return const FirebaseErrorEntity(
      isPermissionDenied: false,
      lastErrorType: null,
      lastErrorMessage: null,
      lastErrorCode: null,
      lastErrorTime: null,
    );
  }

  /// Check if there are any errors
  bool get hasErrors => lastErrorType != null;

  /// Check if there are no errors
  bool get hasNoErrors => !hasErrors;

  /// Check if the last error was a network error
  bool get isNetworkError => lastErrorType == FirebaseErrorType.networkError;

  /// Check if the last error was a timeout
  bool get isTimeoutError => lastErrorType == FirebaseErrorType.timeout;

  /// Check if the last error was service unavailable
  bool get isServiceUnavailableError => lastErrorType == FirebaseErrorType.serviceUnavailable;

  /// Get a user-friendly error message
  String get userFriendlyMessage {
    if (lastErrorMessage != null) {
      return lastErrorMessage!;
    }

    switch (lastErrorType) {
      case FirebaseErrorType.permissionDenied:
        return 'Access denied. Please check your permissions.';
      case FirebaseErrorType.serviceUnavailable:
        return 'Service is currently unavailable. Please try again later.';
      case FirebaseErrorType.timeout:
        return 'Request timed out. Please check your connection and try again.';
      case FirebaseErrorType.resourceExhausted:
        return 'Service limit reached. Please try again later.';
      case FirebaseErrorType.networkError:
        return 'Network error. Please check your connection.';
      case FirebaseErrorType.unknown:
      case null:
        return 'An unexpected error occurred.';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is FirebaseErrorEntity &&
           other.isPermissionDenied == isPermissionDenied &&
           other.lastErrorType == lastErrorType &&
           other.lastErrorMessage == lastErrorMessage &&
           other.lastErrorCode == lastErrorCode &&
           other.lastErrorTime == lastErrorTime;
  }

  @override
  int get hashCode {
    return isPermissionDenied.hashCode ^
           lastErrorType.hashCode ^
           lastErrorMessage.hashCode ^
           lastErrorCode.hashCode ^
           lastErrorTime.hashCode;
  }

  @override
  String toString() {
    return 'FirebaseErrorEntity('
           'isPermissionDenied: $isPermissionDenied, '
           'lastErrorType: $lastErrorType, '
           'lastErrorMessage: $lastErrorMessage, '
           'lastErrorCode: $lastErrorCode, '
           'lastErrorTime: $lastErrorTime'
           ')';
  }
}
