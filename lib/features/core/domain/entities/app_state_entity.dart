/// App State Entity
///
/// Represents the global application state in the domain layer
/// Contains user information, API credentials, and authentication state
library app_state_entity;

import 'package:towasl/shared/models/user_model.dart';

/// App State Entity
///
/// Domain entity representing the global application state
class AppStateEntity {
  /// User identification
  final String userId;

  /// User model containing profile information
  final UserModel userModel;

  /// API credentials for external services
  final String msegatApikey;
  final String msegatUserSender;
  final String msegatUsername;

  /// Authentication provider information
  final String providerName;

  /// Constructor
  ///
  /// @param userId The current user ID
  /// @param userModel The user model with profile information
  /// @param msegatApikey The API key for Msegat service
  /// @param msegatUserSender The user sender for Msegat service
  /// @param msegatUsername The username for Msegat service
  /// @param providerName The authentication provider name
  const AppStateEntity({
    required this.userId,
    required this.userModel,
    required this.msegatApikey,
    required this.msegatUserSender,
    required this.msegatUsername,
    required this.providerName,
  });

  /// Create an empty app state entity
  factory AppStateEntity.empty() {
    return AppStateEntity(
      userId: '',
      userModel: UserModel(),
      msegatApikey: "6c3076fac5b70da2eb1d59bcb1475b8b",
      msegatUserSender: '',
      msegatUsername: '',
      providerName: '',
    );
  }

  /// Create a copy of this entity with updated values
  ///
  /// @param userId Optional new user ID
  /// @param userModel Optional new user model
  /// @param msegatApikey Optional new API key
  /// @param msegatUserSender Optional new user sender
  /// @param msegatUsername Optional new username
  /// @param providerName Optional new provider name
  /// @return New AppStateEntity with updated values
  AppStateEntity copyWith({
    String? userId,
    UserModel? userModel,
    String? msegatApikey,
    String? msegatUserSender,
    String? msegatUsername,
    String? providerName,
  }) {
    return AppStateEntity(
      userId: userId ?? this.userId,
      userModel: userModel ?? this.userModel,
      msegatApikey: msegatApikey ?? this.msegatApikey,
      msegatUserSender: msegatUserSender ?? this.msegatUserSender,
      msegatUsername: msegatUsername ?? this.msegatUsername,
      providerName: providerName ?? this.providerName,
    );
  }

  /// Check if user is logged in
  bool get isLoggedIn => userId.isNotEmpty;

  /// Check if user has complete profile
  bool get hasCompleteProfile {
    return isLoggedIn;
  }

  /// Check if API credentials are configured
  bool get hasApiCredentials {
    return msegatApikey.isNotEmpty &&
        msegatUserSender.isNotEmpty &&
        msegatUsername.isNotEmpty;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AppStateEntity &&
        other.userId == userId &&
        other.userModel == userModel &&
        other.msegatApikey == msegatApikey &&
        other.msegatUserSender == msegatUserSender &&
        other.msegatUsername == msegatUsername &&
        other.providerName == providerName;
  }

  @override
  int get hashCode {
    return userId.hashCode ^
        userModel.hashCode ^
        msegatApikey.hashCode ^
        msegatUserSender.hashCode ^
        msegatUsername.hashCode ^
        providerName.hashCode;
  }

  @override
  String toString() {
    return 'AppStateEntity('
        'userId: $userId, '
        'userModel: $userModel, '
        'msegatApikey: $msegatApikey, '
        'msegatUserSender: $msegatUserSender, '
        'msegatUsername: $msegatUsername, '
        'providerName: $providerName'
        ')';
  }
}
