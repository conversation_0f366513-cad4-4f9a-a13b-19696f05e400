/// Update App State Use Case
///
/// Handles updating the application state
/// Part of the domain layer in Clean Architecture
library update_app_state_usecase;

import 'package:towasl/features/core/domain/entities/app_state_entity.dart';
import 'package:towasl/features/core/domain/repositories/app_state_repository.dart';
import 'package:towasl/shared/models/user_model.dart';

/// Update App State Use Case
///
/// Encapsulates the business logic for updating application state
class UpdateAppStateUseCase {
  final AppStateRepository _repository;

  /// Constructor
  ///
  /// @param repository The app state repository
  UpdateAppStateUseCase(this._repository);

  /// Execute the use case to update app state
  ///
  /// @param appState The new app state to save
  /// @return Future that completes when the state is updated
  Future<void> execute(AppStateEntity appState) async {
    await _repository.updateAppState(appState);
  }

  /// Update the user ID
  ///
  /// @param userId The new user ID to set
  /// @return Future that completes when the user ID is updated
  Future<void> updateUserId(String userId) async {
    await _repository.setUserId(userId);
  }

  /// Update the user model
  ///
  /// @param userModel The new user model to set
  /// @return Future that completes when the user model is updated
  Future<void> updateUserModel(UserModel userModel) async {
    await _repository.setUserModel(userModel);
  }

  /// Update Msegat credentials
  ///
  /// @param userSender The new user sender
  /// @param username The new username
  /// @return Future that completes when credentials are updated
  Future<void> updateMsegatCredentials({
    String? userSender,
    String? username,
  }) async {
    if (userSender != null) {
      await _repository.setMsegatUserSender(userSender);
    }
    if (username != null) {
      await _repository.setMsegatUsername(username);
    }
  }

  /// Update the provider name
  ///
  /// @param providerName The new provider name to set
  /// @return Future that completes when the provider name is updated
  Future<void> updateProviderName(String providerName) async {
    await _repository.setProviderName(providerName);
  }

  /// Clear all user data
  ///
  /// @return Future that completes when all user data is cleared
  Future<void> clearUserData() async {
    await _repository.clearUserData();
  }

  /// Save current user data to persistent storage
  ///
  /// @return Future that completes when user data is saved
  Future<void> saveUserData() async {
    await _repository.saveUserData();
  }
}
