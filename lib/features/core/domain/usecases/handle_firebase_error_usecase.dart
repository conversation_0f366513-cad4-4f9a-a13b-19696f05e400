/// Handle Firebase Error Use Case
///
/// Handles Firebase error management and processing
/// Part of the domain layer in Clean Architecture
library handle_firebase_error_usecase;

import 'package:towasl/features/core/domain/entities/firebase_error_entity.dart';
import 'package:towasl/features/core/domain/repositories/firebase_error_repository.dart';

/// Handle Firebase Error Use Case
///
/// Encapsulates the business logic for handling Firebase errors
class HandleFirebaseErrorUseCase {
  final FirebaseErrorRepository _repository;

  /// Constructor
  ///
  /// @param repository The Firebase error repository
  HandleFirebaseErrorUseCase(this._repository);

  /// Initialize Firebase error handling
  ///
  /// @return Future that completes when error handling is initialized
  Future<void> initialize() async {
    await _repository.initializeErrorHandling();
  }

  /// Dispose Firebase error handling
  ///
  /// @return Future that completes when error handling is disposed
  Future<void> dispose() async {
    await _repository.disposeErrorHandling();
  }

  /// Handle a Firebase error
  ///
  /// @param error The error to handle
  /// @return Future that completes when the error is handled
  Future<void> handleError(dynamic error) async {
    await _repository.handleFirebaseError(error);
  }

  /// Get the current Firebase error state
  ///
  /// @return Future that resolves to the current FirebaseErrorEntity
  Future<FirebaseErrorEntity> getCurrentErrorState() async {
    return await _repository.getFirebaseErrorState();
  }

  /// Check if permission is currently denied
  ///
  /// @return Future that resolves to true if permission is denied
  Future<bool> isPermissionDenied() async {
    return await _repository.isPermissionDenied();
  }

  /// Reset the permission denied state
  ///
  /// @return Future that completes when the state is reset
  Future<void> resetPermissionDenied() async {
    await _repository.resetPermissionDenied();
  }

  /// Clear all error information
  ///
  /// @return Future that completes when all errors are cleared
  Future<void> clearErrors() async {
    await _repository.clearErrors();
  }

  /// Check if an error is network-related
  ///
  /// @param error The error to check
  /// @return Future that resolves to true if the error is network-related
  Future<bool> isNetworkError(dynamic error) async {
    return await _repository.isNetworkError(error);
  }

  /// Check if an error is permission-related
  ///
  /// @param error The error to check
  /// @return Future that resolves to true if the error is permission-related
  Future<bool> isPermissionError(dynamic error) async {
    return await _repository.isPermissionError(error);
  }

  /// Get error state stream for reactive updates
  ///
  /// @return Stream that emits FirebaseErrorEntity when state changes
  Stream<FirebaseErrorEntity> getErrorStateStream() {
    return _repository.errorStateStream;
  }

  /// Get permission denied stream for reactive updates
  ///
  /// @return Stream that emits bool when permission denied state changes
  Stream<bool> getPermissionDeniedStream() {
    return _repository.permissionDeniedStream;
  }
}
