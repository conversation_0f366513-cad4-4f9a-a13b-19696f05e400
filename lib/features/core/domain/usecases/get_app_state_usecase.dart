/// Get App State Use Case
///
/// <PERSON>les retrieving the current application state
/// Part of the domain layer in Clean Architecture
library get_app_state_usecase;

import 'package:towasl/features/core/domain/entities/app_state_entity.dart';
import 'package:towasl/features/core/domain/repositories/app_state_repository.dart';

/// Get App State Use Case
///
/// Encapsulates the business logic for retrieving application state
class GetAppStateUseCase {
  final AppStateRepository _repository;

  /// Constructor
  ///
  /// @param repository The app state repository
  GetAppStateUseCase(this._repository);

  /// Execute the use case to get current app state
  ///
  /// @return Future that resolves to the current AppStateEntity
  Future<AppStateEntity> execute() async {
    return await _repository.getAppState();
  }

  /// Get the current user ID
  ///
  /// @return Future that resolves to the current user ID
  Future<String> getUserId() async {
    return await _repository.getUserId();
  }

  /// Check if user is logged in
  ///
  /// @return Future that resolves to true if user is logged in
  Future<bool> isUserLoggedIn() async {
    return await _repository.isUserLoggedIn();
  }

  /// Get app state stream for reactive updates
  ///
  /// @return Stream that emits AppStateEntity when state changes
  Stream<AppStateEntity> getAppStateStream() {
    return _repository.appStateStream;
  }

  /// Get user ID stream for reactive updates
  ///
  /// @return Stream that emits String when user ID changes
  Stream<String> getUserIdStream() {
    return _repository.userIdStream;
  }
}
