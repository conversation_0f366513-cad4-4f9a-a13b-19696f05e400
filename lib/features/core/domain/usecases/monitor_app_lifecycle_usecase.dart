/// Monitor App Lifecycle Use Case
///
/// Handles monitoring and managing application lifecycle
/// Part of the domain layer in Clean Architecture
library monitor_app_lifecycle_usecase;

import 'package:flutter/material.dart';
import 'package:towasl/features/core/domain/entities/app_lifecycle_entity.dart';
import 'package:towasl/features/core/domain/repositories/app_lifecycle_repository.dart';

/// Monitor App Lifecycle Use Case
///
/// Encapsulates the business logic for monitoring application lifecycle
class MonitorAppLifecycleUseCase {
  final AppLifecycleRepository _repository;

  /// Constructor
  ///
  /// @param repository The app lifecycle repository
  MonitorAppLifecycleUseCase(this._repository);

  /// Initialize lifecycle monitoring
  ///
  /// @return Future that completes when monitoring is initialized
  Future<void> initialize() async {
    await _repository.initializeLifecycleMonitoring();
  }

  /// Dispose lifecycle monitoring
  ///
  /// @return Future that completes when monitoring is disposed
  Future<void> dispose() async {
    await _repository.disposeLifecycleMonitoring();
  }

  /// Get the current app lifecycle state
  ///
  /// @return Future that resolves to the current AppLifecycleEntity
  Future<AppLifecycleEntity> getCurrentState() async {
    return await _repository.getAppLifecycleState();
  }

  /// Handle app lifecycle state changes
  ///
  /// @param state The new app lifecycle state
  /// @return Future that completes when the state change is handled
  Future<void> handleLifecycleStateChange(AppLifecycleState state) async {
    await _repository.handleAppLifecycleStateChange(state);
  }

  /// Update app foreground state
  ///
  /// @param isInForeground Whether the app is in foreground
  /// @return Future that completes when the state is updated
  Future<void> updateForegroundState(bool isInForeground) async {
    await _repository.setAppForegroundState(isInForeground);
    await _repository.updateLastActiveTime(DateTime.now());
  }

  /// Update connectivity state
  ///
  /// @param isConnected Whether the device is connected
  /// @return Future that completes when the state is updated
  Future<void> updateConnectivityState(bool isConnected) async {
    await _repository.setConnectivityState(isConnected);
  }

  /// Check if app is in foreground
  ///
  /// @return Future that resolves to true if app is in foreground
  Future<bool> isAppInForeground() async {
    return await _repository.isAppInForeground();
  }

  /// Check if device is connected
  ///
  /// @return Future that resolves to true if connected
  Future<bool> isConnected() async {
    return await _repository.isConnected();
  }

  /// Validate session when app resumes
  ///
  /// @return Future that completes when session validation is done
  Future<void> validateSessionOnResume() async {
    await _repository.validateSessionOnResume();
  }

  /// Validate session when connectivity is restored
  ///
  /// @return Future that completes when session validation is done
  Future<void> validateSessionOnReconnect() async {
    await _repository.validateSessionOnReconnect();
  }

  /// Get lifecycle state stream for reactive updates
  ///
  /// @return Stream that emits AppLifecycleEntity when state changes
  Stream<AppLifecycleEntity> getLifecycleStateStream() {
    return _repository.lifecycleStateStream;
  }

  /// Get foreground state stream for reactive updates
  ///
  /// @return Stream that emits bool when foreground state changes
  Stream<bool> getForegroundStateStream() {
    return _repository.foregroundStateStream;
  }

  /// Get connectivity state stream for reactive updates
  ///
  /// @return Stream that emits bool when connectivity state changes
  Stream<bool> getConnectivityStateStream() {
    return _repository.connectivityStateStream;
  }
}
