/// Firebase Error Repository Interface
///
/// Defines the contract for Firebase error data operations
/// Part of the domain layer in Clean Architecture
library firebase_error_repository;

import 'package:towasl/features/core/domain/entities/firebase_error_entity.dart';

/// Firebase Error Repository Interface
///
/// Defines methods for managing Firebase error state and handling
abstract class FirebaseErrorRepository {
  /// Get the current Firebase error state
  ///
  /// @return Future that resolves to the current FirebaseErrorEntity
  Future<FirebaseErrorEntity> getFirebaseErrorState();

  /// Update the Firebase error state
  ///
  /// @param errorState The new error state to save
  /// @return Future that completes when the state is updated
  Future<void> updateFirebaseErrorState(FirebaseErrorEntity errorState);

  /// Check if permission is currently denied
  ///
  /// @return Future that resolves to true if permission is denied
  Future<bool> isPermissionDenied();

  /// Set the permission denied state
  ///
  /// @param isDenied Whether permission is denied
  /// @return Future that completes when the state is set
  Future<void> setPermissionDenied(bool isDenied);

  /// Reset the permission denied state
  ///
  /// @return Future that completes when the state is reset
  Future<void> resetPermissionDenied();

  /// Handle a Firebase error
  ///
  /// @param error The error to handle
  /// @return Future that completes when the error is handled
  Future<void> handleFirebaseError(dynamic error);

  /// Handle a Firebase exception specifically
  ///
  /// @param exception The Firebase exception to handle
  /// @return Future that completes when the exception is handled
  Future<void> handleFirebaseException(dynamic exception);

  /// Handle permission denied errors
  ///
  /// @param exception The permission denied exception
  /// @return Future that completes when the error is handled
  Future<void> handlePermissionDenied(dynamic exception);

  /// Handle service unavailable errors
  ///
  /// @param exception The service unavailable exception
  /// @return Future that completes when the error is handled
  Future<void> handleServiceUnavailable(dynamic exception);

  /// Handle timeout errors
  ///
  /// @param exception The timeout exception
  /// @return Future that completes when the error is handled
  Future<void> handleTimeout(dynamic exception);

  /// Handle resource exhausted errors
  ///
  /// @param exception The resource exhausted exception
  /// @return Future that completes when the error is handled
  Future<void> handleResourceExhausted(dynamic exception);

  /// Handle unknown Firebase errors
  ///
  /// @param exception The unknown Firebase exception
  /// @return Future that completes when the error is handled
  Future<void> handleUnknownFirebaseError(dynamic exception);

  /// Handle generic (non-Firebase) errors
  ///
  /// @param error The generic error
  /// @return Future that completes when the error is handled
  Future<void> handleGenericError(dynamic error);

  /// Initialize Firebase error handling
  ///
  /// @return Future that completes when error handling is initialized
  Future<void> initializeErrorHandling();

  /// Dispose Firebase error handling
  ///
  /// @return Future that completes when error handling is disposed
  Future<void> disposeErrorHandling();

  /// Check if an error is a network-related error
  ///
  /// @param error The error to check
  /// @return Future that resolves to true if the error is network-related
  Future<bool> isNetworkError(dynamic error);

  /// Check if an error is a permission-related error
  ///
  /// @param error The error to check
  /// @return Future that resolves to true if the error is permission-related
  Future<bool> isPermissionError(dynamic error);

  /// Clear all error information
  ///
  /// @return Future that completes when all errors are cleared
  Future<void> clearErrors();

  /// Stream of Firebase error state changes
  ///
  /// @return Stream that emits FirebaseErrorEntity when state changes
  Stream<FirebaseErrorEntity> get errorStateStream;

  /// Stream of permission denied state changes
  ///
  /// @return Stream that emits bool when permission denied state changes
  Stream<bool> get permissionDeniedStream;
}
