/// App State Repository Interface
///
/// Defines the contract for app state data operations
/// Part of the domain layer in Clean Architecture
library app_state_repository;

import 'package:towasl/features/core/domain/entities/app_state_entity.dart';
import 'package:towasl/shared/models/user_model.dart';

/// App State Repository Interface
///
/// Defines methods for managing global application state
abstract class AppStateRepository {
  /// Get the current app state
  ///
  /// @return Future that resolves to the current AppStateEntity
  Future<AppStateEntity> getAppState();

  /// Update the app state
  ///
  /// @param appState The new app state to save
  /// @return Future that completes when the state is updated
  Future<void> updateAppState(AppStateEntity appState);

  /// Get the current user ID
  ///
  /// @return Future that resolves to the current user ID
  Future<String> getUserId();

  /// Set the user ID
  ///
  /// @param userId The new user ID to set
  /// @return Future that completes when the user ID is set
  Future<void> setUserId(String userId);

  /// Get the current user model
  ///
  /// @return Future that resolves to the current UserModel
  Future<UserModel> getUserModel();

  /// Set the user model
  ///
  /// @param userModel The new user model to set
  /// @return Future that completes when the user model is set
  Future<void> setUserModel(UserModel userModel);

  /// Get the Msegat API key
  ///
  /// @return Future that resolves to the API key
  Future<String> getMsegatApikey();

  /// Get the Msegat user sender
  ///
  /// @return Future that resolves to the user sender
  Future<String> getMsegatUserSender();

  /// Set the Msegat user sender
  ///
  /// @param sender The new user sender to set
  /// @return Future that completes when the sender is set
  Future<void> setMsegatUserSender(String sender);

  /// Get the Msegat username
  ///
  /// @return Future that resolves to the username
  Future<String> getMsegatUsername();

  /// Set the Msegat username
  ///
  /// @param username The new username to set
  /// @return Future that completes when the username is set
  Future<void> setMsegatUsername(String username);

  /// Get the provider name
  ///
  /// @return Future that resolves to the provider name
  Future<String> getProviderName();

  /// Set the provider name
  ///
  /// @param name The new provider name to set
  /// @return Future that completes when the provider name is set
  Future<void> setProviderName(String name);

  /// Clear all user data
  ///
  /// @return Future that completes when all user data is cleared
  Future<void> clearUserData();

  /// Check if user is logged in
  ///
  /// @return Future that resolves to true if user is logged in
  Future<bool> isUserLoggedIn();

  /// Load user data from persistent storage
  ///
  /// @return Future that completes when user data is loaded
  Future<void> loadUserData();

  /// Save user data to persistent storage
  ///
  /// @return Future that completes when user data is saved
  Future<void> saveUserData();

  /// Stream of app state changes
  ///
  /// @return Stream that emits AppStateEntity when state changes
  Stream<AppStateEntity> get appStateStream;

  /// Stream of user ID changes
  ///
  /// @return Stream that emits String when user ID changes
  Stream<String> get userIdStream;

  /// Stream of user model changes
  ///
  /// @return Stream that emits UserModel when user model changes
  Stream<UserModel> get userModelStream;
}
