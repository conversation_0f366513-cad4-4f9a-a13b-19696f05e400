/// App Lifecycle Repository Interface
///
/// Defines the contract for app lifecycle data operations
/// Part of the domain layer in Clean Architecture
library app_lifecycle_repository;

import 'package:flutter/material.dart';
import 'package:towasl/features/core/domain/entities/app_lifecycle_entity.dart';

/// App Lifecycle Repository Interface
///
/// Defines methods for managing application lifecycle state
abstract class AppLifecycleRepository {
  /// Get the current app lifecycle state
  ///
  /// @return Future that resolves to the current AppLifecycleEntity
  Future<AppLifecycleEntity> getAppLifecycleState();

  /// Update the app lifecycle state
  ///
  /// @param lifecycleState The new lifecycle state to save
  /// @return Future that completes when the state is updated
  Future<void> updateAppLifecycleState(AppLifecycleEntity lifecycleState);

  /// Check if the app is currently in foreground
  ///
  /// @return Future that resolves to true if app is in foreground
  Future<bool> isAppInForeground();

  /// Set the app foreground state
  ///
  /// @param isInForeground Whether the app is in foreground
  /// @return Future that completes when the state is set
  Future<void> setAppForegroundState(bool isInForeground);

  /// Check if the device has network connectivity
  ///
  /// @return Future that resolves to true if connected
  Future<bool> isConnected();

  /// Set the connectivity state
  ///
  /// @param isConnected Whether the device is connected
  /// @return Future that completes when the state is set
  Future<void> setConnectivityState(bool isConnected);

  /// Get the last active time
  ///
  /// @return Future that resolves to the last active DateTime
  Future<DateTime> getLastActiveTime();

  /// Update the last active time
  ///
  /// @param time The new last active time
  /// @return Future that completes when the time is updated
  Future<void> updateLastActiveTime(DateTime time);

  /// Initialize lifecycle monitoring
  ///
  /// @return Future that completes when monitoring is initialized
  Future<void> initializeLifecycleMonitoring();

  /// Dispose lifecycle monitoring
  ///
  /// @return Future that completes when monitoring is disposed
  Future<void> disposeLifecycleMonitoring();

  /// Handle app lifecycle state changes
  ///
  /// @param state The new app lifecycle state
  /// @return Future that completes when the state change is handled
  Future<void> handleAppLifecycleStateChange(AppLifecycleState state);

  /// Validate session when app resumes
  ///
  /// @return Future that completes when session validation is done
  Future<void> validateSessionOnResume();

  /// Validate session when connectivity is restored
  ///
  /// @return Future that completes when session validation is done
  Future<void> validateSessionOnReconnect();

  /// Stream of app lifecycle state changes
  ///
  /// @return Stream that emits AppLifecycleEntity when state changes
  Stream<AppLifecycleEntity> get lifecycleStateStream;

  /// Stream of app foreground state changes
  ///
  /// @return Stream that emits bool when foreground state changes
  Stream<bool> get foregroundStateStream;

  /// Stream of connectivity state changes
  ///
  /// @return Stream that emits bool when connectivity state changes
  Stream<bool> get connectivityStateStream;

  /// Stream of last active time changes
  ///
  /// @return Stream that emits DateTime when last active time changes
  Stream<DateTime> get lastActiveTimeStream;
}
