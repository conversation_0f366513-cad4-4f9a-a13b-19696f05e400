/// App Lifecycle Provider
///
/// Provides Riverpod state management for app lifecycle functionality
/// Replaces AppLifecycleViewModel with Riverpod architecture
library app_lifecycle_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_lifecycle_provider.g.dart';

/// App lifecycle states
enum AppLifecycleStateEnum {
  resumed,
  inactive,
  paused,
  detached,
  hidden,
}

/// State class for app lifecycle management
class AppLifecycleState {
  /// Current app lifecycle state
  final AppLifecycleStateEnum currentState;

  /// Previous app lifecycle state
  final AppLifecycleStateEnum? previousState;

  /// Whether app is in foreground
  final bool isInForeground;

  /// Whether app is in background
  final bool isInBackground;

  /// Last time app went to background
  final DateTime? backgroundTime;

  /// Last time app came to foreground
  final DateTime? foregroundTime;

  const AppLifecycleState({
    this.currentState = AppLifecycleStateEnum.resumed,
    this.previousState,
    this.isInForeground = true,
    this.isInBackground = false,
    this.backgroundTime,
    this.foregroundTime,
  });

  AppLifecycleState copyWith({
    AppLifecycleStateEnum? currentState,
    AppLifecycleStateEnum? previousState,
    bool? isInForeground,
    bool? isInBackground,
    DateTime? backgroundTime,
    DateTime? foregroundTime,
  }) {
    return AppLifecycleState(
      currentState: currentState ?? this.currentState,
      previousState: previousState ?? this.previousState,
      isInForeground: isInForeground ?? this.isInForeground,
      isInBackground: isInBackground ?? this.isInBackground,
      backgroundTime: backgroundTime ?? this.backgroundTime,
      foregroundTime: foregroundTime ?? this.foregroundTime,
    );
  }
}

/// App Lifecycle Notifier
///
/// Manages app lifecycle state and handles lifecycle events
/// Follows MVVM pattern with Riverpod state management
@riverpod
class AppLifecycle extends _$AppLifecycle {
  @override
  AppLifecycleState build() {
    if (kDebugMode) {
      print('AppLifecycleNotifier: Initialized');
    }

    return const AppLifecycleState();
  }

  /// Handle app lifecycle state change
  void onLifecycleStateChanged(AppLifecycleStateEnum newState) {
    final previousState = state.currentState;

    // Update state
    state = state.copyWith(
      currentState: newState,
      previousState: previousState,
      isInForeground: _isInForeground(newState),
      isInBackground: _isInBackground(newState),
    );

    // Handle specific transitions
    _handleStateTransition(previousState, newState);

    if (kDebugMode) {
      print(
          'AppLifecycleNotifier: State changed from $previousState to $newState');
    }
  }

  /// Simulate app going to background
  void simulateAppToBackground() {
    onLifecycleStateChanged(AppLifecycleStateEnum.paused);
  }

  /// Simulate app coming to foreground
  void simulateAppToForeground() {
    onLifecycleStateChanged(AppLifecycleStateEnum.resumed);
  }

  /// Check if app is in foreground
  bool _isInForeground(AppLifecycleStateEnum state) {
    return state == AppLifecycleStateEnum.resumed;
  }

  /// Check if app is in background
  bool _isInBackground(AppLifecycleStateEnum state) {
    return state == AppLifecycleStateEnum.paused ||
        state == AppLifecycleStateEnum.detached ||
        state == AppLifecycleStateEnum.hidden;
  }

  /// Handle state transitions
  void _handleStateTransition(
    AppLifecycleStateEnum previousState,
    AppLifecycleStateEnum newState,
  ) {
    // App went to background
    if (_isInForeground(previousState) && _isInBackground(newState)) {
      _onAppWentToBackground();
    }

    // App came to foreground
    if (_isInBackground(previousState) && _isInForeground(newState)) {
      _onAppCameToForeground();
    }
  }

  /// Handle app going to background
  void _onAppWentToBackground() {
    state = state.copyWith(backgroundTime: DateTime.now());

    if (kDebugMode) {
      print('AppLifecycleNotifier: App went to background');
    }

    // Perform background tasks
    _performBackgroundTasks();
  }

  /// Handle app coming to foreground
  void _onAppCameToForeground() {
    state = state.copyWith(foregroundTime: DateTime.now());

    if (kDebugMode) {
      print('AppLifecycleNotifier: App came to foreground');
    }

    // Perform foreground tasks
    _performForegroundTasks();
  }

  /// Perform tasks when app goes to background
  void _performBackgroundTasks() {
    // Save app state
    // Pause timers
    // Reduce resource usage

    if (kDebugMode) {
      print('AppLifecycleNotifier: Performing background tasks');
    }
  }

  /// Perform tasks when app comes to foreground
  void _performForegroundTasks() {
    // Refresh data
    // Resume timers
    // Check for updates

    if (kDebugMode) {
      print('AppLifecycleNotifier: Performing foreground tasks');
    }
  }

  /// Get time spent in background
  Duration? getTimeInBackground() {
    if (state.backgroundTime != null && state.foregroundTime != null) {
      return state.foregroundTime!.difference(state.backgroundTime!);
    }
    return null;
  }

  /// Check if app has been in background for too long
  bool hasBeenInBackgroundTooLong(
      {Duration threshold = const Duration(minutes: 30)}) {
    final timeInBackground = getTimeInBackground();
    return timeInBackground != null && timeInBackground > threshold;
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for current app lifecycle state
@riverpod
AppLifecycleStateEnum currentAppLifecycleState(
    CurrentAppLifecycleStateRef ref) {
  return ref.watch(appLifecycleProvider).currentState;
}

/// Provider for checking if app is in foreground
@riverpod
bool isAppInForeground(IsAppInForegroundRef ref) {
  return ref.watch(appLifecycleProvider).isInForeground;
}

/// Provider for checking if app is in background
@riverpod
bool isAppInBackground(IsAppInBackgroundRef ref) {
  return ref.watch(appLifecycleProvider).isInBackground;
}

/// Provider for background time
@riverpod
DateTime? appBackgroundTime(AppBackgroundTimeRef ref) {
  return ref.watch(appLifecycleProvider).backgroundTime;
}

/// Provider for foreground time
@riverpod
DateTime? appForegroundTime(AppForegroundTimeRef ref) {
  return ref.watch(appLifecycleProvider).foregroundTime;
}
