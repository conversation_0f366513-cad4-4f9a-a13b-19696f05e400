// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_lifecycle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentAppLifecycleStateHash() =>
    r'd0c2c09abbcaca78093b9ade24e4a416d17754f1';

/// Provider for current app lifecycle state
///
/// Copied from [currentAppLifecycleState].
@ProviderFor(currentAppLifecycleState)
final currentAppLifecycleStateProvider =
    AutoDisposeProvider<AppLifecycleStateEnum>.internal(
  currentAppLifecycleState,
  name: r'currentAppLifecycleStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAppLifecycleStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentAppLifecycleStateRef
    = AutoDisposeProviderRef<AppLifecycleStateEnum>;
String _$isAppInForegroundHash() => r'570cdd0caf80034ad2baf1b1d57f14a00d21c3e0';

/// Provider for checking if app is in foreground
///
/// Copied from [isAppInForeground].
@ProviderFor(isAppInForeground)
final isAppInForegroundProvider = AutoDisposeProvider<bool>.internal(
  isAppInForeground,
  name: r'isAppInForegroundProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAppInForegroundHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsAppInForegroundRef = AutoDisposeProviderRef<bool>;
String _$isAppInBackgroundHash() => r'9e7b8cc56fb5ab4ce512a3c6ce968cb8a1fd3206';

/// Provider for checking if app is in background
///
/// Copied from [isAppInBackground].
@ProviderFor(isAppInBackground)
final isAppInBackgroundProvider = AutoDisposeProvider<bool>.internal(
  isAppInBackground,
  name: r'isAppInBackgroundProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAppInBackgroundHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsAppInBackgroundRef = AutoDisposeProviderRef<bool>;
String _$appBackgroundTimeHash() => r'094bb5539682781692e290b011b54ce75986ad24';

/// Provider for background time
///
/// Copied from [appBackgroundTime].
@ProviderFor(appBackgroundTime)
final appBackgroundTimeProvider = AutoDisposeProvider<DateTime?>.internal(
  appBackgroundTime,
  name: r'appBackgroundTimeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appBackgroundTimeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AppBackgroundTimeRef = AutoDisposeProviderRef<DateTime?>;
String _$appForegroundTimeHash() => r'ba13cabcbc4eb53ba28710b3f8dccd829c47eba4';

/// Provider for foreground time
///
/// Copied from [appForegroundTime].
@ProviderFor(appForegroundTime)
final appForegroundTimeProvider = AutoDisposeProvider<DateTime?>.internal(
  appForegroundTime,
  name: r'appForegroundTimeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appForegroundTimeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AppForegroundTimeRef = AutoDisposeProviderRef<DateTime?>;
String _$appLifecycleHash() => r'2abe7ee7f0be1cf018af939f5cf9715c62bb8a3d';

/// App Lifecycle Notifier
///
/// Manages app lifecycle state and handles lifecycle events
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [AppLifecycle].
@ProviderFor(AppLifecycle)
final appLifecycleProvider =
    AutoDisposeNotifierProvider<AppLifecycle, AppLifecycleState>.internal(
  AppLifecycle.new,
  name: r'appLifecycleProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$appLifecycleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppLifecycle = AutoDisposeNotifier<AppLifecycleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
