// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'use_case_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appStateUseCaseHash() => r'cc145416e2f62b3f54943999cdad36ef189adf82';

/// App State Use Case Provider
///
/// Provides use cases for app state management
///
/// Copied from [appStateUseCase].
@ProviderFor(appStateUseCase)
final appStateUseCaseProvider = AutoDisposeProvider<AppStateUseCase>.internal(
  appStateUseCase,
  name: r'appStateUseCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appStateUseCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AppStateUseCaseRef = AutoDisposeProviderRef<AppStateUseCase>;
String _$userProfileUseCaseHash() =>
    r'7753eb33011b71c9be3151199f542a1bbe6c5a86';

/// User Profile Use Case Provider
///
/// Provides use cases for user profile operations
///
/// Copied from [userProfileUseCase].
@ProviderFor(userProfileUseCase)
final userProfileUseCaseProvider =
    AutoDisposeProvider<UserProfileUseCase>.internal(
  userProfileUseCase,
  name: r'userProfileUseCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userProfileUseCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserProfileUseCaseRef = AutoDisposeProviderRef<UserProfileUseCase>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
