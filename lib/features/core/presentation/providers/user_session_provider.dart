/// User Session Provider
///
/// Provides Riverpod state management for user session functionality
/// Replaces UserSessionViewModel with Riverpod architecture
library user_session_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';

part 'user_session_provider.g.dart';

/// User session states
enum SessionStatus {
  active,
  inactive,
  expired,
  invalid,
}

/// State class for user session management
class UserSessionState {
  /// Current session status
  final SessionStatus status;

  /// Session token
  final String? sessionToken;

  /// Session creation time
  final DateTime? sessionCreatedAt;

  /// Session last activity time
  final DateTime? lastActivityAt;

  /// Session expiry time
  final DateTime? expiresAt;

  /// User ID associated with session
  final String? userId;

  /// Whether session is being validated
  final bool isValidating;

  /// Whether session is being refreshed
  final bool isRefreshing;

  /// Error message if any
  final String? errorMessage;

  const UserSessionState({
    this.status = SessionStatus.inactive,
    this.sessionToken,
    this.sessionCreatedAt,
    this.lastActivityAt,
    this.expiresAt,
    this.userId,
    this.isValidating = false,
    this.isRefreshing = false,
    this.errorMessage,
  });

  UserSessionState copyWith({
    SessionStatus? status,
    String? sessionToken,
    DateTime? sessionCreatedAt,
    DateTime? lastActivityAt,
    DateTime? expiresAt,
    String? userId,
    bool? isValidating,
    bool? isRefreshing,
    String? errorMessage,
  }) {
    return UserSessionState(
      status: status ?? this.status,
      sessionToken: sessionToken ?? this.sessionToken,
      sessionCreatedAt: sessionCreatedAt ?? this.sessionCreatedAt,
      lastActivityAt: lastActivityAt ?? this.lastActivityAt,
      expiresAt: expiresAt ?? this.expiresAt,
      userId: userId ?? this.userId,
      isValidating: isValidating ?? this.isValidating,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  UserSessionState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Check if session is active
  bool get isActive => status == SessionStatus.active;

  /// Check if session is expired
  bool get isExpired => status == SessionStatus.expired;

  /// Check if session is valid
  bool get isValid =>
      status == SessionStatus.active || status == SessionStatus.inactive;

  /// Get session duration
  Duration? get sessionDuration {
    if (sessionCreatedAt != null && lastActivityAt != null) {
      return lastActivityAt!.difference(sessionCreatedAt!);
    }
    return null;
  }

  /// Check if session will expire soon (within 5 minutes)
  bool get willExpireSoon {
    if (expiresAt == null) return false;
    final now = DateTime.now();
    final timeUntilExpiry = expiresAt!.difference(now);
    return timeUntilExpiry.inMinutes <= 5;
  }
}

/// User Session Notifier
///
/// Manages user session state and handles session lifecycle
/// Follows MVVM pattern with Riverpod state management
@riverpod
class UserSession extends _$UserSession {
  @override
  UserSessionState build() {
    // Load existing session on initialization
    _loadExistingSession();

    if (kDebugMode) {
      print('UserSessionNotifier: Initialized');
    }

    return const UserSessionState();
  }

  /// Load existing session from storage
  Future<void> _loadExistingSession() async {
    try {
      // Get user ID from app state
      final userId = ref.read(userIdProvider);

      if (userId.isNotEmpty) {
        // Simulate loading session from secure storage
        await Future.delayed(const Duration(milliseconds: 300));

        // Mock session data
        final sessionToken = _generateSessionToken();
        final now = DateTime.now();

        state = state.copyWith(
          status: SessionStatus.active,
          sessionToken: sessionToken,
          sessionCreatedAt: now.subtract(const Duration(hours: 2)),
          lastActivityAt: now,
          expiresAt: now.add(const Duration(days: 7)), // 7 days expiry
          userId: userId,
        );

        if (kDebugMode) {
          print(
              'UserSessionNotifier: Existing session loaded for user: $userId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('UserSessionNotifier: Error loading existing session - $e');
      }
    }
  }

  /// Create new session
  Future<bool> createSession(String userId) async {
    if (kDebugMode) {
      print('UserSessionNotifier: Creating session for user: $userId');
    }

    try {
      state = state.copyWith(isValidating: true);

      // Generate session token
      final sessionToken = _generateSessionToken();
      final now = DateTime.now();

      // Create session
      state = state.copyWith(
        status: SessionStatus.active,
        sessionToken: sessionToken,
        sessionCreatedAt: now,
        lastActivityAt: now,
        expiresAt: now.add(const Duration(days: 7)), // 7 days expiry
        userId: userId,
        isValidating: false,
      );

      // Save session to storage
      await _saveSessionToStorage();

      if (kDebugMode) {
        print('UserSessionNotifier: Session created successfully');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isValidating: false,
        errorMessage: 'Failed to create session',
      );

      if (kDebugMode) {
        print('UserSessionNotifier: Error creating session - $e');
      }

      return false;
    }
  }

  /// Validate current session
  Future<bool> validateSession() async {
    if (state.sessionToken == null) {
      state = state.copyWith(status: SessionStatus.inactive);
      return false;
    }

    if (kDebugMode) {
      print('UserSessionNotifier: Validating session');
    }

    try {
      state = state.copyWith(isValidating: true);

      // Check if session is expired
      if (state.expiresAt != null && DateTime.now().isAfter(state.expiresAt!)) {
        state = state.copyWith(
          status: SessionStatus.expired,
          isValidating: false,
        );

        if (kDebugMode) {
          print('UserSessionNotifier: Session expired');
        }

        return false;
      }

      // Simulate session validation
      await Future.delayed(const Duration(milliseconds: 500));

      // Update last activity
      state = state.copyWith(
        status: SessionStatus.active,
        lastActivityAt: DateTime.now(),
        isValidating: false,
      );

      if (kDebugMode) {
        print('UserSessionNotifier: Session validated successfully');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        status: SessionStatus.invalid,
        isValidating: false,
        errorMessage: 'Session validation failed',
      );

      if (kDebugMode) {
        print('UserSessionNotifier: Error validating session - $e');
      }

      return false;
    }
  }

  /// Refresh session
  Future<bool> refreshSession() async {
    if (state.sessionToken == null) {
      return false;
    }

    if (kDebugMode) {
      print('UserSessionNotifier: Refreshing session');
    }

    try {
      state = state.copyWith(isRefreshing: true);

      // Simulate session refresh
      await Future.delayed(const Duration(milliseconds: 800));

      // Generate new token and extend expiry
      final newToken = _generateSessionToken();
      final now = DateTime.now();

      state = state.copyWith(
        sessionToken: newToken,
        lastActivityAt: now,
        expiresAt: now.add(const Duration(days: 7)), // Extend for 7 more days
        isRefreshing: false,
      );

      // Save updated session
      await _saveSessionToStorage();

      if (kDebugMode) {
        print('UserSessionNotifier: Session refreshed successfully');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isRefreshing: false,
        errorMessage: 'Failed to refresh session',
      );

      if (kDebugMode) {
        print('UserSessionNotifier: Error refreshing session - $e');
      }

      return false;
    }
  }

  /// End current session
  Future<void> endSession() async {
    if (kDebugMode) {
      print('UserSessionNotifier: Ending session');
    }

    try {
      // Clear session from storage
      await _clearSessionFromStorage();

      // Reset state
      state = const UserSessionState(status: SessionStatus.inactive);

      if (kDebugMode) {
        print('UserSessionNotifier: Session ended successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UserSessionNotifier: Error ending session - $e');
      }
    }
  }

  /// Update last activity time
  void updateLastActivity() {
    if (state.isActive) {
      state = state.copyWith(lastActivityAt: DateTime.now());
    }
  }

  /// Generate session token
  String _generateSessionToken() {
    // In real implementation, this would be a secure random token
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 1000000).toString();
    return 'session_${random}_${timestamp.toString().substring(8)}';
  }

  /// Save session to storage
  Future<void> _saveSessionToStorage() async {
    // In real implementation, this would save to secure storage
    await Future.delayed(const Duration(milliseconds: 100));

    if (kDebugMode) {
      print('UserSessionNotifier: Session saved to storage');
    }
  }

  /// Clear session from storage
  Future<void> _clearSessionFromStorage() async {
    // In real implementation, this would clear from secure storage
    await Future.delayed(const Duration(milliseconds: 100));

    if (kDebugMode) {
      print('UserSessionNotifier: Session cleared from storage');
    }
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for session status
@riverpod
SessionStatus sessionStatus(SessionStatusRef ref) {
  return ref.watch(userSessionProvider).status;
}

/// Provider for checking if session is active
@riverpod
bool isSessionActive(IsSessionActiveRef ref) {
  return ref.watch(userSessionProvider).isActive;
}

/// Provider for checking if session is expired
@riverpod
bool isSessionExpired(IsSessionExpiredRef ref) {
  return ref.watch(userSessionProvider).isExpired;
}

/// Provider for session token
@riverpod
String? sessionToken(SessionTokenRef ref) {
  return ref.watch(userSessionProvider).sessionToken;
}

/// Provider for session validation state
@riverpod
bool isSessionValidating(IsSessionValidatingRef ref) {
  return ref.watch(userSessionProvider).isValidating;
}

/// Provider for session refresh state
@riverpod
bool isSessionRefreshing(IsSessionRefreshingRef ref) {
  return ref.watch(userSessionProvider).isRefreshing;
}

/// Provider for checking if session will expire soon
@riverpod
bool willSessionExpireSoon(WillSessionExpireSoonRef ref) {
  return ref.watch(userSessionProvider).willExpireSoon;
}

/// Provider for session error message
@riverpod
String? sessionErrorMessage(SessionErrorMessageRef ref) {
  return ref.watch(userSessionProvider).errorMessage;
}
