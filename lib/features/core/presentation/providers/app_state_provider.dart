/// App State Provider
///
/// Provides global application state management using Riverpod
/// Replaces AppStateViewModel with Riverpod state management
library app_state_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/core/domain/entities/app_state_entity.dart';
import 'package:towasl/features/core/presentation/providers/use_case_provider.dart';
import 'package:towasl/shared/models/user_model.dart';

part 'app_state_provider.g.dart';

/// App State Notifier
///
/// Manages global application state using Riverpod
@riverpod
class AppState extends _$AppState {
  @override
  AsyncValue<AppStateEntity> build() {
    // Initialize with empty state, actual loading happens in loadAppState()
    return AsyncValue.data(AppStateEntity.empty());
  }

  /// Load the current app state
  Future<void> loadAppState() async {
    try {
      state = const AsyncValue.loading();

      // Use the app state use case to load data
      final useCase = ref.read(appStateUseCaseProvider);

      // Load user ID from storage
      final userId = await useCase.loadUserId();

      // Load user model if user ID exists
      UserModel? userModel;
      if (userId != null && userId.isNotEmpty) {
        userModel = await useCase.loadUserModel(userId);
      }

      // Create app state entity
      final appState = AppStateEntity(
        userId: userId ?? '',
        userModel: userModel ?? UserModel(),
        msegatApikey: "6c3076fac5b70da2eb1d59bcb1475b8b", // Default API key
        msegatUserSender: '',
        msegatUsername: '',
        providerName: '',
      );

      state = AsyncValue.data(appState);

      if (kDebugMode) {
        print(
            'AppStateNotifier: App state loaded - User ID: ${appState.userId}');
        print('AppStateNotifier: User logged in: ${appState.isLoggedIn}');
        print(
            'AppStateNotifier: Profile complete: ${appState.hasCompleteProfile}');
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      if (kDebugMode) {
        print('AppStateNotifier: Error loading app state - $error');
      }
    }
  }

  /// Update the user ID
  ///
  /// @param userId The new user ID to set
  Future<void> setUserId(String userId) async {
    try {
      // Use the app state use case to persist the user ID
      final useCase = ref.read(appStateUseCaseProvider);
      await useCase.saveUserId(userId);

      // Update local state
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncValue.data(currentState.copyWith(userId: userId));
      }

      if (kDebugMode) {
        print('AppStateNotifier: User ID updated and saved: $userId');
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      if (kDebugMode) {
        print('AppStateNotifier: Error updating user ID - $error');
      }
    }
  }

  /// Update the user model
  ///
  /// @param userModel The new user model to set
  Future<void> setUserModel(UserModel userModel) async {
    try {
      // Use the app state use case to persist the user model
      final useCase = ref.read(appStateUseCaseProvider);
      await useCase.saveUserModel(userModel);

      // Update local state
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncValue.data(currentState.copyWith(userModel: userModel));
      }

      if (kDebugMode) {
        print('AppStateNotifier: User model updated and saved');
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      if (kDebugMode) {
        print('AppStateNotifier: Error updating user model - $error');
      }
    }
  }

  /// Clear all user data
  Future<void> clearUserData() async {
    try {
      // Update local state
      state = AsyncValue.data(AppStateEntity.empty());

      if (kDebugMode) {
        print('AppStateNotifier: User data cleared');
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      if (kDebugMode) {
        print('AppStateNotifier: Error clearing user data - $error');
      }
    }
  }

  /// Refresh the app state from the data source
  Future<void> refreshAppState() async {
    await loadAppState();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS FOR ACCESSING APP STATE PROPERTIES
// ============================================================================

/// Provider for current user ID
@riverpod
String userId(UserIdRef ref) {
  final appState = ref.watch(appStateProvider);
  return appState.when(
    data: (state) => state.userId,
    loading: () => '',
    error: (_, __) => '',
  );
}

/// Provider for current user model
@riverpod
UserModel userModel(UserModelRef ref) {
  final appState = ref.watch(appStateProvider);
  return appState.when(
    data: (state) => state.userModel,
    loading: () => UserModel(),
    error: (_, __) => UserModel(),
  );
}

/// Provider for checking if user is logged in
@riverpod
bool isUserLoggedIn(IsUserLoggedInRef ref) {
  final appState = ref.watch(appStateProvider);
  return appState.when(
    data: (state) => state.isLoggedIn,
    loading: () => false,
    error: (_, __) => false,
  );
}

/// Provider for checking if user has complete profile
@riverpod
bool hasCompleteProfile(HasCompleteProfileRef ref) {
  final appState = ref.watch(appStateProvider);
  return appState.when(
    data: (state) => state.hasCompleteProfile,
    loading: () => false,
    error: (_, __) => false,
  );
}
