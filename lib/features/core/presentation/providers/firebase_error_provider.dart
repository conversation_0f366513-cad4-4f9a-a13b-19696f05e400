/// Firebase Error Provider
///
/// Provides Riverpod state management for Firebase error handling
/// Replaces FirebaseErrorViewModel with Riverpod architecture
library firebase_error_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'firebase_error_provider.g.dart';

/// Firebase error types
enum FirebaseErrorType {
  network,
  authentication,
  permission,
  storage,
  database,
  unknown,
}

/// State class for Firebase error management
class FirebaseErrorState {
  /// Current error type
  final FirebaseErrorType? errorType;
  
  /// Error message
  final String? errorMessage;
  
  /// Error code from Firebase
  final String? errorCode;
  
  /// Whether error is being handled
  final bool isHandlingError;
  
  /// Error timestamp
  final DateTime? errorTimestamp;
  
  /// Whether error has been acknowledged by user
  final bool isAcknowledged;

  const FirebaseErrorState({
    this.errorType,
    this.errorMessage,
    this.errorCode,
    this.isHandlingError = false,
    this.errorTimestamp,
    this.isAcknowledged = false,
  });

  FirebaseErrorState copyWith({
    FirebaseErrorType? errorType,
    String? errorMessage,
    String? errorCode,
    bool? isHandlingError,
    DateTime? errorTimestamp,
    bool? isAcknowledged,
  }) {
    return FirebaseErrorState(
      errorType: errorType ?? this.errorType,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      isHandlingError: isHandlingError ?? this.isHandlingError,
      errorTimestamp: errorTimestamp ?? this.errorTimestamp,
      isAcknowledged: isAcknowledged ?? this.isAcknowledged,
    );
  }

  /// Clear error state
  FirebaseErrorState clearError() {
    return const FirebaseErrorState();
  }

  /// Check if there's an active error
  bool get hasError => errorType != null && !isAcknowledged;
}

/// Firebase Error Notifier
///
/// Manages Firebase error state and provides error handling utilities
/// Follows MVVM pattern with Riverpod state management
@riverpod
class FirebaseError extends _$FirebaseError {
  @override
  FirebaseErrorState build() {
    if (kDebugMode) {
      print('FirebaseErrorNotifier: Initialized');
    }
    
    return const FirebaseErrorState();
  }

  /// Handle Firebase error
  void handleError(
    String errorCode,
    String errorMessage, {
    FirebaseErrorType? errorType,
  }) {
    final type = errorType ?? _determineErrorType(errorCode);
    
    state = state.copyWith(
      errorType: type,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorTimestamp: DateTime.now(),
      isAcknowledged: false,
    );
    
    if (kDebugMode) {
      print('FirebaseErrorNotifier: Error handled - Type: $type, Code: $errorCode, Message: $errorMessage');
    }
  }

  /// Determine error type from error code
  FirebaseErrorType _determineErrorType(String errorCode) {
    if (errorCode.contains('network') || errorCode.contains('timeout')) {
      return FirebaseErrorType.network;
    } else if (errorCode.contains('auth') || errorCode.contains('user')) {
      return FirebaseErrorType.authentication;
    } else if (errorCode.contains('permission') || errorCode.contains('denied')) {
      return FirebaseErrorType.permission;
    } else if (errorCode.contains('storage')) {
      return FirebaseErrorType.storage;
    } else if (errorCode.contains('database') || errorCode.contains('firestore')) {
      return FirebaseErrorType.database;
    } else {
      return FirebaseErrorType.unknown;
    }
  }

  /// Get user-friendly error message
  String getUserFriendlyMessage() {
    if (state.errorType == null) return '';
    
    switch (state.errorType!) {
      case FirebaseErrorType.network:
        return 'Network connection error. Please check your internet connection and try again.';
      case FirebaseErrorType.authentication:
        return 'Authentication error. Please log in again.';
      case FirebaseErrorType.permission:
        return 'Permission denied. You don\'t have access to this resource.';
      case FirebaseErrorType.storage:
        return 'Storage error. Failed to upload or download files.';
      case FirebaseErrorType.database:
        return 'Database error. Failed to save or retrieve data.';
      case FirebaseErrorType.unknown:
        return state.errorMessage ?? 'An unexpected error occurred. Please try again.';
    }
  }

  /// Acknowledge error (mark as seen by user)
  void acknowledgeError() {
    state = state.copyWith(isAcknowledged: true);
    
    if (kDebugMode) {
      print('FirebaseErrorNotifier: Error acknowledged');
    }
  }

  /// Clear error state
  void clearError() {
    state = state.clearError();
    
    if (kDebugMode) {
      print('FirebaseErrorNotifier: Error cleared');
    }
  }

  /// Retry operation (clear error and allow retry)
  void retryOperation() {
    clearError();
    
    if (kDebugMode) {
      print('FirebaseErrorNotifier: Retrying operation');
    }
  }

  /// Check if error is retryable
  bool isRetryable() {
    if (state.errorType == null) return false;
    
    switch (state.errorType!) {
      case FirebaseErrorType.network:
      case FirebaseErrorType.unknown:
        return true;
      case FirebaseErrorType.authentication:
      case FirebaseErrorType.permission:
      case FirebaseErrorType.storage:
      case FirebaseErrorType.database:
        return false;
    }
  }

  /// Get error severity level
  String getErrorSeverity() {
    if (state.errorType == null) return 'info';
    
    switch (state.errorType!) {
      case FirebaseErrorType.network:
      case FirebaseErrorType.unknown:
        return 'warning';
      case FirebaseErrorType.authentication:
      case FirebaseErrorType.permission:
        return 'error';
      case FirebaseErrorType.storage:
      case FirebaseErrorType.database:
        return 'error';
    }
  }

  /// Log error for analytics
  void logError() {
    if (state.errorType == null) return;
    
    // In real implementation, this would log to Firebase Analytics or Crashlytics
    if (kDebugMode) {
      print('FirebaseErrorNotifier: Logging error for analytics');
      print('Error Type: ${state.errorType}');
      print('Error Code: ${state.errorCode}');
      print('Error Message: ${state.errorMessage}');
      print('Timestamp: ${state.errorTimestamp}');
    }
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for current error type
@riverpod
FirebaseErrorType? currentErrorType(CurrentErrorTypeRef ref) {
  return ref.watch(firebaseErrorProvider).errorType;
}

/// Provider for current error message
@riverpod
String? currentErrorMessage(CurrentErrorMessageRef ref) {
  return ref.watch(firebaseErrorProvider).errorMessage;
}

/// Provider for current error code
@riverpod
String? currentErrorCode(CurrentErrorCodeRef ref) {
  return ref.watch(firebaseErrorProvider).errorCode;
}

/// Provider for checking if there's an active error
@riverpod
bool hasFirebaseError(HasFirebaseErrorRef ref) {
  return ref.watch(firebaseErrorProvider).hasError;
}

/// Provider for error handling state
@riverpod
bool isHandlingFirebaseError(IsHandlingFirebaseErrorRef ref) {
  return ref.watch(firebaseErrorProvider).isHandlingError;
}

/// Provider for user-friendly error message
@riverpod
String userFriendlyErrorMessage(UserFriendlyErrorMessageRef ref) {
  return ref.read(firebaseErrorProvider.notifier).getUserFriendlyMessage();
}

/// Provider for checking if error is retryable
@riverpod
bool isErrorRetryable(IsErrorRetryableRef ref) {
  return ref.read(firebaseErrorProvider.notifier).isRetryable();
}

/// Provider for error severity
@riverpod
String errorSeverity(ErrorSeverityRef ref) {
  return ref.read(firebaseErrorProvider.notifier).getErrorSeverity();
}
