/// Use Case Provider
///
/// Provides Riverpod providers for application use cases
/// Integrates business logic with state management
library use_case_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/shared/models/user_model.dart';
import 'package:towasl/shared/services/storage_service.dart';

part 'use_case_provider.g.dart';

/// App State Use Case Provider
///
/// Provides use cases for app state management
@riverpod
AppStateUseCase appStateUseCase(AppStateUseCaseRef ref) {
  return AppStateUseCaseImpl(
    ref.read(storageServiceProvider),
    ref.read(userRepositoryProvider),
  );
}

/// App State Use Case Implementation
///
/// Handles business logic for app state operations
class AppStateUseCaseImpl implements AppStateUseCase {
  final StorageService _storageService;
  final UserRepository _userRepository;

  const AppStateUseCaseImpl(this._storageService, this._userRepository);

  @override
  Future<String?> loadUserId() async {
    try {
      if (kDebugMode) {
        print('AppStateUseCase: Loading user ID from storage');
      }

      // Use storage service to load user ID
      final userId = _storageService.getUserIDValue();

      if (kDebugMode) {
        print('AppStateUseCase: Loaded user ID: $userId');
      }

      return userId.isNotEmpty ? userId : null;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error loading user ID - $e');
      }
      return null;
    }
  }

  @override
  Future<UserModel?> loadUserModel(String userId) async {
    try {
      if (kDebugMode) {
        print('AppStateUseCase: Loading user model for: $userId');
      }

      final user = await _userRepository.getUserById(userId);

      if (kDebugMode) {
        print('AppStateUseCase: Loaded user model: $userId');
      }

      return user;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error loading user model - $e');
      }
      return null;
    }
  }

  @override
  Future<bool> saveUserId(String userId) async {
    try {
      if (kDebugMode) {
        print('AppStateUseCase: Saving user ID: $userId');
      }

      // Use storage service to save user ID and login status
      _storageService.setLoginData(userId);

      if (kDebugMode) {
        print('AppStateUseCase: User ID saved successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error saving user ID - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> saveUserModel(UserModel user) async {
    try {
      if (kDebugMode) {
        print('AppStateUseCase: Saving user model: ${user.userId}');
      }

      final success = await _userRepository.saveUser(user);

      if (success) {
        // Also save user ID to storage
        await saveUserId(user.userId ?? '');
      }

      if (kDebugMode) {
        print('AppStateUseCase: User model saved: $success');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error saving user model - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> clearUserData() async {
    try {
      if (kDebugMode) {
        print('AppStateUseCase: Clearing user data');
      }

      // Use storage service to clear user data
      await _storageService.clearUserData();

      if (kDebugMode) {
        print('AppStateUseCase: User data cleared successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error clearing user data - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> isUserLoggedIn() async {
    try {
      final userId = await loadUserId();
      final isLoggedIn = userId != null && userId.isNotEmpty;

      if (kDebugMode) {
        print('AppStateUseCase: User logged in status: $isLoggedIn');
      }

      return isLoggedIn;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error checking login status - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> hasCompleteProfile(String userId) async {
    try {
      final user = await _userRepository.getUserById(userId);
      if (user == null) return false;

      final hasInterests =
          user.userInterest != null && user.userInterest!.isNotEmpty;
      final hasLocation = user.userLocation != null &&
          user.userLocation!.city != null &&
          user.userLocation!.city!.isNotEmpty;
      final hasPersonalInfo = user.birthdayYear != null &&
          user.birthdayYear!.isNotEmpty &&
          user.gender != null &&
          user.gender!.isNotEmpty;

      final isComplete = hasInterests && hasLocation && hasPersonalInfo;

      if (kDebugMode) {
        print('AppStateUseCase: Profile completion status for $userId:');
        print('  - Has interests: $hasInterests');
        print('  - Has location: $hasLocation');
        print('  - Has personal info: $hasPersonalInfo');
        print('  - Is complete: $isComplete');
      }

      return isComplete;
    } catch (e) {
      if (kDebugMode) {
        print('AppStateUseCase: Error checking profile completion - $e');
      }
      return false;
    }
  }
}

/// Abstract App State Use Case Interface
///
/// Defines the contract for app state business logic
abstract class AppStateUseCase {
  Future<String?> loadUserId();
  Future<UserModel?> loadUserModel(String userId);
  Future<bool> saveUserId(String userId);
  Future<bool> saveUserModel(UserModel user);
  Future<bool> clearUserData();
  Future<bool> isUserLoggedIn();
  Future<bool> hasCompleteProfile(String userId);
}

/// User Profile Use Case Provider
///
/// Provides use cases for user profile operations
@riverpod
UserProfileUseCase userProfileUseCase(UserProfileUseCaseRef ref) {
  return UserProfileUseCaseImpl(
    ref.read(userRepositoryProvider),
  );
}

/// User Profile Use Case Implementation
///
/// Handles business logic for user profile operations
class UserProfileUseCaseImpl implements UserProfileUseCase {
  final UserRepository _userRepository;

  const UserProfileUseCaseImpl(this._userRepository);

  @override
  Future<bool> updateUserInterests(
      String userId, Map<String, List<String>> interests) async {
    try {
      final user = await _userRepository.getUserById(userId);
      if (user == null) return false;

      final updatedUser = UserModel(
        userId: user.userId,
        mobile: user.mobile,
        countryCode: user.countryCode,
        userLocation: user.userLocation,
        birthdayYear: user.birthdayYear,
        selectedInterests: user.selectedInterests,
        gender: user.gender,
        nationality: user.nationality,
      );

      return await _userRepository.updateUser(updatedUser);
    } catch (e) {
      if (kDebugMode) {
        print('UserProfileUseCase: Error updating interests - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> updateUserLocation(String userId, UserLocation location) async {
    try {
      final user = await _userRepository.getUserById(userId);
      if (user == null) return false;

      final updatedUser = UserModel(
        userId: user.userId,
        mobile: user.mobile,
        countryCode: user.countryCode,
        userLocation: location,
        birthdayYear: user.birthdayYear,
        selectedInterests: user.selectedInterests,
        gender: user.gender,
        nationality: user.nationality,
      );

      return await _userRepository.updateUser(updatedUser);
    } catch (e) {
      if (kDebugMode) {
        print('UserProfileUseCase: Error updating location - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> updatePersonalInfo(
    String userId, {
    String? birthdayYear,
    String? gender,
    String? nationality,
  }) async {
    try {
      final user = await _userRepository.getUserById(userId);
      if (user == null) return false;

      final updatedUser = UserModel(
        userId: user.userId,
        mobile: user.mobile,
        countryCode: user.countryCode,
        userLocation: user.userLocation,
        birthdayYear: birthdayYear ?? user.birthdayYear,
        selectedInterests: user.selectedInterests,
        gender: gender ?? user.gender,
        nationality: nationality ?? user.nationality,
      );

      return await _userRepository.updateUser(updatedUser);
    } catch (e) {
      if (kDebugMode) {
        print('UserProfileUseCase: Error updating personal info - $e');
      }
      return false;
    }
  }
}

/// Abstract User Profile Use Case Interface
///
/// Defines the contract for user profile business logic
abstract class UserProfileUseCase {
  Future<bool> updateUserInterests(
      String userId, Map<String, List<String>> interests);
  Future<bool> updateUserLocation(String userId, UserLocation location);
  Future<bool> updatePersonalInfo(
    String userId, {
    String? birthdayYear,
    String? gender,
    String? nationality,
  });
}
