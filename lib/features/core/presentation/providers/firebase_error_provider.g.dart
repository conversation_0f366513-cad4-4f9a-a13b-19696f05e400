// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_error_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentErrorTypeHash() => r'8034a9fad1452483c49e4bfdd43a1a54f8d35ef8';

/// Provider for current error type
///
/// Copied from [currentErrorType].
@ProviderFor(currentErrorType)
final currentErrorTypeProvider =
    AutoDisposeProvider<FirebaseErrorType?>.internal(
  currentErrorType,
  name: r'currentErrorTypeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentErrorTypeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentErrorTypeRef = AutoDisposeProviderRef<FirebaseErrorType?>;
String _$currentErrorMessageHash() =>
    r'85214dda52fd8511ccf375ea24f24bb6b601698e';

/// Provider for current error message
///
/// Copied from [currentErrorMessage].
@ProviderFor(currentErrorMessage)
final currentErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  currentErrorMessage,
  name: r'currentErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$currentErrorCodeHash() => r'b3f0568a5336811b2eb24092029023c65fafea44';

/// Provider for current error code
///
/// Copied from [currentErrorCode].
@ProviderFor(currentErrorCode)
final currentErrorCodeProvider = AutoDisposeProvider<String?>.internal(
  currentErrorCode,
  name: r'currentErrorCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentErrorCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentErrorCodeRef = AutoDisposeProviderRef<String?>;
String _$hasFirebaseErrorHash() => r'7d2cdb1fdacb5413f1310edc04b61b7d61ca8cea';

/// Provider for checking if there's an active error
///
/// Copied from [hasFirebaseError].
@ProviderFor(hasFirebaseError)
final hasFirebaseErrorProvider = AutoDisposeProvider<bool>.internal(
  hasFirebaseError,
  name: r'hasFirebaseErrorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasFirebaseErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasFirebaseErrorRef = AutoDisposeProviderRef<bool>;
String _$isHandlingFirebaseErrorHash() =>
    r'b20bb4444290b2ef0392f338bf5ffa1f52f2af8e';

/// Provider for error handling state
///
/// Copied from [isHandlingFirebaseError].
@ProviderFor(isHandlingFirebaseError)
final isHandlingFirebaseErrorProvider = AutoDisposeProvider<bool>.internal(
  isHandlingFirebaseError,
  name: r'isHandlingFirebaseErrorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isHandlingFirebaseErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsHandlingFirebaseErrorRef = AutoDisposeProviderRef<bool>;
String _$userFriendlyErrorMessageHash() =>
    r'93632b5c5f877a87e51dc64d018ea938a9c58734';

/// Provider for user-friendly error message
///
/// Copied from [userFriendlyErrorMessage].
@ProviderFor(userFriendlyErrorMessage)
final userFriendlyErrorMessageProvider = AutoDisposeProvider<String>.internal(
  userFriendlyErrorMessage,
  name: r'userFriendlyErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userFriendlyErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserFriendlyErrorMessageRef = AutoDisposeProviderRef<String>;
String _$isErrorRetryableHash() => r'c966e102c7977bd7cb5a965fb2b5c5346cd8c842';

/// Provider for checking if error is retryable
///
/// Copied from [isErrorRetryable].
@ProviderFor(isErrorRetryable)
final isErrorRetryableProvider = AutoDisposeProvider<bool>.internal(
  isErrorRetryable,
  name: r'isErrorRetryableProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isErrorRetryableHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsErrorRetryableRef = AutoDisposeProviderRef<bool>;
String _$errorSeverityHash() => r'a57e84cc907cc7e3fe4ea6cf1ffba9c66560be0d';

/// Provider for error severity
///
/// Copied from [errorSeverity].
@ProviderFor(errorSeverity)
final errorSeverityProvider = AutoDisposeProvider<String>.internal(
  errorSeverity,
  name: r'errorSeverityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$errorSeverityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ErrorSeverityRef = AutoDisposeProviderRef<String>;
String _$firebaseErrorHash() => r'9eb8b7954569f369a03427d28000ef954ec85dfa';

/// Firebase Error Notifier
///
/// Manages Firebase error state and provides error handling utilities
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [FirebaseError].
@ProviderFor(FirebaseError)
final firebaseErrorProvider =
    AutoDisposeNotifierProvider<FirebaseError, FirebaseErrorState>.internal(
  FirebaseError.new,
  name: r'firebaseErrorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FirebaseError = AutoDisposeNotifier<FirebaseErrorState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
