// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userIdHash() => r'dcdd20961ed9f8f962b6f9a6c31d36f460889d30';

/// Provider for current user ID
///
/// Copied from [userId].
@ProviderFor(userId)
final userIdProvider = AutoDisposeProvider<String>.internal(
  userId,
  name: r'userIdProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userIdHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserIdRef = AutoDisposeProviderRef<String>;
String _$userModelHash() => r'4a9fda003905567f457b0c3c49b5d817a6227272';

/// Provider for current user model
///
/// Copied from [userModel].
@ProviderFor(userModel)
final userModelProvider = AutoDisposeProvider<UserModel>.internal(
  userModel,
  name: r'userModelProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserModelRef = AutoDisposeProviderRef<UserModel>;
String _$isUserLoggedInHash() => r'eab07a91ee963d4f005dd3861302bcc50b9b63b4';

/// Provider for checking if user is logged in
///
/// Copied from [isUserLoggedIn].
@ProviderFor(isUserLoggedIn)
final isUserLoggedInProvider = AutoDisposeProvider<bool>.internal(
  isUserLoggedIn,
  name: r'isUserLoggedInProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isUserLoggedInHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsUserLoggedInRef = AutoDisposeProviderRef<bool>;
String _$hasCompleteProfileHash() =>
    r'98b3f035919fba46d2000d2fbede6a7e484f90a3';

/// Provider for checking if user has complete profile
///
/// Copied from [hasCompleteProfile].
@ProviderFor(hasCompleteProfile)
final hasCompleteProfileProvider = AutoDisposeProvider<bool>.internal(
  hasCompleteProfile,
  name: r'hasCompleteProfileProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasCompleteProfileHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasCompleteProfileRef = AutoDisposeProviderRef<bool>;
String _$appStateHash() => r'93e8bbf38035394c0eb6a0ce94bf95139376cd7a';

/// App State Notifier
///
/// Manages global application state using Riverpod
///
/// Copied from [AppState].
@ProviderFor(AppState)
final appStateProvider =
    AutoDisposeNotifierProvider<AppState, AsyncValue<AppStateEntity>>.internal(
  AppState.new,
  name: r'appStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$appStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppState = AutoDisposeNotifier<AsyncValue<AppStateEntity>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
