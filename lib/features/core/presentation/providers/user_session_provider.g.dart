// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_session_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionStatusHash() => r'a31049a69ffc7fcdee569c3e4be132960a0f90f2';

/// Provider for session status
///
/// Copied from [sessionStatus].
@ProviderFor(sessionStatus)
final sessionStatusProvider = AutoDisposeProvider<SessionStatus>.internal(
  sessionStatus,
  name: r'sessionStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SessionStatusRef = AutoDisposeProviderRef<SessionStatus>;
String _$isSessionActiveHash() => r'97ba3609cd057a55e50d3e56d1fc2619453163ea';

/// Provider for checking if session is active
///
/// Copied from [isSessionActive].
@ProviderFor(isSessionActive)
final isSessionActiveProvider = AutoDisposeProvider<bool>.internal(
  isSessionActive,
  name: r'isSessionActiveProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSessionActiveHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsSessionActiveRef = AutoDisposeProviderRef<bool>;
String _$isSessionExpiredHash() => r'c65be766ffda8564605a4eb0875b7c023e1d3f09';

/// Provider for checking if session is expired
///
/// Copied from [isSessionExpired].
@ProviderFor(isSessionExpired)
final isSessionExpiredProvider = AutoDisposeProvider<bool>.internal(
  isSessionExpired,
  name: r'isSessionExpiredProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSessionExpiredHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsSessionExpiredRef = AutoDisposeProviderRef<bool>;
String _$sessionTokenHash() => r'4b539f307fcd1fccb04f2f22e16dbe9fe8bb89dd';

/// Provider for session token
///
/// Copied from [sessionToken].
@ProviderFor(sessionToken)
final sessionTokenProvider = AutoDisposeProvider<String?>.internal(
  sessionToken,
  name: r'sessionTokenProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$sessionTokenHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SessionTokenRef = AutoDisposeProviderRef<String?>;
String _$isSessionValidatingHash() =>
    r'e905b1a5d8a542378451f413c0b3d19add80c3ed';

/// Provider for session validation state
///
/// Copied from [isSessionValidating].
@ProviderFor(isSessionValidating)
final isSessionValidatingProvider = AutoDisposeProvider<bool>.internal(
  isSessionValidating,
  name: r'isSessionValidatingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSessionValidatingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsSessionValidatingRef = AutoDisposeProviderRef<bool>;
String _$isSessionRefreshingHash() =>
    r'9cec3de24c3f11875595044c8092676c83a611db';

/// Provider for session refresh state
///
/// Copied from [isSessionRefreshing].
@ProviderFor(isSessionRefreshing)
final isSessionRefreshingProvider = AutoDisposeProvider<bool>.internal(
  isSessionRefreshing,
  name: r'isSessionRefreshingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSessionRefreshingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsSessionRefreshingRef = AutoDisposeProviderRef<bool>;
String _$willSessionExpireSoonHash() =>
    r'671d251c759608362163eadebc503024d59ea933';

/// Provider for checking if session will expire soon
///
/// Copied from [willSessionExpireSoon].
@ProviderFor(willSessionExpireSoon)
final willSessionExpireSoonProvider = AutoDisposeProvider<bool>.internal(
  willSessionExpireSoon,
  name: r'willSessionExpireSoonProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$willSessionExpireSoonHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WillSessionExpireSoonRef = AutoDisposeProviderRef<bool>;
String _$sessionErrorMessageHash() =>
    r'c2cc19d477c87d5a3c572b388fb79a241deca416';

/// Provider for session error message
///
/// Copied from [sessionErrorMessage].
@ProviderFor(sessionErrorMessage)
final sessionErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  sessionErrorMessage,
  name: r'sessionErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SessionErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$userSessionHash() => r'cf156e3ff08f9dcb8ac849edaeb500bd66995883';

/// User Session Notifier
///
/// Manages user session state and handles session lifecycle
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [UserSession].
@ProviderFor(UserSession)
final userSessionProvider =
    AutoDisposeNotifierProvider<UserSession, UserSessionState>.internal(
  UserSession.new,
  name: r'userSessionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userSessionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserSession = AutoDisposeNotifier<UserSessionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
