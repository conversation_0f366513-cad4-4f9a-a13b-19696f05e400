/// Firebase Error Repository Implementation
///
/// Implements the Firebase error repository interface using services
/// Part of the data layer in Clean Architecture
library firebase_error_repository_impl;

import 'package:towasl/features/core/domain/entities/firebase_error_entity.dart';
import 'package:towasl/features/core/domain/repositories/firebase_error_repository.dart';
import 'package:towasl/core/services/firebase_error_service.dart';

/// Firebase Error Repository Implementation
///
/// Implements Firebase error operations using the FirebaseErrorService
class FirebaseErrorRepositoryImpl implements FirebaseErrorRepository {
  final FirebaseErrorService _firebaseErrorService;

  /// Constructor
  ///
  /// @param firebaseErrorService The Firebase error service
  FirebaseErrorRepositoryImpl(this._firebaseErrorService);

  @override
  Future<FirebaseErrorEntity> getFirebaseErrorState() async {
    return FirebaseErrorEntity(
      isPermissionDenied: _firebaseErrorService.isPermissionDenied,
    );
  }

  @override
  Future<void> updateFirebaseErrorState(FirebaseErrorEntity errorState) async {
    // The service manages its own state
    // This method is here for interface compliance
  }

  @override
  Future<bool> isPermissionDenied() async {
    return _firebaseErrorService.isPermissionDenied;
  }

  @override
  Future<void> setPermissionDenied(bool isDenied) async {
    // The service manages permission state internally
    // This would be set when handling permission errors
  }

  @override
  Future<void> resetPermissionDenied() async {
    _firebaseErrorService.resetPermissionDenied();
  }

  @override
  Future<void> handleFirebaseError(dynamic error) async {
    _firebaseErrorService.handleFirebaseError(error);
  }

  @override
  Future<void> handleFirebaseException(dynamic exception) async {
    _firebaseErrorService.handleFirebaseError(exception);
  }

  @override
  Future<void> handlePermissionDenied(dynamic exception) async {
    _firebaseErrorService.handleFirebaseError(exception);
  }

  @override
  Future<void> handleServiceUnavailable(dynamic exception) async {
    _firebaseErrorService.handleFirebaseError(exception);
  }

  @override
  Future<void> handleTimeout(dynamic exception) async {
    _firebaseErrorService.handleFirebaseError(exception);
  }

  @override
  Future<void> handleResourceExhausted(dynamic exception) async {
    _firebaseErrorService.handleFirebaseError(exception);
  }

  @override
  Future<void> handleUnknownFirebaseError(dynamic exception) async {
    _firebaseErrorService.handleFirebaseError(exception);
  }

  @override
  Future<void> handleGenericError(dynamic error) async {
    _firebaseErrorService.handleFirebaseError(error);
  }

  @override
  Future<void> initializeErrorHandling() async {
    _firebaseErrorService.initialize();
  }

  @override
  Future<void> disposeErrorHandling() async {
    _firebaseErrorService.dispose();
  }

  @override
  Future<bool> isNetworkError(dynamic error) async {
    return _firebaseErrorService.isNetworkError(error);
  }

  @override
  Future<bool> isPermissionError(dynamic error) async {
    return _firebaseErrorService.isPermissionError(error);
  }

  @override
  Future<void> clearErrors() async {
    _firebaseErrorService.resetPermissionDenied();
  }

  @override
  Stream<FirebaseErrorEntity> get errorStateStream {
    // Create a periodic stream that emits the current error state
    return Stream.periodic(
        const Duration(seconds: 1),
        (_) => FirebaseErrorEntity(
              isPermissionDenied: _firebaseErrorService.isPermissionDenied,
            ));
  }

  @override
  Stream<bool> get permissionDeniedStream {
    // Create a periodic stream that emits the current permission denied state
    return Stream.periodic(const Duration(seconds: 1),
        (_) => _firebaseErrorService.isPermissionDenied);
  }
}
