/// App State Repository Implementation
///
/// Implements the app state repository interface using services
/// Part of the data layer in Clean Architecture
library app_state_repository_impl;

import 'package:towasl/features/core/domain/entities/app_state_entity.dart';
import 'package:towasl/features/core/domain/repositories/app_state_repository.dart';
import 'package:towasl/core/services/app_state_service.dart';
import 'package:towasl/shared/models/user_model.dart';

/// App State Repository Implementation
///
/// Implements app state operations using the AppStateService
class AppStateRepositoryImpl implements AppStateRepository {
  final AppStateService _appStateService;

  /// Constructor
  ///
  /// @param appStateService The app state service
  AppStateRepositoryImpl(this._appStateService);

  @override
  Future<AppStateEntity> getAppState() async {
    return AppStateEntity(
      userId: _appStateService.userId,
      userModel: _appStateService.userModel,
      msegatApikey: _appStateService.msegatApikey,
      msegatUserSender: _appStateService.msegatUserSender,
      msegatUsername: _appStateService.msegatUsername,
      providerName: _appStateService.providerName,
    );
  }

  @override
  Future<void> updateAppState(AppStateEntity appState) async {
    _appStateService.userId = appState.userId;
    _appStateService.userModel = appState.userModel;
    _appStateService.msegatUserSender = appState.msegatUserSender;
    _appStateService.msegatUsername = appState.msegatUsername;
    _appStateService.providerName = appState.providerName;
  }

  @override
  Future<String> getUserId() async {
    return _appStateService.userId;
  }

  @override
  Future<void> setUserId(String userId) async {
    _appStateService.userId = userId;
  }

  @override
  Future<UserModel> getUserModel() async {
    return _appStateService.userModel;
  }

  @override
  Future<void> setUserModel(UserModel userModel) async {
    _appStateService.userModel = userModel;
  }

  @override
  Future<String> getMsegatApikey() async {
    return _appStateService.msegatApikey;
  }

  @override
  Future<String> getMsegatUserSender() async {
    return _appStateService.msegatUserSender;
  }

  @override
  Future<void> setMsegatUserSender(String sender) async {
    _appStateService.msegatUserSender = sender;
  }

  @override
  Future<String> getMsegatUsername() async {
    return _appStateService.msegatUsername;
  }

  @override
  Future<void> setMsegatUsername(String username) async {
    _appStateService.msegatUsername = username;
  }

  @override
  Future<String> getProviderName() async {
    return _appStateService.providerName;
  }

  @override
  Future<void> setProviderName(String name) async {
    _appStateService.providerName = name;
  }

  @override
  Future<void> clearUserData() async {
    _appStateService.clearUserData();
  }

  @override
  Future<bool> isUserLoggedIn() async {
    return _appStateService.userId.isNotEmpty;
  }

  @override
  Future<void> loadUserData() async {
    // User data is automatically loaded by the service
    // This method is here for interface compliance
  }

  @override
  Future<void> saveUserData() async {
    // User data is automatically saved by the service
    // This method is here for interface compliance
  }

  @override
  Stream<AppStateEntity> get appStateStream {
    // Create a periodic stream that emits the current app state
    return Stream.periodic(
        const Duration(seconds: 1),
        (_) => AppStateEntity(
              userId: _appStateService.userId,
              userModel: _appStateService.userModel,
              msegatApikey: _appStateService.msegatApikey,
              msegatUserSender: _appStateService.msegatUserSender,
              msegatUsername: _appStateService.msegatUsername,
              providerName: _appStateService.providerName,
            ));
  }

  @override
  Stream<String> get userIdStream {
    // Create a periodic stream that emits the current user ID
    return Stream.periodic(
        const Duration(seconds: 1), (_) => _appStateService.userId);
  }

  @override
  Stream<UserModel> get userModelStream {
    // Create a periodic stream that emits the current user model
    return Stream.periodic(
        const Duration(seconds: 1), (_) => _appStateService.userModel);
  }
}
