/// App Lifecycle Repository Implementation
///
/// Implements the app lifecycle repository interface using services
/// Part of the data layer in Clean Architecture
library app_lifecycle_repository_impl;

import 'package:flutter/material.dart';
import 'package:towasl/features/core/domain/entities/app_lifecycle_entity.dart';
import 'package:towasl/features/core/domain/repositories/app_lifecycle_repository.dart';
import 'package:towasl/core/services/app_lifecycle_service.dart';

/// App Lifecycle Repository Implementation
///
/// Implements app lifecycle operations using the AppLifecycleService
class AppLifecycleRepositoryImpl implements AppLifecycleRepository {
  final AppLifecycleService _appLifecycleService;

  /// Constructor
  ///
  /// @param appLifecycleService The app lifecycle service
  AppLifecycleRepositoryImpl(this._appLifecycleService);

  @override
  Future<AppLifecycleEntity> getAppLifecycleState() async {
    return AppLifecycleEntity(
      isAppInForeground: _appLifecycleService.isAppInForeground,
      isConnected: _appLifecycleService.isConnected,
      lastActiveTime: _appLifecycleService.lastActiveTime,
    );
  }

  @override
  Future<void> updateAppLifecycleState(
      AppLifecycleEntity lifecycleState) async {
    // The service manages its own state, so we don't need to update it directly
    // This method is here for interface compliance
  }

  @override
  Future<bool> isAppInForeground() async {
    return _appLifecycleService.isAppInForeground;
  }

  @override
  Future<void> setAppForegroundState(bool isInForeground) async {
    // The service manages foreground state internally
    // This would be handled by the lifecycle state changes
  }

  @override
  Future<bool> isConnected() async {
    return _appLifecycleService.isConnected;
  }

  @override
  Future<void> setConnectivityState(bool isConnected) async {
    // The service manages connectivity state internally
    // This would be handled by the connectivity monitoring
  }

  @override
  Future<DateTime> getLastActiveTime() async {
    return _appLifecycleService.lastActiveTime;
  }

  @override
  Future<void> updateLastActiveTime(DateTime time) async {
    // The service manages last active time internally
    // This would be updated automatically on lifecycle changes
  }

  @override
  Future<void> initializeLifecycleMonitoring() async {
    _appLifecycleService.initialize();
  }

  @override
  Future<void> disposeLifecycleMonitoring() async {
    _appLifecycleService.dispose();
  }

  @override
  Future<void> handleAppLifecycleStateChange(AppLifecycleState state) async {
    _appLifecycleService.handleAppLifecycleStateChange(state);
  }

  @override
  Future<void> validateSessionOnResume() async {
    // Session validation logic would be implemented here
    // For now, this is a placeholder
  }

  @override
  Future<void> validateSessionOnReconnect() async {
    // Session validation logic would be implemented here
    // For now, this is a placeholder
  }

  @override
  Stream<AppLifecycleEntity> get lifecycleStateStream {
    // Create a periodic stream that emits the current lifecycle state
    return Stream.periodic(
        const Duration(seconds: 1),
        (_) => AppLifecycleEntity(
              isAppInForeground: _appLifecycleService.isAppInForeground,
              isConnected: _appLifecycleService.isConnected,
              lastActiveTime: _appLifecycleService.lastActiveTime,
            ));
  }

  @override
  Stream<bool> get foregroundStateStream {
    // Create a periodic stream that emits the current foreground state
    return Stream.periodic(const Duration(seconds: 1),
        (_) => _appLifecycleService.isAppInForeground);
  }

  @override
  Stream<bool> get connectivityStateStream {
    // Create a periodic stream that emits the current connectivity state
    return Stream.periodic(
        const Duration(seconds: 1), (_) => _appLifecycleService.isConnected);
  }

  @override
  Stream<DateTime> get lastActiveTimeStream {
    // Create a periodic stream that emits the current last active time
    return Stream.periodic(
        const Duration(seconds: 1), (_) => _appLifecycleService.lastActiveTime);
  }
}
