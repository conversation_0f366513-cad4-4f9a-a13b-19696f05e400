/// User Repository Implementation
///
/// Implements the UserRepository interface using Firebase services
/// Provides concrete implementation for user data operations
library user_repository_impl;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/profile/domain/repositories/user_repository.dart';
import 'package:towasl/shared/services/firebase_service.dart';
import 'package:towasl/shared/models/user_model.dart';

/// User Repository Implementation
///
/// Implements the UserRepository interface using FirebaseService
class UserRepositoryImpl implements UserRepository {
  final FirebaseService _firebaseService;

  /// Constructor that takes a FirebaseService instance
  ///
  /// @param firebaseService The Firebase service for database operations
  UserRepositoryImpl({
    required FirebaseService firebaseService,
  }) : _firebaseService = firebaseService;

  @override
  Future<UserModel?> getUserById(String userId) async {
    try {
      final snapshot = await _firebaseService.getDocument("users", userId);

      if (snapshot.exists) {
        final rawData = snapshot.data();
        if (kDebugMode) {
          print("UserRepository: Raw Firestore data for user $userId:");
          print(
              "  - Data keys: ${(rawData as Map<String, dynamic>?)?.keys.toList()}");
        }

        final userModel = UserModel.fromJson(rawData as Map<String, dynamic>);

        if (kDebugMode) {
          print("UserRepository: Parsed UserModel:");
          print("  - User ID: ${userModel.userId}");
          print("  - Mobile: ${userModel.mobile}");
          print("  - Gender: ${userModel.gender}");
          print("  - Nationality: ${userModel.nationality}");
          print("  - Birth year: ${userModel.birthdayYear}");
          print("  - Has interests: ${userModel.userInterest != null}");
          print("  - Has location: ${userModel.userLocation != null}");
        }

        return userModel;
      }
      return null;
    } catch (e) {
      if (kDebugMode) print("Error fetching user data: $e");
      rethrow;
    }
  }

  @override
  Future<void> createUser(String userId, Map<String, dynamic> userData) async {
    try {
      await _firebaseService.setDocument("users", userId, userData);
    } catch (e) {
      if (kDebugMode) print("Error creating user: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateUser(String userId, Map<String, dynamic> userData) async {
    try {
      await _firebaseService.updateDocument("users", userId, userData);
    } catch (e) {
      if (kDebugMode) print("Error updating user: $e");
      rethrow;
    }
  }

  @override
  Future<String?> getUserIdByMobile(String mobile) async {
    try {
      // First try to find user with mobile field
      var snapshot = await _firebaseService.firestore
          .collection("users")
          .where("mobile", isEqualTo: mobile)
          .limit(1)
          .get();

      // If no results, try legacy email field (for backward compatibility)
      if (snapshot.docs.isEmpty) {
        snapshot = await _firebaseService.firestore
            .collection("users")
            .where("email", isEqualTo: mobile)
            .limit(1)
            .get();
      }

      if (snapshot.docs.isNotEmpty) {
        var userData = snapshot.docs.first.data();
        return userData['user_id'];
      }
      return null;
    } catch (e) {
      if (kDebugMode) print("Error checking if user exists: $e");
      rethrow;
    }
  }

  @override
  Stream<bool> isUserBlocked(String userId) {
    if (kDebugMode) {
      print("UserRepository: Setting up blocking monitor for user: $userId");
    }

    // Monitor the specific user document for is_blocked field changes
    return _firebaseService.firestore
        .collection("users")
        .doc(userId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        final data = snapshot.data();
        final isBlocked = data?['is_blocked'] == true;

        if (kDebugMode) {
          print("UserRepository: User $userId blocking status: $isBlocked");
        }

        return isBlocked;
      }

      if (kDebugMode) {
        print("UserRepository: User document $userId does not exist");
      }

      return false;
    });
  }

  @override
  Future<void> updateFcmToken(String userId, String fcmToken) async {
    try {
      await _firebaseService.updateDocument("users", userId, {
        'fcm_token': fcmToken,
      });
    } catch (e) {
      if (kDebugMode) print("Error updating FCM token: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateSessionToken(String userId, String sessionToken) async {
    try {
      await _firebaseService.updateDocument("users", userId, {
        'session_token': sessionToken,
      });
    } catch (e) {
      if (kDebugMode) print("Error updating session token: $e");
      rethrow;
    }
  }

  @override
  Future<void> updatePersonalInfo(
      String userId, Map<String, dynamic> personalInfo) async {
    try {
      await _firebaseService.updateDocument("users", userId, personalInfo);
    } catch (e) {
      if (kDebugMode) print("Error updating personal info: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateUserInterests(
      String userId, Map<String, dynamic> interests) async {
    try {
      await _firebaseService.updateDocument("users", userId, interests);
    } catch (e) {
      if (kDebugMode) print("Error updating user interests: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateUserLocation(
      String userId, Map<String, dynamic> location) async {
    try {
      await _firebaseService.updateDocument("users", userId, location);
    } catch (e) {
      if (kDebugMode) print("Error updating user location: $e");
      rethrow;
    }
  }

  @override
  Future<void> deleteUser(String userId) async {
    try {
      await _firebaseService.deleteDocument("users", userId);
    } catch (e) {
      if (kDebugMode) print("Error deleting user: $e");
      rethrow;
    }
  }
}
