// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentUserModelHash() => r'0f731ea6a737fbe5f5006a407e0359fcd69a484e';

/// Provider for current user model
///
/// Copied from [currentUserModel].
@ProviderFor(currentUserModel)
final currentUserModelProvider = AutoDisposeProvider<UserModel?>.internal(
  currentUserModel,
  name: r'currentUserModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentUserModelRef = AutoDisposeProviderRef<UserModel?>;
String _$isUserLoadingHash() => r'4f1174fa526e4ee3fff22888ad2592116a62fd7c';

/// Provider for user loading state
///
/// Copied from [isUserLoading].
@ProviderFor(isUserLoading)
final isUserLoadingProvider = AutoDisposeProvider<bool>.internal(
  isUserLoading,
  name: r'isUserLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isUserLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsUserLoadingRef = AutoDisposeProviderRef<bool>;
String _$isUserSavingHash() => r'37813a08cfcda508f074473bec5750bdb606f200';

/// Provider for user saving state
///
/// Copied from [isUserSaving].
@ProviderFor(isUserSaving)
final isUserSavingProvider = AutoDisposeProvider<bool>.internal(
  isUserSaving,
  name: r'isUserSavingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isUserSavingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsUserSavingRef = AutoDisposeProviderRef<bool>;
String _$isUserUpdatingHash() => r'5ec23b05c945cd1ed56794faacb7359915e3921d';

/// Provider for user updating state
///
/// Copied from [isUserUpdating].
@ProviderFor(isUserUpdating)
final isUserUpdatingProvider = AutoDisposeProvider<bool>.internal(
  isUserUpdating,
  name: r'isUserUpdatingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isUserUpdatingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsUserUpdatingRef = AutoDisposeProviderRef<bool>;
String _$userErrorMessageHash() => r'b5d42daee81bf94f5f9243f704a7cedace5e18e9';

/// Provider for user error message
///
/// Copied from [userErrorMessage].
@ProviderFor(userErrorMessage)
final userErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  userErrorMessage,
  name: r'userErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$userHash() => r'8987a0b873db00314aacd0bd20fbc687bf07d694';

/// User Notifier
///
/// Manages user profile state and business logic
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [User].
@ProviderFor(User)
final userProvider = AutoDisposeNotifierProvider<User, UserState>.internal(
  User.new,
  name: r'userProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$User = AutoDisposeNotifier<UserState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
