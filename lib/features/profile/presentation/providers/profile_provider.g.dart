// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isProfileLoadingHash() => r'8e5717b8246d305bd761df666845238fe6ec73f6';

/// Convenience providers for profile state
///
/// Copied from [isProfileLoading].
@ProviderFor(isProfileLoading)
final isProfileLoadingProvider = AutoDisposeProvider<bool>.internal(
  isProfileLoading,
  name: r'isProfileLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isProfileLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsProfileLoadingRef = AutoDisposeProviderRef<bool>;
String _$isProfileLoggingOutHash() =>
    r'0af1f58c02600962978bb7f8df131ecb877069c8';

/// See also [isProfileLoggingOut].
@ProviderFor(isProfileLoggingOut)
final isProfileLoggingOutProvider = AutoDisposeProvider<bool>.internal(
  isProfileLoggingOut,
  name: r'isProfileLoggingOutProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isProfileLoggingOutHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsProfileLoggingOutRef = AutoDisposeProviderRef<bool>;
String _$currentProfileUserHash() =>
    r'df9f9f64f04e2dc68cf342a2a7d3c74cd625cf1b';

/// See also [currentProfileUser].
@ProviderFor(currentProfileUser)
final currentProfileUserProvider = AutoDisposeProvider<UserModel?>.internal(
  currentProfileUser,
  name: r'currentProfileUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentProfileUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentProfileUserRef = AutoDisposeProviderRef<UserModel?>;
String _$profileErrorMessageHash() =>
    r'4c9d7609d55d6331ec20ba239b2b097daee3120f';

/// See also [profileErrorMessage].
@ProviderFor(profileErrorMessage)
final profileErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  profileErrorMessage,
  name: r'profileErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ProfileErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$hasProfileInterestsHash() =>
    r'fd9f66b78aa66667312f7f811eb916efc14ef9fe';

/// Profile completion status providers
///
/// Copied from [hasProfileInterests].
@ProviderFor(hasProfileInterests)
final hasProfileInterestsProvider = AutoDisposeProvider<bool>.internal(
  hasProfileInterests,
  name: r'hasProfileInterestsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasProfileInterestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasProfileInterestsRef = AutoDisposeProviderRef<bool>;
String _$hasProfilePersonalInfoHash() =>
    r'5bc10f9fadaedbb2178b19619016f679df62dee7';

/// See also [hasProfilePersonalInfo].
@ProviderFor(hasProfilePersonalInfo)
final hasProfilePersonalInfoProvider = AutoDisposeProvider<bool>.internal(
  hasProfilePersonalInfo,
  name: r'hasProfilePersonalInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasProfilePersonalInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasProfilePersonalInfoRef = AutoDisposeProviderRef<bool>;
String _$hasProfileLocationHash() =>
    r'b852f19bea8dbdf75e3de5d05aeef439266ab96e';

/// See also [hasProfileLocation].
@ProviderFor(hasProfileLocation)
final hasProfileLocationProvider = AutoDisposeProvider<bool>.internal(
  hasProfileLocation,
  name: r'hasProfileLocationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasProfileLocationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasProfileLocationRef = AutoDisposeProviderRef<bool>;
String _$profileCompletionPercentageHash() =>
    r'6ef54628b154032fe5bccff8614d969ca7edc4cd';

/// See also [profileCompletionPercentage].
@ProviderFor(profileCompletionPercentage)
final profileCompletionPercentageProvider =
    AutoDisposeProvider<double>.internal(
  profileCompletionPercentage,
  name: r'profileCompletionPercentageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileCompletionPercentageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ProfileCompletionPercentageRef = AutoDisposeProviderRef<double>;
String _$profileHash() => r'829840d1264e89e6d6c94d31f8cf069f946516f1';

/// Profile Notifier
///
/// Manages profile state and business logic
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [Profile].
@ProviderFor(Profile)
final profileProvider =
    AutoDisposeNotifierProvider<Profile, ProfileState>.internal(
  Profile.new,
  name: r'profileProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$profileHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Profile = AutoDisposeNotifier<ProfileState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
