/// Profile Provider
///
/// Provides Riverpod state management for profile functionality
/// Manages profile viewing and sign out functionality
library profile_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/routes.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/shared/models/user_model.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_usecase_provider.dart';
import 'package:towasl/features/core/presentation/providers/use_case_provider.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

part 'profile_provider.g.dart';

/// Profile State
///
/// Represents the current state of the profile screen
class ProfileState {
  /// Whether profile is currently loading
  final bool isLoading;

  /// Whether user is currently logging out
  final bool isLoggingOut;

  /// Current user model
  final UserModel? userModel;

  /// Error message if any
  final String? errorMessage;

  /// Creates a ProfileState
  const ProfileState({
    this.isLoading = false,
    this.isLoggingOut = false,
    this.userModel,
    this.errorMessage,
  });

  /// Create a copy of this state with updated values
  ProfileState copyWith({
    bool? isLoading,
    bool? isLoggingOut,
    UserModel? userModel,
    String? errorMessage,
  }) {
    return ProfileState(
      isLoading: isLoading ?? this.isLoading,
      isLoggingOut: isLoggingOut ?? this.isLoggingOut,
      userModel: userModel ?? this.userModel,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProfileState &&
        other.isLoading == isLoading &&
        other.isLoggingOut == isLoggingOut &&
        other.userModel == userModel &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        isLoggingOut.hashCode ^
        userModel.hashCode ^
        errorMessage.hashCode;
  }

  @override
  String toString() {
    return 'ProfileState(isLoading: $isLoading, isLoggingOut: $isLoggingOut, '
        'userModel: $userModel, errorMessage: $errorMessage)';
  }
}

/// Profile Notifier
///
/// Manages profile state and business logic
/// Follows MVVM pattern with Riverpod state management
@riverpod
class Profile extends _$Profile {
  @override
  ProfileState build() {
    if (kDebugMode) {
      print('ProfileNotifier: Initialized');
    }
    return const ProfileState();
  }

  /// Load user profile data
  Future<void> loadUserProfile(String userId) async {
    if (kDebugMode) {
      print('ProfileNotifier: Loading user profile for userId: $userId');
    }

    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      // Get user repository
      final userRepository = ref.read(userRepositoryProvider);

      // Load user data
      final userModel = await userRepository.getUserById(userId);

      if (userModel != null) {
        state = state.copyWith(
          userModel: userModel,
          isLoading: false,
        );

        if (kDebugMode) {
          print('ProfileNotifier: User profile loaded successfully');
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'User not found',
        );

        if (kDebugMode) {
          print('ProfileNotifier: User not found');
        }
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load profile',
      );

      if (kDebugMode) {
        print('ProfileNotifier: Error loading profile - $e');
      }
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    if (kDebugMode) {
      print('ProfileNotifier: Starting sign out process');
    }

    try {
      state = state.copyWith(isLoggingOut: true);

      // Get login use case for sign out
      final loginUseCase = ref.read(loginUserUseCaseProvider);

      // Perform sign out
      final result = await loginUseCase.signOut();

      if (result.isSuccess) {
        // Clear app state using the correct method
        await ref.read(appStateProvider.notifier).clearUserData();

        // Clear app state use case data
        final appStateUseCase = ref.read(appStateUseCaseProvider);
        await appStateUseCase.clearUserData();

        if (kDebugMode) {
          print('ProfileNotifier: Sign out successful');
        }

        // Show success message
        ToastCustom.successToast('تم تسجيل الخروج بنجاح');

        // Navigate to welcome screen
        _navigateToWelcome();
      } else {
        state = state.copyWith(
          isLoggingOut: false,
          errorMessage: result.errorMessage ?? 'Sign out failed',
        );

        if (kDebugMode) {
          print('ProfileNotifier: Sign out failed - ${result.errorMessage}');
        }

        // Show error message
        ToastCustom.errorToast('فشل في تسجيل الخروج');
      }
    } catch (e) {
      state = state.copyWith(
        isLoggingOut: false,
        errorMessage: 'An error occurred during sign out',
      );

      if (kDebugMode) {
        print('ProfileNotifier: Sign out error - $e');
      }

      // Show error message
      ToastCustom.errorToast('حدث خطأ أثناء تسجيل الخروج');
    }
  }

  /// Navigate to welcome screen
  void _navigateToWelcome() {
    if (kDebugMode) {
      print('ProfileNotifier: Navigating to welcome screen');
    }

    // Use navigation provider for routing
    ref
        .read(navigationProvider.notifier)
        .navigateToAndClearAll(AppRoutes.welcome);
  }

  /// Refresh profile data
  Future<void> refreshProfile() async {
    final userId = ref.read(userIdProvider);
    if (userId.isNotEmpty) {
      await loadUserProfile(userId);
    }
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

/// Convenience providers for profile state
@riverpod
bool isProfileLoading(IsProfileLoadingRef ref) {
  return ref.watch(profileProvider).isLoading;
}

@riverpod
bool isProfileLoggingOut(IsProfileLoggingOutRef ref) {
  return ref.watch(profileProvider).isLoggingOut;
}

@riverpod
UserModel? currentProfileUser(CurrentProfileUserRef ref) {
  return ref.watch(profileProvider).userModel;
}

@riverpod
String? profileErrorMessage(ProfileErrorMessageRef ref) {
  return ref.watch(profileProvider).errorMessage;
}

/// Profile completion status providers
@riverpod
bool hasProfileInterests(HasProfileInterestsRef ref) {
  final user = ref.watch(currentProfileUserProvider);
  return user?.userInterest?.isNotEmpty ?? false;
}

@riverpod
bool hasProfilePersonalInfo(HasProfilePersonalInfoRef ref) {
  final user = ref.watch(currentProfileUserProvider);
  if (user == null) return false;

  return (user.birthdayYear?.isNotEmpty ?? false) &&
      (user.gender?.isNotEmpty ?? false) &&
      (user.nationality?.isNotEmpty ?? false);
}

@riverpod
bool hasProfileLocation(HasProfileLocationRef ref) {
  final user = ref.watch(currentProfileUserProvider);
  return user?.userLocation != null;
}

@riverpod
double profileCompletionPercentage(ProfileCompletionPercentageRef ref) {
  final user = ref.watch(currentProfileUserProvider);
  if (user == null) return 0.0;

  int completedFields = 0;
  const int totalFields = 3; // interests, personal info, location

  if (ref.watch(hasProfileInterestsProvider)) completedFields++;
  if (ref.watch(hasProfilePersonalInfoProvider)) completedFields++;
  if (ref.watch(hasProfileLocationProvider)) completedFields++;

  return completedFields / totalFields;
}
