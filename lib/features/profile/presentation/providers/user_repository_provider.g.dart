// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_repository_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userRepositoryHash() => r'bfdf5de13f3876ed66a1165c743619442a0e05f9';

/// User Repository Provider
///
/// Provides access to user data operations
///
/// Copied from [userRepository].
@ProviderFor(userRepository)
final userRepositoryProvider = AutoDisposeProvider<UserRepository>.internal(
  userRepository,
  name: r'userRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserRepositoryRef = AutoDisposeProviderRef<UserRepository>;
String _$hasInterestsHash() => r'8efba590b0df0def475263cfa94084efaf2ff5ae';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Convenience providers for profile completion
///
/// Copied from [hasInterests].
@ProviderFor(hasInterests)
const hasInterestsProvider = HasInterestsFamily();

/// Convenience providers for profile completion
///
/// Copied from [hasInterests].
class HasInterestsFamily extends Family<AsyncValue<bool>> {
  /// Convenience providers for profile completion
  ///
  /// Copied from [hasInterests].
  const HasInterestsFamily();

  /// Convenience providers for profile completion
  ///
  /// Copied from [hasInterests].
  HasInterestsProvider call(
    String userId,
  ) {
    return HasInterestsProvider(
      userId,
    );
  }

  @override
  HasInterestsProvider getProviderOverride(
    covariant HasInterestsProvider provider,
  ) {
    return call(
      provider.userId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'hasInterestsProvider';
}

/// Convenience providers for profile completion
///
/// Copied from [hasInterests].
class HasInterestsProvider extends AutoDisposeFutureProvider<bool> {
  /// Convenience providers for profile completion
  ///
  /// Copied from [hasInterests].
  HasInterestsProvider(
    String userId,
  ) : this._internal(
          (ref) => hasInterests(
            ref as HasInterestsRef,
            userId,
          ),
          from: hasInterestsProvider,
          name: r'hasInterestsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$hasInterestsHash,
          dependencies: HasInterestsFamily._dependencies,
          allTransitiveDependencies:
              HasInterestsFamily._allTransitiveDependencies,
          userId: userId,
        );

  HasInterestsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(HasInterestsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: HasInterestsProvider._internal(
        (ref) => create(ref as HasInterestsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _HasInterestsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is HasInterestsProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin HasInterestsRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _HasInterestsProviderElement
    extends AutoDisposeFutureProviderElement<bool> with HasInterestsRef {
  _HasInterestsProviderElement(super.provider);

  @override
  String get userId => (origin as HasInterestsProvider).userId;
}

String _$hasLocationHash() => r'f9dfd1e9ca8d84546cdece0b785680a22892702e';

/// See also [hasLocation].
@ProviderFor(hasLocation)
const hasLocationProvider = HasLocationFamily();

/// See also [hasLocation].
class HasLocationFamily extends Family<AsyncValue<bool>> {
  /// See also [hasLocation].
  const HasLocationFamily();

  /// See also [hasLocation].
  HasLocationProvider call(
    String userId,
  ) {
    return HasLocationProvider(
      userId,
    );
  }

  @override
  HasLocationProvider getProviderOverride(
    covariant HasLocationProvider provider,
  ) {
    return call(
      provider.userId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'hasLocationProvider';
}

/// See also [hasLocation].
class HasLocationProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [hasLocation].
  HasLocationProvider(
    String userId,
  ) : this._internal(
          (ref) => hasLocation(
            ref as HasLocationRef,
            userId,
          ),
          from: hasLocationProvider,
          name: r'hasLocationProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$hasLocationHash,
          dependencies: HasLocationFamily._dependencies,
          allTransitiveDependencies:
              HasLocationFamily._allTransitiveDependencies,
          userId: userId,
        );

  HasLocationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(HasLocationRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: HasLocationProvider._internal(
        (ref) => create(ref as HasLocationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _HasLocationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is HasLocationProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin HasLocationRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _HasLocationProviderElement extends AutoDisposeFutureProviderElement<bool>
    with HasLocationRef {
  _HasLocationProviderElement(super.provider);

  @override
  String get userId => (origin as HasLocationProvider).userId;
}

String _$hasPersonalInfoHash() => r'57c7778e2a838b3d6962cca0440f390e83982076';

/// See also [hasPersonalInfo].
@ProviderFor(hasPersonalInfo)
const hasPersonalInfoProvider = HasPersonalInfoFamily();

/// See also [hasPersonalInfo].
class HasPersonalInfoFamily extends Family<AsyncValue<bool>> {
  /// See also [hasPersonalInfo].
  const HasPersonalInfoFamily();

  /// See also [hasPersonalInfo].
  HasPersonalInfoProvider call(
    String userId,
  ) {
    return HasPersonalInfoProvider(
      userId,
    );
  }

  @override
  HasPersonalInfoProvider getProviderOverride(
    covariant HasPersonalInfoProvider provider,
  ) {
    return call(
      provider.userId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'hasPersonalInfoProvider';
}

/// See also [hasPersonalInfo].
class HasPersonalInfoProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [hasPersonalInfo].
  HasPersonalInfoProvider(
    String userId,
  ) : this._internal(
          (ref) => hasPersonalInfo(
            ref as HasPersonalInfoRef,
            userId,
          ),
          from: hasPersonalInfoProvider,
          name: r'hasPersonalInfoProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$hasPersonalInfoHash,
          dependencies: HasPersonalInfoFamily._dependencies,
          allTransitiveDependencies:
              HasPersonalInfoFamily._allTransitiveDependencies,
          userId: userId,
        );

  HasPersonalInfoProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(HasPersonalInfoRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: HasPersonalInfoProvider._internal(
        (ref) => create(ref as HasPersonalInfoRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _HasPersonalInfoProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is HasPersonalInfoProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin HasPersonalInfoRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _HasPersonalInfoProviderElement
    extends AutoDisposeFutureProviderElement<bool> with HasPersonalInfoRef {
  _HasPersonalInfoProviderElement(super.provider);

  @override
  String get userId => (origin as HasPersonalInfoProvider).userId;
}

String _$profileCheckerHash() => r'****************************************';

abstract class _$ProfileChecker
    extends BuildlessAutoDisposeAsyncNotifier<Map<String, bool>> {
  late final String userId;

  FutureOr<Map<String, bool>> build(
    String userId,
  );
}

/// Profile Completion Checker Provider
///
/// Provides methods to check profile completion status
///
/// Copied from [ProfileChecker].
@ProviderFor(ProfileChecker)
const profileCheckerProvider = ProfileCheckerFamily();

/// Profile Completion Checker Provider
///
/// Provides methods to check profile completion status
///
/// Copied from [ProfileChecker].
class ProfileCheckerFamily extends Family<AsyncValue<Map<String, bool>>> {
  /// Profile Completion Checker Provider
  ///
  /// Provides methods to check profile completion status
  ///
  /// Copied from [ProfileChecker].
  const ProfileCheckerFamily();

  /// Profile Completion Checker Provider
  ///
  /// Provides methods to check profile completion status
  ///
  /// Copied from [ProfileChecker].
  ProfileCheckerProvider call(
    String userId,
  ) {
    return ProfileCheckerProvider(
      userId,
    );
  }

  @override
  ProfileCheckerProvider getProviderOverride(
    covariant ProfileCheckerProvider provider,
  ) {
    return call(
      provider.userId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'profileCheckerProvider';
}

/// Profile Completion Checker Provider
///
/// Provides methods to check profile completion status
///
/// Copied from [ProfileChecker].
class ProfileCheckerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    ProfileChecker, Map<String, bool>> {
  /// Profile Completion Checker Provider
  ///
  /// Provides methods to check profile completion status
  ///
  /// Copied from [ProfileChecker].
  ProfileCheckerProvider(
    String userId,
  ) : this._internal(
          () => ProfileChecker()..userId = userId,
          from: profileCheckerProvider,
          name: r'profileCheckerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$profileCheckerHash,
          dependencies: ProfileCheckerFamily._dependencies,
          allTransitiveDependencies:
              ProfileCheckerFamily._allTransitiveDependencies,
          userId: userId,
        );

  ProfileCheckerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<Map<String, bool>> runNotifierBuild(
    covariant ProfileChecker notifier,
  ) {
    return notifier.build(
      userId,
    );
  }

  @override
  Override overrideWith(ProfileChecker Function() create) {
    return ProviderOverride(
      origin: this,
      override: ProfileCheckerProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ProfileChecker, Map<String, bool>>
      createElement() {
    return _ProfileCheckerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProfileCheckerProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProfileCheckerRef
    on AutoDisposeAsyncNotifierProviderRef<Map<String, bool>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _ProfileCheckerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ProfileChecker,
        Map<String, bool>> with ProfileCheckerRef {
  _ProfileCheckerProviderElement(super.provider);

  @override
  String get userId => (origin as ProfileCheckerProvider).userId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
