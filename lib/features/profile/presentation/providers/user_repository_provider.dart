/// User Repository Provider
///
/// Provides Riverpod providers for user data management
/// Integrates with existing user repository and provides profile checking
library user_repository_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/profile/data/repositories/user_repository_impl.dart'
    as data_repo;
import 'package:towasl/shared/models/user_model.dart';

part 'user_repository_provider.g.dart';

/// User Repository Provider
///
/// Provides access to user data operations
@riverpod
UserRepository userRepository(UserRepositoryRef ref) {
  // Use the real UserRepository implementation wrapped for compatibility
  final realRepo = data_repo.UserRepositoryImpl(
    firebaseService: ref.read(firebaseServiceProvider),
  );
  return UserRepositoryWrapper(realRepo);
}

/// User Repository Wrapper
///
/// Wraps the real UserRepository to provide the interface expected by the app
class UserRepositoryWrapper implements UserRepository {
  final data_repo.UserRepositoryImpl _realRepository;

  const UserRepositoryWrapper(this._realRepository);

  @override
  Future<UserModel?> getUserById(String userId) async {
    return await _realRepository.getUserById(userId);
  }

  @override
  Future<bool> saveUser(UserModel user) async {
    try {
      if (kDebugMode) {
        print('UserRepository: Saving user: ${user.userId}');
      }

      // Convert UserModel to Map and save using createUser
      await _realRepository.createUser(user.userId!, user.toJson());

      if (kDebugMode) {
        print('UserRepository: User saved successfully to Firestore');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('UserRepository: Error saving user - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> updateUser(UserModel user) async {
    try {
      if (kDebugMode) {
        print('UserRepository: Updating user: ${user.userId}');
      }

      // Convert UserModel to Map and update using updateUser
      await _realRepository.updateUser(user.userId!, user.toJson());

      if (kDebugMode) {
        print('UserRepository: User updated successfully in Firestore');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('UserRepository: Error updating user - $e');
      }
      return false;
    }
  }

  @override
  Future<bool> deleteUser(String userId) async {
    try {
      if (kDebugMode) {
        print('UserRepository: Deleting user: $userId');
      }

      // Delete user using real repository
      await _realRepository.deleteUser(userId);

      if (kDebugMode) {
        print('UserRepository: User deleted successfully from Firestore');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('UserRepository: Error deleting user - $e');
      }
      return false;
    }
  }
}

/// User Repository Interface for App State Use Case
///
/// Defines the contract for user data operations expected by the app state use case
abstract class UserRepository {
  Future<UserModel?> getUserById(String userId);
  Future<bool> saveUser(UserModel user);
  Future<bool> updateUser(UserModel user);
  Future<bool> deleteUser(String userId);
}

/// Profile Completion Checker Provider
///
/// Provides methods to check profile completion status
@riverpod
class ProfileChecker extends _$ProfileChecker {
  @override
  Future<Map<String, bool>> build(String userId) async {
    final userRepo = ref.read(userRepositoryProvider);
    final user = await userRepo.getUserById(userId);

    if (user == null) {
      return {
        'hasInterests': false,
        'hasLocation': false,
        'hasPersonalInfo': false,
      };
    }

    return {
      'hasInterests': _hasInterests(user),
      'hasLocation': _hasLocation(user),
      'hasPersonalInfo': _hasPersonalInfo(user),
    };
  }

  /// Check if user has interests
  bool _hasInterests(UserModel user) {
    return user.userInterest != null && user.userInterest!.isNotEmpty;
  }

  /// Check if user has location
  bool _hasLocation(UserModel user) {
    return user.userLocation != null &&
        user.userLocation!.city != null &&
        user.userLocation!.city!.isNotEmpty;
  }

  /// Check if user has personal info
  /// Note: name is not required for profile completion
  bool _hasPersonalInfo(UserModel user) {
    return user.birthdayYear != null &&
        user.birthdayYear!.isNotEmpty &&
        user.gender != null &&
        user.gender!.isNotEmpty &&
        user.nationality != null &&
        user.nationality!.isNotEmpty;
  }

  /// Refresh profile completion status
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// Convenience providers for profile completion
@riverpod
Future<bool> hasInterests(HasInterestsRef ref, String userId) async {
  final profileStatus = await ref.watch(profileCheckerProvider(userId).future);
  return profileStatus['hasInterests'] ?? false;
}

@riverpod
Future<bool> hasLocation(HasLocationRef ref, String userId) async {
  final profileStatus = await ref.watch(profileCheckerProvider(userId).future);
  return profileStatus['hasLocation'] ?? false;
}

@riverpod
Future<bool> hasPersonalInfo(HasPersonalInfoRef ref, String userId) async {
  final profileStatus = await ref.watch(profileCheckerProvider(userId).future);
  return profileStatus['hasPersonalInfo'] ?? false;
}
