/// User Data Helpers
///
/// Utilities for manipulating and processing user profile data
/// Includes age calculation, profile completion checks, and data formatting
library user_data_helpers;

import 'package:towasl/shared/models/user_model.dart';

/// User data manipulation utilities
class UserDataHelpers {
  /// Calculate user's age from birth year
  ///
  /// @param birthYear The birth year string
  /// @return The calculated age, or 0 if invalid
  static int calculateAge(String? birthYear) {
    if (birthYear == null || birthYear.isEmpty) return 0;

    try {
      final year = int.parse(birthYear);
      final currentYear = DateTime.now().year;
      return currentYear - year;
    } catch (e) {
      return 0;
    }
  }

  /// Get user's age from UserModel
  ///
  /// @param user The user model
  /// @return The user's age
  static int getUserAge(UserModel user) {
    return calculateAge(user.birthdayYear);
  }

  /// Check if user profile is complete
  ///
  /// @param user The user model to check
  /// @return True if all required fields are filled
  /// Note: name is not required for profile completion during onboarding
  static bool isProfileComplete(UserModel user) {
    return (user.gender ?? "").isNotEmpty &&
        (user.birthdayYear ?? "").isNotEmpty &&
        (user.nationality ?? "").isNotEmpty &&
        user.userLocation != null &&
        user.userInterest != null &&
        (user.userInterest?.isNotEmpty ?? false);
  }

  /// Get profile completion percentage
  ///
  /// @param user The user model to check
  /// @return Completion percentage (0.0 to 1.0)
  static double getProfileCompletionPercentage(UserModel user) {
    int completedFields = 0;
    const int totalFields =
        5; // gender, birthYear, nationality, location, interests (name not required)

    if ((user.gender ?? "").isNotEmpty) completedFields++;
    if ((user.birthdayYear ?? "").isNotEmpty) completedFields++;
    if ((user.nationality ?? "").isNotEmpty) completedFields++;
    if (user.userLocation != null) completedFields++;
    if (user.userInterest != null && (user.userInterest?.isNotEmpty ?? false)) {
      completedFields++;
    }

    return completedFields / totalFields;
  }

  /// Format user's age display
  ///
  /// @param user The user model
  /// @return The formatted age string
  static String getAgeDisplay(UserModel user) {
    final age = getUserAge(user);
    if (age == 0) return 'Age not set';
    return '$age years old';
  }

  /// Get gender display text
  ///
  /// @param user The user model
  /// @return The localized gender display text
  static String getGenderDisplay(UserModel user) {
    final gender = user.gender?.toLowerCase() ?? '';
    switch (gender) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      default:
        return 'Not specified';
    }
  }

  /// Check if user is of legal age
  ///
  /// @param user The user model
  /// @return True if user is 18 or older
  static bool isLegalAge(UserModel user) {
    final age = getUserAge(user);
    return age >= 18;
  }

  /// Get age range for matching
  ///
  /// Returns a list of birth years for users within the specified age range
  ///
  /// @param user The current user
  /// @param minAgeDiff Minimum age difference (default: -5)
  /// @param maxAgeDiff Maximum age difference (default: +5)
  /// @return List of birth years for matching
  static List<String> getMatchingAgeRange(UserModel user,
      {int minAgeDiff = -5, int maxAgeDiff = 5}) {
    final userAge = getUserAge(user);
    if (userAge == 0) return [];

    final currentYear = DateTime.now().year;

    final minAge = userAge + minAgeDiff;
    final maxAge = userAge + maxAgeDiff;

    // Ensure age limits are reasonable
    final clampedMinAge = minAge < 18 ? 18 : minAge;
    final clampedMaxAge = maxAge > 120 ? 120 : maxAge;

    final List<String> birthYears = [];
    for (int age = clampedMinAge; age <= clampedMaxAge; age++) {
      birthYears.add((currentYear - age).toString());
    }

    return birthYears;
  }

  /// Validate birth year
  ///
  /// @param birthYear The birth year to validate
  /// @return True if the birth year is valid
  static bool isValidBirthYear(String? birthYear) {
    if (birthYear == null || birthYear.isEmpty) return false;

    try {
      final year = int.parse(birthYear);
      final currentYear = DateTime.now().year;
      final age = currentYear - year;

      return age >= 18 && age <= 120;
    } catch (e) {
      return false;
    }
  }

  /// Get missing profile fields
  ///
  /// @param user The user model to check
  /// @return List of missing field names
  /// Note: name is not required for profile completion during onboarding
  static List<String> getMissingFields(UserModel user) {
    final List<String> missingFields = [];

    // Note: name is not included as it's not required for profile completion
    if ((user.gender ?? "").isEmpty) missingFields.add('Gender');
    if ((user.birthdayYear ?? "").isEmpty) missingFields.add('Birth Year');
    if ((user.nationality ?? "").isEmpty) missingFields.add('Nationality');
    if (user.userLocation == null) missingFields.add('Location');
    if (user.userInterest == null || (user.userInterest?.isEmpty ?? true)) {
      missingFields.add('Interests');
    }

    return missingFields;
  }

  /// Create a summary of user profile
  ///
  /// @param user The user model
  /// @return A formatted profile summary
  static String getProfileSummary(UserModel user) {
    final age = getUserAge(user);
    final gender = getGenderDisplay(user);
    final nationality = user.nationality ?? 'Not specified';

    return '$age, $gender, $nationality';
  }

  /// Check if two users are compatible for matching
  ///
  /// Basic compatibility check based on age and location
  ///
  /// @param user1 The first user
  /// @param user2 The second user
  /// @return True if users are potentially compatible
  static bool areUsersCompatible(UserModel user1, UserModel user2) {
    // Check if both users have complete profiles
    if (!isProfileComplete(user1) || !isProfileComplete(user2)) {
      return false;
    }

    // Check age compatibility (within 10 years)
    final age1 = getUserAge(user1);
    final age2 = getUserAge(user2);
    final ageDifference = (age1 - age2).abs();

    if (ageDifference > 10) return false;

    // Check if they're in the same city (if location data is available)
    if (user1.userLocation?.city != null && user2.userLocation?.city != null) {
      return user1.userLocation!.city == user2.userLocation!.city;
    }

    return true;
  }
}
