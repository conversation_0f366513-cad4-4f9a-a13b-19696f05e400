/// User Domain Entity
///
/// Pure business object representing user profile information
/// Contains no external dependencies and represents core business logic
///
/// This entity defines the structure for user data in the domain layer
library user_entity;

/// User Entity
///
/// Represents a user in the application with all their profile information
/// Contains personal information, location, and interests
class UserEntity {
  /// User's gender (e.g., "male", "female")
  final String gender;

  /// User's nationality
  final String nationality;

  /// Unique user identifier
  final String userId;

  /// User's mobile number (primary identifier)
  final String mobile;

  /// User's phone number
  final String phoneNumber;

  /// Country code for phone number (e.g., "966" for Saudi Arabia)
  final String countryCode;

  /// User's location information
  final UserLocationEntity? userLocation;

  /// User's birth year (used for age calculation)
  final String birthdayYear;

  /// User's interests organized by categories
  final Map<String, List<String>> userInterest;

  /// Creates a UserEntity
  ///
  /// @param gender User's gender
  /// @param nationality User's nationality
  /// @param userId Unique user identifier
  /// @param mobile User's mobile number
  /// @param phoneNumber User's phone number
  /// @param countryCode Country code for phone number
  /// @param userLocation User's location information
  /// @param birthdayYear User's birth year
  /// @param userInterest User's interests organized by categories
  const UserEntity({
    required this.gender,
    required this.nationality,
    required this.userId,
    required this.mobile,
    required this.phoneNumber,
    required this.countryCode,
    this.userLocation,
    required this.birthdayYear,
    required this.userInterest,
  });

  /// Creates a copy of this entity with updated values
  ///
  /// @param gender New gender (optional)
  /// @param nationality New nationality (optional)
  /// @param userId New user ID (optional)
  /// @param mobile New mobile number (optional)
  /// @param phoneNumber New phone number (optional)
  /// @param countryCode New country code (optional)
  /// @param userLocation New location (optional)
  /// @param birthdayYear New birth year (optional)
  /// @param userInterest New interests (optional)
  /// @return New UserEntity instance with updated values
  UserEntity copyWith({
    String? gender,
    String? nationality,
    String? userId,
    String? mobile,
    String? phoneNumber,
    String? countryCode,
    UserLocationEntity? userLocation,
    String? birthdayYear,
    Map<String, List<String>>? userInterest,
  }) {
    return UserEntity(
      gender: gender ?? this.gender,
      nationality: nationality ?? this.nationality,
      userId: userId ?? this.userId,
      mobile: mobile ?? this.mobile,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
      userLocation: userLocation ?? this.userLocation,
      birthdayYear: birthdayYear ?? this.birthdayYear,
      userInterest: userInterest ?? this.userInterest,
    );
  }

  /// Gets the user's age based on birth year
  ///
  /// @return User's age as an integer, or 0 if birth year is invalid
  int get age {
    try {
      final currentYear = DateTime.now().year;
      return currentYear - int.parse(birthdayYear);
    } catch (e) {
      return 0;
    }
  }

  /// Checks if the user has a valid location
  ///
  /// @return True if location exists and has valid coordinates
  bool get hasValidLocation =>
      userLocation != null && userLocation!.hasValidCoordinates;

  /// Checks if the user has interests
  ///
  /// @return True if user has any interests
  bool get hasInterests => userInterest.isNotEmpty;

  /// Gets the total number of interests
  ///
  /// @return Total count of all interests across categories
  int get totalInterestCount {
    return userInterest.values
        .fold(0, (sum, interests) => sum + interests.length);
  }

  /// Checks if the user profile is complete
  ///
  /// @return True if all required fields are filled
  bool get isProfileComplete {
    return gender.isNotEmpty &&
        birthdayYear.isNotEmpty &&
        nationality.isNotEmpty &&
        hasValidLocation &&
        hasInterests;
  }

  /// Gets profile completion percentage
  ///
  /// @return Completion percentage (0.0 to 1.0)
  double get profileCompletionPercentage {
    int completedFields = 0;
    const int totalFields =
        6; // name, gender, birthYear, nationality, location, interests

    if (gender.isNotEmpty) completedFields++;
    if (birthdayYear.isNotEmpty) completedFields++;
    if (nationality.isNotEmpty) completedFields++;
    if (hasValidLocation) completedFields++;
    if (hasInterests) completedFields++;

    return completedFields / totalFields;
  }

  /// Gets missing profile fields
  ///
  /// @return List of missing field names
  List<String> get missingFields {
    final List<String> missingFields = [];

    if (gender.isEmpty) missingFields.add('Gender');
    if (birthdayYear.isEmpty) missingFields.add('Birth Year');
    if (nationality.isEmpty) missingFields.add('Nationality');
    if (!hasValidLocation) missingFields.add('Location');
    if (!hasInterests) missingFields.add('Interests');

    return missingFields;
  }

  /// Checks if the user is in the same city as another user
  ///
  /// @param other The other user to compare with
  /// @return True if both users are in the same city
  bool isSameCityAs(UserEntity other) {
    if (userLocation == null || other.userLocation == null) return false;
    return userLocation!.isSameCityAs(other.userLocation!);
  }

  /// Gets interests for a specific category
  ///
  /// @param category The interest category to get
  /// @return List of interests in the category, or empty list if not found
  List<String> getInterestsForCategory(String category) {
    return userInterest[category] ?? [];
  }

  /// Checks if the user has a specific interest
  ///
  /// @param category The interest category
  /// @param interest The specific interest to check
  /// @return True if the user has this interest
  bool hasInterest(String category, String interest) {
    final categoryInterests = userInterest[category];
    return categoryInterests?.contains(interest) ?? false;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserEntity &&
        other.userId == userId &&
        other.mobile == mobile;
  }

  @override
  int get hashCode {
    return userId.hashCode ^ mobile.hashCode;
  }

  @override
  String toString() {
    return 'UserEntity(userId: $userId, '
        'age: $age, profileComplete: $isProfileComplete)';
  }
}

/// User Location Entity
///
/// Represents geographical information about a user's location
class UserLocationEntity {
  /// Country name
  final String country;

  /// Longitude coordinate
  final double longitude;

  /// City name
  final String city;

  /// Latitude coordinate
  final double latitude;

  /// District or neighborhood name
  final String district;

  /// Creates a UserLocationEntity
  ///
  /// @param country Country name
  /// @param longitude Longitude coordinate
  /// @param city City name
  /// @param latitude Latitude coordinate
  /// @param district District name
  const UserLocationEntity({
    required this.country,
    required this.longitude,
    required this.city,
    required this.latitude,
    required this.district,
  });

  /// Checks if the location has valid coordinates
  ///
  /// @return True if coordinates are valid (not zero)
  bool get hasValidCoordinates => latitude != 0.0 && longitude != 0.0;

  /// Gets the full address as a formatted string
  ///
  /// @return Formatted address string
  String get fullAddress {
    final parts = <String>[];
    if (district.isNotEmpty) parts.add(district);
    if (city.isNotEmpty) parts.add(city);
    if (country.isNotEmpty) parts.add(country);
    return parts.join(', ');
  }

  /// Checks if this location is in the same city as another location
  ///
  /// @param other The other location to compare with
  /// @return True if both locations are in the same city and country
  bool isSameCityAs(UserLocationEntity other) {
    return city.toLowerCase() == other.city.toLowerCase() &&
        country.toLowerCase() == other.country.toLowerCase();
  }

  @override
  String toString() {
    return 'UserLocationEntity(country: $country, city: $city, '
        'district: $district, lat: $latitude, lng: $longitude)';
  }
}
