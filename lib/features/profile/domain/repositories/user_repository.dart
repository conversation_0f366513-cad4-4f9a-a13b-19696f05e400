/// User Repository
///
/// Provides methods for user-related data operations
/// Abstracts Firebase operations for user management
library user_repository;

import 'package:towasl/shared/models/user_model.dart';

/// User Repository Interface
///
/// Defines the contract for user data operations
/// Allows for easy mocking in tests and different implementations
abstract class UserRepository {
  /// Get user data by user ID
  ///
  /// @param userId The ID of the user to fetch
  /// @return A Future that resolves to the UserModel or null if not found
  Future<UserModel?> getUserById(String userId);

  /// Create a new user
  ///
  /// @param userId The unique ID for the new user
  /// @param userData The user data to save
  /// @return A Future that completes when the user is created
  Future<void> createUser(String userId, Map<String, dynamic> userData);

  /// Update user data
  ///
  /// @param userId The ID of the user to update
  /// @param userData The data to update
  /// @return A Future that completes when the user is updated
  Future<void> updateUser(String userId, Map<String, dynamic> userData);

  /// Check if user exists by mobile number
  ///
  /// @param mobile The mobile number to check
  /// @return A Future that resolves to the user ID if found, null otherwise
  Future<String?> getUserIdByMobile(String mobile);

  /// Check if user is blocked
  ///
  /// @param userId The ID of the user to check
  /// @return A Stream that emits true if user is blocked, false otherwise
  Stream<bool> isUserBlocked(String userId);

  /// Update user's FCM token
  ///
  /// @param userId The ID of the user
  /// @param fcmToken The FCM token to update
  /// @return A Future that completes when the token is updated
  Future<void> updateFcmToken(String userId, String fcmToken);

  /// Update user's session token
  ///
  /// @param userId The ID of the user
  /// @param sessionToken The session token to update
  /// @return A Future that completes when the session token is updated
  Future<void> updateSessionToken(String userId, String sessionToken);

  /// Update user's personal information
  ///
  /// @param userId The ID of the user
  /// @param personalInfo The personal information to update
  /// @return A Future that completes when the personal info is updated
  Future<void> updatePersonalInfo(
      String userId, Map<String, dynamic> personalInfo);

  /// Update user's interests
  ///
  /// @param userId The ID of the user
  /// @param interests The interests data to update
  /// @return A Future that completes when the interests are updated
  Future<void> updateUserInterests(
      String userId, Map<String, dynamic> interests);

  /// Update user's location
  ///
  /// @param userId The ID of the user
  /// @param location The location data to update
  /// @return A Future that completes when the location is updated
  Future<void> updateUserLocation(String userId, Map<String, dynamic> location);

  /// Delete user account
  ///
  /// @param userId The ID of the user to delete
  /// @return A Future that completes when the user is deleted
  Future<void> deleteUser(String userId);
}
