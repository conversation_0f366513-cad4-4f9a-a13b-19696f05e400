/// Location Domain Entity
///
/// Pure business object representing geographical location information
/// Contains no external dependencies and represents core business logic
///
/// This entity defines the structure for location data in the domain layer
library location_entity;

import 'dart:math' as math;

/// User Location Entity
///
/// Represents geographical information about a user's location
/// Includes both coordinates and address components
class LocationEntity {
  /// Country name
  final String country;

  /// Longitude coordinate
  final double longitude;

  /// City name
  final String city;

  /// Latitude coordinate
  final double latitude;

  /// District or neighborhood name
  final String district;

  /// Creates a LocationEntity
  ///
  /// @param country Country name
  /// @param longitude Longitude coordinate
  /// @param city City name
  /// @param latitude Latitude coordinate
  /// @param district District or neighborhood name
  const LocationEntity({
    required this.country,
    required this.longitude,
    required this.city,
    required this.latitude,
    required this.district,
  });

  /// Creates a copy of this entity with updated values
  ///
  /// @param country New country name (optional)
  /// @param longitude New longitude coordinate (optional)
  /// @param city New city name (optional)
  /// @param latitude New latitude coordinate (optional)
  /// @param district New district name (optional)
  /// @return New LocationEntity instance with updated values
  LocationEntity copyWith({
    String? country,
    double? longitude,
    String? city,
    double? latitude,
    String? district,
  }) {
    return LocationEntity(
      country: country ?? this.country,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      latitude: latitude ?? this.latitude,
      district: district ?? this.district,
    );
  }

  /// Gets the full address as a formatted string
  ///
  /// @return Formatted address string
  String get fullAddress {
    final parts = <String>[];
    if (district.isNotEmpty) parts.add(district);
    if (city.isNotEmpty) parts.add(city);
    if (country.isNotEmpty) parts.add(country);
    return parts.join(', ');
  }

  /// Gets the coordinates as a formatted string
  ///
  /// @return Formatted coordinates string (lat, lng)
  String get coordinatesString => '$latitude, $longitude';

  /// Checks if the location has valid coordinates
  ///
  /// @return True if coordinates are valid (not zero)
  bool get hasValidCoordinates => latitude != 0.0 && longitude != 0.0;

  /// Checks if the location has complete address information
  ///
  /// @return True if country, city, and district are all provided
  bool get hasCompleteAddress =>
      country.isNotEmpty && city.isNotEmpty && district.isNotEmpty;

  /// Calculates distance to another location (in kilometers)
  ///
  /// Uses the Haversine formula to calculate the great-circle distance
  ///
  /// @param other The other location to calculate distance to
  /// @return Distance in kilometers
  double distanceTo(LocationEntity other) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double lat1Rad = latitude * (3.14159265359 / 180);
    final double lat2Rad = other.latitude * (3.14159265359 / 180);
    final double deltaLatRad =
        (other.latitude - latitude) * (3.14159265359 / 180);
    final double deltaLngRad =
        (other.longitude - longitude) * (3.14159265359 / 180);

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);

    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  /// Checks if this location is in the same city as another location
  ///
  /// @param other The other location to compare with
  /// @return True if both locations are in the same city and country
  bool isSameCityAs(LocationEntity other) {
    return city.toLowerCase() == other.city.toLowerCase() &&
        country.toLowerCase() == other.country.toLowerCase();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationEntity &&
        other.country == country &&
        other.longitude == longitude &&
        other.city == city &&
        other.latitude == latitude &&
        other.district == district;
  }

  @override
  int get hashCode {
    return country.hashCode ^
        longitude.hashCode ^
        city.hashCode ^
        latitude.hashCode ^
        district.hashCode;
  }

  @override
  String toString() {
    return 'LocationEntity(country: $country, city: $city, '
        'district: $district, lat: $latitude, lng: $longitude)';
  }
}

/// Location Validation Result
///
/// Represents the result of location validation operations
class LocationValidationResult {
  /// Whether the location is valid
  final bool isValid;

  /// Error message if validation failed
  final String? errorMessage;

  /// Creates a LocationValidationResult
  ///
  /// @param isValid Whether the location is valid
  /// @param errorMessage Error message if validation failed
  const LocationValidationResult({
    required this.isValid,
    this.errorMessage,
  });

  /// Creates a successful validation result
  ///
  /// @return LocationValidationResult indicating success
  factory LocationValidationResult.success() {
    return const LocationValidationResult(isValid: true);
  }

  /// Creates a failed validation result
  ///
  /// @param message Error message describing the validation failure
  /// @return LocationValidationResult indicating failure
  factory LocationValidationResult.failure(String message) {
    return LocationValidationResult(
      isValid: false,
      errorMessage: message,
    );
  }

  @override
  String toString() {
    return 'LocationValidationResult(isValid: $isValid, '
        'errorMessage: $errorMessage)';
  }
}
