/// Location Repository Interface
///
/// Defines the contract for location data operations
/// Implementations should handle data source specifics (GPS, API, etc.)
library location_repository;

import '../entities/location_entity.dart';

/// Abstract repository for location data operations
///
/// Defines the interface for location-related data operations
/// Implementations should handle GPS access, geocoding, and location storage
abstract class LocationRepository {
  /// Get current device location
  ///
  /// Retrieves the current GPS coordinates of the device
  /// Requires location permissions to be granted
  ///
  /// @return A Future that resolves to the current LocationEntity
  /// @throws LocationException if location access fails
  Future<LocationEntity> getCurrentLocation();

  /// Get location from coordinates
  ///
  /// Performs reverse geocoding to get address information from coordinates
  ///
  /// @param latitude Latitude coordinate
  /// @param longitude Longitude coordinate
  /// @return A Future that resolves to LocationEntity with address information
  /// @throws LocationException if geocoding fails
  Future<LocationEntity> getLocationFromCoordinates(
    double latitude,
    double longitude,
  );

  /// Search for locations by query
  ///
  /// Performs forward geocoding to find locations matching the search query
  ///
  /// @param query Search query (e.g., "Riyadh, Saudi Arabia")
  /// @return A Future that resolves to a list of matching LocationEntity objects
  /// @throws LocationException if search fails
  Future<List<LocationEntity>> searchLocations(String query);

  /// Update user location
  ///
  /// Saves the user's location to the data source (Firestore, local storage, etc.)
  ///
  /// @param userId User identifier
  /// @param location LocationEntity to save
  /// @return A Future that completes when the location is saved
  /// @throws LocationException if saving fails
  Future<void> updateUserLocation(String userId, LocationEntity location);

  /// Get user's saved location
  ///
  /// Retrieves the user's previously saved location from the data source
  ///
  /// @param userId User identifier
  /// @return A Future that resolves to the user's LocationEntity or null if not found
  /// @throws LocationException if retrieval fails
  Future<LocationEntity?> getUserLocation(String userId);

  /// Check if location services are enabled
  ///
  /// Verifies that location services are enabled on the device
  ///
  /// @return A Future that resolves to true if location services are enabled
  Future<bool> isLocationServiceEnabled();

  /// Check location permissions
  ///
  /// Verifies the current location permission status
  ///
  /// @return A Future that resolves to the current permission status
  Future<LocationPermissionStatus> checkLocationPermission();

  /// Request location permissions
  ///
  /// Requests location permissions from the user if not already granted
  ///
  /// @return A Future that resolves to the permission status after the request
  Future<LocationPermissionStatus> requestLocationPermission();

  /// Validate location data
  ///
  /// Validates that the location data is complete and accurate
  ///
  /// @param location LocationEntity to validate
  /// @return LocationValidationResult indicating validation status
  LocationValidationResult validateLocation(LocationEntity location);
}

/// Location Permission Status
///
/// Represents the current status of location permissions
enum LocationPermissionStatus {
  /// Permission has been granted
  granted,

  /// Permission has been denied
  denied,

  /// Permission has been permanently denied
  deniedForever,

  /// Permission status is unknown
  unknown,
}

/// Location Exception
///
/// Custom exception for location-related errors
class LocationException implements Exception {
  /// Error message describing what went wrong
  final String message;

  /// Error code for programmatic handling
  final String? code;

  /// Creates a LocationException
  ///
  /// @param message Error message
  /// @param code Optional error code
  const LocationException(this.message, {this.code});

  @override
  String toString() =>
      'LocationException: $message${code != null ? ' (Code: $code)' : ''}';
}
