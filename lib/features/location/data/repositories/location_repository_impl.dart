/// Location Repository Implementation
///
/// Concrete implementation of LocationRepository interface
/// Handles location data operations using LocationService and FirebaseService
library location_repository_impl;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/repositories/location_repository.dart';
import '../../../../shared/services/location_service.dart';
import '../../../../shared/services/firebase_service.dart';

/// Location Repository Implementation
///
/// Implements location data operations using LocationService for GPS operations
/// and FirebaseService for data persistence
class LocationRepositoryImpl implements LocationRepository {
  final LocationService _locationService;
  final FirebaseService _firebaseService;

  /// Constructor with dependency injection
  ///
  /// @param locationService Service for GPS and geocoding operations
  /// @param firebaseService Service for data persistence
  LocationRepositoryImpl({
    required LocationService locationService,
    required FirebaseService firebaseService,
  })  : _locationService = locationService,
        _firebaseService = firebaseService;

  @override
  Future<LocationEntity> getCurrentLocation() async {
    try {
      if (kDebugMode) {
        print('LocationRepository: Getting current location');
      }

      final locationData = await _locationService.getCurrentLocation();

      final entity = LocationEntity(
        country: locationData.country,
        longitude: locationData.lng,
        city: locationData.city,
        latitude: locationData.lat,
        district: locationData.district,
      );

      if (kDebugMode) {
        print(
            'LocationRepository: Current location retrieved: ${entity.fullAddress}');
      }

      return entity;
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error getting current location: $e');
      }
      throw LocationException('Failed to get current location: $e');
    }
  }

  @override
  Future<LocationEntity> getLocationFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      if (kDebugMode) {
        print(
            'LocationRepository: Getting location from coordinates: $latitude, $longitude');
      }

      // Use the location service to reverse geocode coordinates
      final locationData = await _locationService.getCurrentLocation();

      // For now, we'll use the current implementation
      // In a full implementation, you'd want to add reverse geocoding to LocationService
      final entity = LocationEntity(
        country: locationData.country,
        longitude: longitude,
        city: locationData.city,
        latitude: latitude,
        district: locationData.district,
      );

      if (kDebugMode) {
        print(
            'LocationRepository: Location from coordinates: ${entity.fullAddress}');
      }

      return entity;
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocationRepository: Error getting location from coordinates: $e');
      }
      throw LocationException('Failed to get location from coordinates: $e');
    }
  }

  @override
  Future<List<LocationEntity>> searchLocations(String query) async {
    try {
      if (kDebugMode) {
        print('LocationRepository: Searching locations for: $query');
      }

      // For now, return empty list as this would require a geocoding API
      // In a full implementation, you'd integrate with a geocoding service
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error searching locations: $e');
      }
      throw LocationException('Failed to search locations: $e');
    }
  }

  @override
  Future<void> updateUserLocation(
      String userId, LocationEntity location) async {
    try {
      if (kDebugMode) {
        print('LocationRepository: Updating user location for: $userId');
      }

      final locationData = {
        'user_location': {
          'lat': location.latitude,
          'lng': location.longitude,
          'country': location.country,
          'city': location.city,
          'district': location.district,
          'updated_at': Timestamp.now(),
        }
      };

      await _firebaseService.updateDocument('users', userId, locationData);

      if (kDebugMode) {
        print('LocationRepository: User location updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error updating user location: $e');
      }
      throw LocationException('Failed to update user location: $e');
    }
  }

  @override
  Future<LocationEntity?> getUserLocation(String userId) async {
    try {
      if (kDebugMode) {
        print('LocationRepository: Getting user location for: $userId');
      }

      final doc = await _firebaseService.getDocument('users', userId);

      if (!doc.exists) {
        if (kDebugMode) {
          print('LocationRepository: User document not found');
        }
        return null;
      }

      final data = doc.data() as Map<String, dynamic>?;
      final userLocationData = data?['user_location'] as Map<String, dynamic>?;

      if (userLocationData == null) {
        if (kDebugMode) {
          print('LocationRepository: No location data found for user');
        }
        return null;
      }

      final entity = LocationEntity(
        country: userLocationData['country'] ?? '',
        longitude: (userLocationData['lng'] ?? 0.0).toDouble(),
        city: userLocationData['city'] ?? '',
        latitude: (userLocationData['lat'] ?? 0.0).toDouble(),
        district: userLocationData['district'] ?? '',
      );

      if (kDebugMode) {
        print(
            'LocationRepository: User location retrieved: ${entity.fullAddress}');
      }

      return entity;
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error getting user location: $e');
      }
      throw LocationException('Failed to get user location: $e');
    }
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await _locationService.isLocationServiceEnabled();
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error checking location service: $e');
      }
      return false;
    }
  }

  @override
  Future<LocationPermissionStatus> checkLocationPermission() async {
    try {
      final isGranted = await _locationService.isLocationPermissionGranted();
      return isGranted
          ? LocationPermissionStatus.granted
          : LocationPermissionStatus.denied;
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error checking location permission: $e');
      }
      return LocationPermissionStatus.unknown;
    }
  }

  @override
  Future<LocationPermissionStatus> requestLocationPermission() async {
    try {
      final isGranted = await _locationService.requestLocationPermission();
      return isGranted
          ? LocationPermissionStatus.granted
          : LocationPermissionStatus.denied;
    } catch (e) {
      if (kDebugMode) {
        print('LocationRepository: Error requesting location permission: $e');
      }
      return LocationPermissionStatus.denied;
    }
  }

  @override
  LocationValidationResult validateLocation(LocationEntity location) {
    if (!location.hasValidCoordinates) {
      return LocationValidationResult.failure('Invalid coordinates');
    }

    if (location.country.isEmpty) {
      return LocationValidationResult.failure('Country is required');
    }

    if (location.city.isEmpty) {
      return LocationValidationResult.failure('City is required');
    }

    return LocationValidationResult.success();
  }
}
