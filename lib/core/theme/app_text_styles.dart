library app_text_styles;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';

/// Naming convention: style{Color}{Size}
// Sizes: 11-14 (Normal), 15-17 (Large), 18-25 (XLarge)

TextStyle stylePrimaryNormal = const TextStyle(color: AppColors.primaryPurple, fontWeight: FontWeight.w300, fontSize: 13);
TextStyle stylePrimaryLarge = const TextStyle(color: AppColors.primaryPurple, fontSize: 16, fontWeight: FontWeight.w500);
    
TextStyle styleFushiNormal = const TextStyle(color: AppColors.primaryFushi, fontSize: 12.5, fontWeight: FontWeight.w400);

TextStyle styleRedNormal = const TextStyle(color: AppColors.red, fontSize: 14, fontWeight: FontWeight.w700);
TextStyle styleRedLarge = const TextStyle(color: AppColors.red, fontSize: 16, fontWeight: FontWeight.w400);

TextStyle styleBlackNormal = const TextStyle(color: AppColors.blackPure, fontSize: 13, fontWeight: FontWeight.w500);
TextStyle styleBlackLarge = const TextStyle(color: AppColors.blackPure, fontSize: 16, fontWeight: FontWeight.w400);

TextStyle styleWhiteLarge = const TextStyle(color: AppColors.whiteOffWhite, fontSize: 16, fontWeight: FontWeight.w500);

TextStyle styleGreyLarge = const TextStyle(color: AppColors.greyMedium, fontSize: 17, fontWeight: FontWeight.w400);
TextStyle styleGreyNormal = const TextStyle(color: AppColors.greyDark, fontSize: 13, fontWeight: FontWeight.w600);