/// Base Notifier
///
/// Provides common functionality for all Notifiers in the MVVM pattern with Riverpod
/// Replaces BaseViewModel to follow Clean Architecture principles with Riverpod
library base_notifier;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

/// Base Notifier for simple state management
///
/// Use this for notifiers that manage simple state without async operations
abstract class BaseNotifier<T> extends Notifier<T> {
  /// Whether the notifier is in a loading state
  bool _isLoading = false;

  /// Error message if operations fail
  String _errorMessage = '';

  /// Whether the notifier has an error
  bool _hasError = false;

  /// Get the current loading state
  bool get isLoading => _isLoading;

  /// Get the current error message
  String get errorMessage => _errorMessage;

  /// Get the current error state
  bool get hasError => _hasError;

  /// Sets the loading state to true
  ///
  /// Used to indicate that an operation is in progress
  void startLoading() {
    _isLoading = true;
    _clearError(); // Clear any previous errors when starting new operation

    if (kDebugMode) {
      print('$runtimeType: Loading started');
    }
  }

  /// Sets the loading state to false
  ///
  /// Used to indicate that an operation has completed
  void stopLoading() {
    _isLoading = false;

    if (kDebugMode) {
      print('$runtimeType: Loading stopped');
    }
  }

  /// Sets an error message and error state
  ///
  /// Used when an operation fails
  void setError(String message) {
    _hasError = true;
    _errorMessage = message;
    _isLoading = false; // Stop loading when error occurs

    if (kDebugMode) {
      print('$runtimeType: Error set - $message');
    }
  }

  /// Clears the error state and message
  void _clearError() {
    _hasError = false;
    _errorMessage = '';

    if (kDebugMode) {
      print('$runtimeType: Error cleared');
    }
  }

  /// Clears the error state and message (public method)
  void clearError() {
    _clearError();
  }

  /// Check if the notifier is in a valid state (not loading and no errors)
  bool get isValidState => !isLoading && !hasError;

  /// Check if the notifier is in an error state
  bool get isErrorState => hasError;

  /// Check if the notifier is in a loading state
  bool get isLoadingState => isLoading;

  /// Reset the notifier to its initial state
  void reset() {
    _isLoading = false;
    _clearError();

    if (kDebugMode) {
      print('$runtimeType: Notifier reset');
    }
  }

  /// Execute an operation with automatic loading state management
  ///
  /// This method handles loading state and error handling automatically
  Future<void> executeOperation(Future<void> Function() operation) async {
    try {
      startLoading();
      await operation();
      stopLoading();
    } catch (error) {
      setError(error.toString());
      if (kDebugMode) {
        print('$runtimeType: Operation failed - $error');
      }
    }
  }

  /// Execute an operation that returns a value with automatic loading state management
  ///
  /// This method handles loading state and error handling automatically
  Future<R?> executeOperationWithResult<R>(
      Future<R> Function() operation) async {
    try {
      startLoading();
      final result = await operation();
      stopLoading();
      return result;
    } catch (error) {
      setError(error.toString());
      if (kDebugMode) {
        print('$runtimeType: Operation with result failed - $error');
      }
      return null;
    }
  }
}

/// Base Async Notifier for async state management
///
/// Use this for notifiers that manage async operations and data loading
abstract class BaseAsyncNotifier<T> extends AsyncNotifier<T> {
  /// Execute an async operation with proper error handling
  ///
  /// This method handles AsyncValue states automatically
  Future<void> executeAsyncOperation(Future<T> Function() operation) async {
    state = const AsyncValue.loading();
    try {
      final result = await operation();
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      if (kDebugMode) {
        print('$runtimeType: Async operation failed - $error');
      }
    }
  }

  /// Execute an async operation without changing the current state to loading
  ///
  /// Useful for operations that should not show loading state
  Future<void> executeAsyncOperationSilent(
      Future<T> Function() operation) async {
    try {
      final result = await operation();
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      if (kDebugMode) {
        print('$runtimeType: Silent async operation failed - $error');
      }
    }
  }

  /// Update state with new data
  void updateData(T data) {
    state = AsyncValue.data(data);
  }

  /// Set error state
  void setError(Object error, StackTrace stackTrace) {
    state = AsyncValue.error(error, stackTrace);
  }

  /// Set loading state
  void setLoading() {
    state = const AsyncValue.loading();
  }

  /// Check if the current state has data
  bool get hasData => state.hasValue;

  /// Check if the current state is loading
  bool get isLoading => state.isLoading;

  /// Check if the current state has error
  bool get hasError => state.hasError;

  /// Get the current data (null if not available)
  T? get data => state.valueOrNull;

  /// Get the current error (null if not available)
  Object? get error => state.error;
}
