/// Application Route Constants
///
/// Centralized definition of all route names used in the application
/// Provides type-safe route navigation and consistent route naming
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  /// Authentication Routes
  static const String welcome = '/welcome';
  static const String splash = '/splash';
  static const String signupLogin = '/signup-login';
  static const String mobileOtp = '/mobile-otp';

  /// Onboarding Routes
  static const String interests = '/interests';
  static const String location = '/location';
  static const String personalInfo = '/personal-info';

  /// Main Application Routes
  static const String home = '/home';
  static const String profile = '/profile';

  /// Profile Routes
  static const String editProfile = '/edit-profile';
  static const String editInterests = '/edit-interests';
  static const String editLocation = '/edit-location';

  /// Utility Routes
  static const String unknown = '/unknown';

  /// Route Parameters
  static const String paramisFromProfile = 'isFromProfile';
  static const String paramIsEditing = 'isEditing';
  static const String paramIsFromEdit = 'isFromEdit';
  static const String paramMobileNumber = 'mobileNumber';
  static const String paramUserId = 'userId';

  /// Get all routes as a list
  static List<String> get allRoutes => [
        welcome,
        splash,
        signupLogin,
        mobileOtp,
        interests,
        location,
        personalInfo,
        home,
        profile,
        editProfile,
        editInterests,
        editLocation,
        unknown,
      ];

  /// Check if a route is valid
  static bool isValidRoute(String route) {
    return allRoutes.contains(route);
  }

  /// Get route display name for debugging
  static String getDisplayName(String route) {
    switch (route) {
      case welcome:
        return 'Welcome Screen';
      case splash:
        return 'Splash Screen';
      case signupLogin:
        return 'Signup/Login';
      case mobileOtp:
        return 'Mobile OTP Verification';
      case interests:
        return 'Interests Selection';
      case location:
        return 'Location Setup';
      case personalInfo:
        return 'Personal Information';
      case home:
        return 'Home';
      case profile:
        return 'Profile';
      case editProfile:
        return 'Edit Profile';
      case editInterests:
        return 'Edit Interests';
      case editLocation:
        return 'Edit Location';
      case unknown:
        return 'Unknown Route';
      default:
        return 'Unknown Route';
    }
  }

  /// Authentication flow routes
  static List<String> get authRoutes => [
        welcome,
        splash,
        signupLogin,
        mobileOtp,
      ];

  /// Onboarding flow routes
  static List<String> get onboardingRoutes => [
        interests,
        location,
        personalInfo,
      ];

  /// Main application routes
  static List<String> get mainRoutes => [
        home,
        profile,
      ];

  /// Profile editing routes
  static List<String> get profileRoutes => [
        editProfile,
        editInterests,
        editLocation,
      ];

  /// Check if route is in authentication flow
  static bool isAuthRoute(String route) {
    return authRoutes.contains(route);
  }

  /// Check if route is in onboarding flow
  static bool isOnboardingRoute(String route) {
    return onboardingRoutes.contains(route);
  }

  /// Check if route is in main application
  static bool isMainRoute(String route) {
    return mainRoutes.contains(route);
  }

  /// Check if route is for profile editing
  static bool isProfileRoute(String route) {
    return profileRoutes.contains(route);
  }

  /// Get the next route in onboarding flow
  static String? getNextOnboardingRoute(String currentRoute) {
    switch (currentRoute) {
      case interests:
        return location;
      case location:
        return personalInfo;
      case personalInfo:
        return home;
      default:
        return null;
    }
  }

  /// Get the previous route in onboarding flow
  static String? getPreviousOnboardingRoute(String currentRoute) {
    switch (currentRoute) {
      case location:
        return interests;
      case personalInfo:
        return location;
      case home:
        return personalInfo;
      default:
        return null;
    }
  }
}
