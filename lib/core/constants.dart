import 'package:towasl/shared/helpers/internationalization/country_data.dart';
import 'package:url_launcher/url_launcher.dart';

/// Application Constants
///
/// Contains all the constant values used throughout the application
/// including default settings, URLs, and API keys
class Constants {
  /// Default country data for phone number input
  static Country defaultCountryData = const Country(
    name: "Saudi Arabia",
    flag: "🇸🇦",
    code: "SA",
    dialCode: "966",
    minLength: 9,
    maxLength: 9,
    nameTranslations: {},
  );

  /// Helper method to launch URLs
  ///
  /// Opens the provided URL in the device's default browser
  /// Throws an exception if the URL cannot be launched
  ///
  /// @param url The URL to open
  static launchURL(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  }

  /// Legal URLs
  static const termsUrl = "https://towasl.com/terms-and-conditions";
  static const privacyUrl = "https://towasl.com/privacy-policy";

  /// OneSignal push notification configuration
  static const oneSignalAppID = "************************************";

  /// Validation constants
  static const yearLength = 4;
}
