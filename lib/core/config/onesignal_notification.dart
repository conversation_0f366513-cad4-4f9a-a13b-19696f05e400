/// OneSignal Notification Configuration
///
/// This file contains handlers for OneSignal push notifications
/// including click listeners and foreground notification display
library onesignal_notification;

import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

/// OneSignal Notification Handler
///
/// Provides methods for handling different notification events
/// such as clicks and foreground notification display
class OneSignalNotificationsClass {
  /// Sets up a listener for notification clicks
  ///
  /// Removes any existing click listeners and adds a new one
  /// Currently configured as a placeholder for future implementation
  static onNotificationClick() {
    OneSignal.Notifications.removeClickListener((event) {});
    OneSignal.Notifications.addClickListener((event) {});
  }

  /// Sets up a listener for notifications received while app is in foreground
  ///
  /// Currently configured to prevent automatic display of notifications
  /// and instead log the notification content for debugging
  static onForegroundNotificationClick() {
    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      // Log notification details in debug mode
      if (kDebugMode) {
        print(
            'NOTIFICATION WILL DISPLAY LISTENER CALLED WITH: ${event.notification.jsonRepresentation()}');
      }

      // Prevent default notification display behavior
      event.preventDefault();

      // This is where you would handle custom notification display
      // or perform other actions when a notification is received

      // To display the notification after custom processing:
      // event.notification.display();

      // Log notification content (this string is not used anywhere)
      "Notification received in foreground notification: \n${event.notification.jsonRepresentation().replaceAll("\\n", "\n")}";
    });
  }
}
