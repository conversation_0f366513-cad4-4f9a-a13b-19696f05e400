/// Navigation Provider
///
/// Provides Riverpod-based navigation functionality
/// Replaces GetX navigation with Riverpod state management
library navigation_provider;

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'navigation_provider.g.dart';

/// Navigation state for managing app navigation
class NavigationState {
  /// Current route name
  final String currentRoute;

  /// Navigation history stack
  final List<String> routeHistory;

  /// Whether navigation is in progress
  final bool isNavigating;

  const NavigationState({
    this.currentRoute = '/',
    this.routeHistory = const ['/'],
    this.isNavigating = false,
  });

  NavigationState copyWith({
    String? currentRoute,
    List<String>? routeHistory,
    bool? isNavigating,
  }) {
    return NavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      routeHistory: routeHistory ?? this.routeHistory,
      isNavigating: isNavigating ?? this.isNavigating,
    );
  }
}

/// Navigation Notifier
///
/// Manages navigation state and provides navigation methods
@riverpod
class Navigation extends _$Navigation {
  /// Global navigator key for navigation
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  NavigationState build() {
    return const NavigationState();
  }

  /// Get the current navigator context
  BuildContext? get context => navigatorKey.currentContext;

  /// Navigate to a new route
  Future<void> navigateTo(String routeName, {Object? arguments}) async {
    if (context == null) return;

    state = state.copyWith(isNavigating: true);

    try {
      await Navigator.of(context!).pushNamed(routeName, arguments: arguments);

      final newHistory = [...state.routeHistory, routeName];
      state = state.copyWith(
        currentRoute: routeName,
        routeHistory: newHistory,
        isNavigating: false,
      );
    } catch (e) {
      state = state.copyWith(isNavigating: false);
      rethrow;
    }
  }

  /// Navigate and replace current route
  Future<void> navigateToReplacement(String routeName,
      {Object? arguments}) async {
    if (context == null) return;

    state = state.copyWith(isNavigating: true);

    try {
      await Navigator.of(context!)
          .pushReplacementNamed(routeName, arguments: arguments);

      final newHistory = [...state.routeHistory];
      if (newHistory.isNotEmpty) {
        newHistory[newHistory.length - 1] = routeName;
      } else {
        newHistory.add(routeName);
      }

      state = state.copyWith(
        currentRoute: routeName,
        routeHistory: newHistory,
        isNavigating: false,
      );
    } catch (e) {
      state = state.copyWith(isNavigating: false);
      rethrow;
    }
  }

  /// Navigate and clear all previous routes
  Future<void> navigateToAndClearAll(String routeName,
      {Object? arguments}) async {
    if (context == null) return;

    state = state.copyWith(isNavigating: true);

    try {
      await Navigator.of(context!).pushNamedAndRemoveUntil(
        routeName,
        (route) => false,
        arguments: arguments,
      );

      state = state.copyWith(
        currentRoute: routeName,
        routeHistory: [routeName],
        isNavigating: false,
      );
    } catch (e) {
      state = state.copyWith(isNavigating: false);
      rethrow;
    }
  }

  /// Go back to previous route
  Future<void> goBack() async {
    if (context == null) return;

    if (Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop();

      final newHistory = [...state.routeHistory];
      if (newHistory.length > 1) {
        newHistory.removeLast();
        state = state.copyWith(
          currentRoute: newHistory.last,
          routeHistory: newHistory,
        );
      }
    }
  }

  /// Check if can go back
  bool canGoBack() {
    return context != null && Navigator.of(context!).canPop();
  }
}

/// Convenience providers for navigation state
@riverpod
String currentRoute(CurrentRouteRef ref) {
  return ref.watch(navigationProvider).currentRoute;
}

@riverpod
bool isNavigating(IsNavigatingRef ref) {
  return ref.watch(navigationProvider).isNavigating;
}

@riverpod
List<String> routeHistory(RouteHistoryRef ref) {
  return ref.watch(navigationProvider).routeHistory;
}
