/// OTP Sender Provider
///
/// Provides Riverpod providers for OTP sender configuration
/// Manages dynamic OTP sender name from Firestore
library otp_sender_provider;

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';

part 'otp_sender_provider.g.dart';

/// OTP Sender Provider
///
/// Provides the current OTP sender name from Firestore
/// Falls back to default value if Firestore is unavailable
@riverpod
Future<String> otpSender(OtpSenderRef ref) async {
  final otpSenderService = ref.read(otpSenderServiceProvider);
  return await otpSenderService.getOtpSender();
}

/// OTP Sender Notifier
///
/// Manages OTP sender state with caching and refresh capabilities
@riverpod
class OtpSenderNotifier extends _$OtpSenderNotifier {
  @override
  Future<String> build() async {
    final otpSenderService = ref.read(otpSenderServiceProvider);
    return await otpSenderService.getOtpSender();
  }

  /// Refresh OTP sender from Firestore
  ///
  /// Forces a fresh fetch from Firestore, bypassing any cache
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    try {
      final otpSenderService = ref.read(otpSenderServiceProvider);
      final newSender = await otpSenderService.getOtpSender();
      state = AsyncValue.data(newSender);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
