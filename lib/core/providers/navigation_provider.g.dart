// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentRouteHash() => r'b513a4e9f2e95c5583558d1dadc333d81ff97aa5';

/// Convenience providers for navigation state
///
/// Copied from [currentRoute].
@ProviderFor(currentRoute)
final currentRouteProvider = AutoDisposeProvider<String>.internal(
  currentRoute,
  name: r'currentRouteProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentRouteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentRouteRef = AutoDisposeProviderRef<String>;
String _$isNavigatingHash() => r'b9ce9699e122f2f5dae0324f040faa055a6f413a';

/// See also [isNavigating].
@ProviderFor(isNavigating)
final isNavigatingProvider = AutoDisposeProvider<bool>.internal(
  isNavigating,
  name: r'isNavigatingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isNavigatingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsNavigatingRef = AutoDisposeProviderRef<bool>;
String _$routeHistoryHash() => r'071d49f0162b2db0fc195b442a23cbad2ad94876';

/// See also [routeHistory].
@ProviderFor(routeHistory)
final routeHistoryProvider = AutoDisposeProvider<List<String>>.internal(
  routeHistory,
  name: r'routeHistoryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$routeHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RouteHistoryRef = AutoDisposeProviderRef<List<String>>;
String _$navigationHash() => r'643a9078917eaf0e64805f0aac560e474d99f93e';

/// Navigation Notifier
///
/// Manages navigation state and provides navigation methods
///
/// Copied from [Navigation].
@ProviderFor(Navigation)
final navigationProvider =
    AutoDisposeNotifierProvider<Navigation, NavigationState>.internal(
  Navigation.new,
  name: r'navigationProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$navigationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Navigation = AutoDisposeNotifier<NavigationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
