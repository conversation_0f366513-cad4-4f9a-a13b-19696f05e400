// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_sender_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$otpSenderHash() => r'68d7a4a0a085759dcbe58d07fa80d0622326ec9d';

/// OTP Sender Provider
///
/// Provides the current OTP sender name from Firestore
/// Falls back to default value if Firestore is unavailable
///
/// Copied from [otpSender].
@ProviderFor(otpSender)
final otpSenderProvider = AutoDisposeFutureProvider<String>.internal(
  otpSender,
  name: r'otpSenderProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$otpSenderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef OtpSenderRef = AutoDisposeFutureProviderRef<String>;
String _$otpSenderNotifierHash() => r'7702d6f308ae8b4afe797cdd74f03e37f62a8386';

/// OTP Sender Notifier
///
/// Manages OTP sender state with caching and refresh capabilities
///
/// Copied from [OtpSenderNotifier].
@ProviderFor(OtpSenderNotifier)
final otpSenderNotifierProvider =
    AutoDisposeAsyncNotifierProvider<OtpSenderNotifier, String>.internal(
  OtpSenderNotifier.new,
  name: r'otpSenderNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$otpSenderNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OtpSenderNotifier = AutoDisposeAsyncNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
