/// Service Providers
///
/// Provides Riverpod providers for core services in the application
/// Replaces GetX dependency injection for services
library service_providers;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get_storage/get_storage.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/shared/services/storage_service.dart';
import 'package:towasl/shared/services/firebase_service.dart';
import 'package:towasl/shared/services/analytics_service.dart';
import 'package:towasl/shared/services/location_service.dart';

import 'package:towasl/shared/services/version_service.dart';
import 'package:towasl/shared/services/otp_sender_service.dart';
import 'package:towasl/features/app_update/app_update.dart';

part 'service_providers.g.dart';

/// Storage Service Provider
///
/// Provides persistent storage functionality using GetStorage
@riverpod
StorageService storageService(StorageServiceRef ref) {
  return StorageServiceImpl(GetStorage());
}

/// Firebase Service Provider
///
/// Provides Firebase functionality for the application
@riverpod
FirebaseService firebaseService(FirebaseServiceRef ref) {
  return FirebaseServiceImpl(
    firestore: FirebaseFirestore.instance,
    messaging: FirebaseMessaging.instance,
  );
}

/// Analytics Service Provider
///
/// Provides Firebase Analytics functionality for the application
@riverpod
AnalyticsService analyticsService(AnalyticsServiceRef ref) {
  return AnalyticsServiceImpl(
    analytics: FirebaseAnalytics.instance,
  );
}

/// Location Service Provider
///
/// Provides location functionality for the application
@riverpod
LocationService locationService(LocationServiceRef ref) {
  return LocationServiceImpl();
}

/// Version Service Provider
///
/// Provides version checking functionality
@riverpod
VersionService versionService(VersionServiceRef ref) {
  final service = VersionService();
  // Note: Initialize should be called before using this service
  return service;
}

/// App Update Remote Data Source Provider
///
/// Provides remote data source for app update functionality
final appUpdateRemoteDataSourceProvider =
    Provider<AppUpdateRemoteDataSource>((ref) {
  final firebaseService = ref.read(firebaseServiceProvider);
  return AppUpdateRemoteDataSourceImpl(firebaseService);
});

/// App Update Repository Provider
///
/// Provides repository for app update functionality
final appUpdateRepositoryProvider = Provider<AppUpdateRepository>((ref) {
  final remoteDataSource = ref.read(appUpdateRemoteDataSourceProvider);
  final storageService = ref.read(storageServiceProvider);
  return AppUpdateRepositoryImpl(remoteDataSource, storageService);
});

/// Check App Update Use Case Provider
///
/// Provides use case for checking app updates
final checkAppUpdateUseCaseProvider = Provider<CheckAppUpdateUseCase>((ref) {
  final repository = ref.read(appUpdateRepositoryProvider);
  return CheckAppUpdateUseCase(repository);
});

/// OTP Sender Service Provider
///
/// Provides OTP sender configuration functionality
@riverpod
OtpSenderService otpSenderService(OtpSenderServiceRef ref) {
  return OtpSenderServiceImpl(
    ref.read(firebaseServiceProvider),
  );
}
