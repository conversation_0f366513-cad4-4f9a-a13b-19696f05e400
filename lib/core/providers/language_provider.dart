/// Language Provider
///
/// Manages application language settings using Riverpod
/// Replaces GetX LanguageController with Riverpod state management
library language_provider;

import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/service_providers.dart';

part 'language_provider.g.dart';

/// Language state class
class LanguageState {
  /// Current language code
  final String languageCode;

  /// Current locale
  final Locale locale;

  const LanguageState({
    required this.languageCode,
    required this.locale,
  });

  LanguageState copyWith({
    String? languageCode,
    Locale? locale,
  }) {
    return LanguageState(
      languageCode: languageCode ?? this.languageCode,
      locale: locale ?? this.locale,
    );
  }
}

/// Language Provider
///
/// Manages application language settings and locale changes
/// Provides reactive language state management
@riverpod
class Language extends _$Language {
  @override
  LanguageState build() {
    // Load saved language preference on initialization
    _loadSavedLanguage();

    if (kDebugMode) {
      print('LanguageProvider: Initialized with default language: ar');
    }

    return const LanguageState(
      languageCode: 'ar',
      locale: Locale('ar', 'SA'),
    );
  }

  /// Load saved language preference from storage
  void _loadSavedLanguage() {
    Future.microtask(() async {
      try {
        final storageService = ref.read(storageServiceProvider);
        final savedLanguage = storageService.getLanguage();

        if (kDebugMode) {
          print('LanguageProvider: Loaded saved language: $savedLanguage');
        }

        await changeLanguage(savedLanguage);
      } catch (e) {
        if (kDebugMode) {
          print('LanguageProvider: Error loading saved language: $e');
        }
        // Default to Arabic if error
        await changeLanguage('ar');
      }
    });
  }

  /// Change the application language
  ///
  /// Updates the app locale and saves the preference
  ///
  /// @param languageCode Language code to switch to (en or ar)
  Future<void> changeLanguage(String languageCode) async {
    try {
      // Create locale with appropriate country code
      final locale = Locale(languageCode, languageCode == "en" ? "US" : "SA");

      // Update state
      state = state.copyWith(
        languageCode: languageCode,
        locale: locale,
      );

      // Save preference to persistent storage
      final storageService = ref.read(storageServiceProvider);
      storageService.setLanguage(languageCode);

      if (kDebugMode) {
        print('LanguageProvider: Language changed to: $languageCode');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LanguageProvider: Error changing language: $e');
      }
    }
  }

  /// Toggle between English and Arabic
  Future<void> toggleLanguage() async {
    final newLanguage = state.languageCode == 'en' ? 'ar' : 'en';
    await changeLanguage(newLanguage);
  }

  /// Check if current language is Arabic
  bool get isArabic => state.languageCode == 'ar';

  /// Check if current language is English
  bool get isEnglish => state.languageCode == 'en';
}

/// Convenience providers for easy access

/// Provider for current language code
@riverpod
String currentLanguageCode(CurrentLanguageCodeRef ref) {
  return ref.watch(languageProvider).languageCode;
}

/// Provider for current locale
@riverpod
Locale currentLocale(CurrentLocaleRef ref) {
  return ref.watch(languageProvider).locale;
}

/// Provider for checking if current language is Arabic
@riverpod
bool isArabicLanguage(IsArabicLanguageRef ref) {
  return ref.watch(languageProvider).languageCode == 'ar';
}

/// Provider for checking if current language is English
@riverpod
bool isEnglishLanguage(IsEnglishLanguageRef ref) {
  return ref.watch(languageProvider).languageCode == 'en';
}
