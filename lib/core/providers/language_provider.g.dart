// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'language_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentLanguageCodeHash() =>
    r'69c137b03bf66c12a7ca5517a251f2056dd7ad54';

/// Convenience providers for easy access
/// Provider for current language code
///
/// Copied from [currentLanguageCode].
@ProviderFor(currentLanguageCode)
final currentLanguageCodeProvider = AutoDisposeProvider<String>.internal(
  currentLanguageCode,
  name: r'currentLanguageCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentLanguageCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentLanguageCodeRef = AutoDisposeProviderRef<String>;
String _$currentLocaleHash() => r'e1d141f67a577f38240028e5323ff8cbd398f1ab';

/// Provider for current locale
///
/// Copied from [currentLocale].
@ProviderFor(currentLocale)
final currentLocaleProvider = AutoDisposeProvider<Locale>.internal(
  currentLocale,
  name: r'currentLocaleProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentLocaleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentLocaleRef = AutoDisposeProviderRef<Locale>;
String _$isArabicLanguageHash() => r'fa535bf0a74daca45399e18daa92dec0efde7952';

/// Provider for checking if current language is Arabic
///
/// Copied from [isArabicLanguage].
@ProviderFor(isArabicLanguage)
final isArabicLanguageProvider = AutoDisposeProvider<bool>.internal(
  isArabicLanguage,
  name: r'isArabicLanguageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isArabicLanguageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsArabicLanguageRef = AutoDisposeProviderRef<bool>;
String _$isEnglishLanguageHash() => r'c7264fae34f251722ca2956561ea8b6c7db86e70';

/// Provider for checking if current language is English
///
/// Copied from [isEnglishLanguage].
@ProviderFor(isEnglishLanguage)
final isEnglishLanguageProvider = AutoDisposeProvider<bool>.internal(
  isEnglishLanguage,
  name: r'isEnglishLanguageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isEnglishLanguageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsEnglishLanguageRef = AutoDisposeProviderRef<bool>;
String _$languageHash() => r'51c7843b364237d44cdae88a9e2979ee4af1b4b6';

/// Language Provider
///
/// Manages application language settings and locale changes
/// Provides reactive language state management
///
/// Copied from [Language].
@ProviderFor(Language)
final languageProvider =
    AutoDisposeNotifierProvider<Language, LanguageState>.internal(
  Language.new,
  name: r'languageProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$languageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Language = AutoDisposeNotifier<LanguageState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
