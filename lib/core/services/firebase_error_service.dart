/// Firebase Error Service
///
/// Handles Firebase error management and Firestore configuration
/// Extracted from FireStoreErrorHandler to follow Clean Architecture principles
library firebase_error_service;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Firebase Error Service Interface
///
/// Defines the contract for Firebase error handling
abstract class FirebaseErrorService {
  // State getters
  bool get isPermissionDenied;

  // Methods
  void initialize();
  void dispose();
  void handleFirebaseError(dynamic error);
  void resetPermissionDenied();
  bool isNetworkError(dynamic error);
  bool isPermissionError(dynamic error);
}

/// Firebase Error Service Implementation
///
/// Implements Firebase error handling and Firestore configuration
class FirebaseErrorServiceImpl implements FirebaseErrorService {
  /// Flag to track if permission has been denied
  bool _isPermissionDenied = false;

  @override
  bool get isPermissionDenied => _isPermissionDenied;

  @override
  void initialize() {
    _configureFirestore();
    _setupErrorListeners();

    if (kDebugMode) {
      print('FirebaseErrorService: Initialized');
    }
  }

  @override
  void dispose() {
    if (kDebugMode) {
      print('FirebaseErrorService: Disposed');
    }
  }

  /// Configure Firestore settings
  void _configureFirestore() {
    try {
      // Configure Firestore settings for offline persistence
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes:
            Settings.CACHE_SIZE_UNLIMITED, // Use unlimited cache size
      );

      if (kDebugMode) {
        print('FirebaseErrorService: Firestore configured successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('FirebaseErrorService: Error configuring Firestore - $e');
      }
    }
  }

  /// Set up error listeners for Firebase operations
  void _setupErrorListeners() {
    try {
      // Set up a listener for Firestore sync events to catch errors
      FirebaseFirestore.instance.snapshotsInSync().listen(
          // Success callback (empty as we're only interested in errors)
          (_) {},
          // Error callback
          onError: (error) {
        handleFirebaseError(error);
      });

      if (kDebugMode) {
        print('FirebaseErrorService: Error listeners set up successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('FirebaseErrorService: Error setting up listeners - $e');
      }
    }
  }

  @override
  void handleFirebaseError(dynamic error) {
    // Log all errors in debug mode
    if (kDebugMode) {
      print("FirebaseErrorService: Firebase error occurred: $error");
    }

    // Handle specific error types
    if (error is FirebaseException) {
      _handleFirebaseException(error);
    } else {
      _handleGenericError(error);
    }
  }

  /// Handle Firebase-specific exceptions
  ///
  /// @param exception The Firebase exception to handle
  void _handleFirebaseException(FirebaseException exception) {
    if (kDebugMode) {
      print(
          "FirebaseErrorService: Firebase exception - Code: ${exception.code}, Message: ${exception.message}");
    }

    switch (exception.code) {
      case 'permission-denied':
        _handlePermissionDenied(exception);
        break;
      case 'unavailable':
        _handleServiceUnavailable(exception);
        break;
      case 'deadline-exceeded':
        _handleTimeout(exception);
        break;
      case 'resource-exhausted':
        _handleResourceExhausted(exception);
        break;
      default:
        _handleUnknownFirebaseError(exception);
    }
  }

  /// Handle permission denied errors
  ///
  /// @param exception The permission denied exception
  void _handlePermissionDenied(FirebaseException exception) {
    _isPermissionDenied = true;

    if (kDebugMode) {
      print("FirebaseErrorService: Permission denied error detected");
    }

    // Additional permission denied handling logic can be added here
    // For example, showing a dialog to the user or redirecting to login
  }

  /// Handle service unavailable errors
  ///
  /// @param exception The service unavailable exception
  void _handleServiceUnavailable(FirebaseException exception) {
    if (kDebugMode) {
      print("FirebaseErrorService: Firebase service unavailable");
    }

    // Handle service unavailable logic here
    // For example, showing offline mode or retry options
  }

  /// Handle timeout errors
  ///
  /// @param exception The timeout exception
  void _handleTimeout(FirebaseException exception) {
    if (kDebugMode) {
      print("FirebaseErrorService: Firebase operation timed out");
    }

    // Handle timeout logic here
    // For example, showing retry options or offline fallback
  }

  /// Handle resource exhausted errors
  ///
  /// @param exception The resource exhausted exception
  void _handleResourceExhausted(FirebaseException exception) {
    if (kDebugMode) {
      print("FirebaseErrorService: Firebase resources exhausted");
    }

    // Handle resource exhausted logic here
    // For example, showing rate limiting message
  }

  /// Handle unknown Firebase errors
  ///
  /// @param exception The unknown Firebase exception
  void _handleUnknownFirebaseError(FirebaseException exception) {
    if (kDebugMode) {
      print(
          "FirebaseErrorService: Unknown Firebase error - ${exception.code}: ${exception.message}");
    }

    // Handle unknown Firebase errors here
    // For example, logging to crash reporting service
  }

  /// Handle generic (non-Firebase) errors
  ///
  /// @param error The generic error
  void _handleGenericError(dynamic error) {
    if (kDebugMode) {
      print("FirebaseErrorService: Generic error occurred: $error");
    }

    // Handle generic errors here
    // For example, logging to crash reporting service
  }

  /// Reset permission denied state
  @override
  void resetPermissionDenied() {
    _isPermissionDenied = false;

    if (kDebugMode) {
      print("FirebaseErrorService: Permission denied state reset");
    }
  }

  /// Check if error is a network-related error
  ///
  /// @param error The error to check
  /// @return True if the error is network-related
  @override
  bool isNetworkError(dynamic error) {
    if (error is FirebaseException) {
      return error.code == 'unavailable' ||
          error.code == 'deadline-exceeded' ||
          error.message?.toLowerCase().contains('network') == true;
    }
    return false;
  }

  /// Check if error is a permission-related error
  ///
  /// @param error The error to check
  /// @return True if the error is permission-related
  @override
  bool isPermissionError(dynamic error) {
    if (error is FirebaseException) {
      return error.code == 'permission-denied';
    }
    return false;
  }
}
