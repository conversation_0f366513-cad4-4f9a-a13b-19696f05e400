/// App State Service
///
/// Manages global application state and configuration
/// Extracted from AppController to follow Clean Architecture principles
library app_state_service;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/models/user_model.dart';
import 'package:towasl/shared/services/storage_service.dart';

/// App State Service Interface
///
/// Defines the contract for global app state management
abstract class AppStateService {
  // User state getters
  String get userId;
  UserModel get userModel;

  // API credentials getters
  String get msegatApikey;
  String get msegatUserSender;
  String get msegatUsername;

  // Authentication provider getter
  String get providerName;

  // User state setters
  set userId(String id);
  set userModel(UserModel model);

  // API credentials setters
  set msegatUserSender(String sender);
  set msegatUsername(String username);

  // Authentication provider setter
  set providerName(String name);

  // Methods
  void clearUserData();
}

/// App State Service Implementation
///
/// Implements global app state management using reactive variables
class AppStateServiceImpl implements AppStateService {
  // Services
  final StorageService _storageService;

  // User state
  String _userId = '';
  UserModel _userModel = UserModel();

  // API credentials
  final String _msegatApikey = "6c3076fac5b70da2eb1d59bcb1475b8b";
  String _msegatUserSender = "";
  String _msegatUsername = "";

  // Authentication provider
  String _providerName = "";

  /// Constructor with dependency injection
  ///
  /// @param storageService The storage service for persistent data
  AppStateServiceImpl({
    required StorageService storageService,
  }) : _storageService = storageService {
    _loadUserIdFromStorage();
  }

  /// Load user ID from storage on initialization
  void _loadUserIdFromStorage() {
    final storedUserId = _storageService.getUserIDValue();
    if (storedUserId.isNotEmpty) {
      _userId = storedUserId;
      if (kDebugMode) {
        print("AppStateService: Loaded user ID from storage: $storedUserId");
      }
    }
  }

  @override
  String get userId => _userId;

  @override
  set userId(String id) {
    if (kDebugMode) {
      print("AppStateService: Setting user ID: $id");
    }

    _userId = id;
    if (id.isNotEmpty) {
      _storageService.setLoginData(id);
    }
  }

  @override
  UserModel get userModel => _userModel;

  @override
  set userModel(UserModel model) {
    _userModel = model;
  }

  @override
  String get msegatApikey => _msegatApikey;

  @override
  String get msegatUserSender => _msegatUserSender;

  @override
  set msegatUserSender(String sender) {
    _msegatUserSender = sender;
  }

  @override
  String get msegatUsername => _msegatUsername;

  @override
  set msegatUsername(String username) {
    _msegatUsername = username;
  }

  @override
  String get providerName => _providerName;

  @override
  set providerName(String name) {
    _providerName = name;
  }

  @override
  void clearUserData() {
    _userId = '';
    _userModel = UserModel();
  }
}
