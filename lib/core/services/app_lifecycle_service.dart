/// App Lifecycle Service
///
/// Handles app lifecycle events and state changes
/// Manages session validation, network connectivity, and background/foreground transitions
/// Extracted from AppLifecycleController to follow Clean Architecture principles
library app_lifecycle_service;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:towasl/core/services/app_state_service.dart';

/// App Lifecycle Service Interface
///
/// Defines the contract for app lifecycle management
abstract class AppLifecycleService {
  // State getters
  bool get isAppInForeground;
  bool get isConnected;
  DateTime get lastActiveTime;

  // Methods
  void initialize();
  void dispose();
  void handleAppLifecycleStateChange(AppLifecycleState state);
}

/// App Lifecycle Service Implementation
///
/// Implements app lifecycle monitoring and connectivity management
class AppLifecycleServiceImpl
    with WidgetsBindingObserver
    implements AppLifecycleService {
  // Dependencies
  final AppStateService _appStateService;

  // State variables
  bool _isAppInForeground = true;
  bool _isConnected = true;
  DateTime _lastActiveTime = DateTime.now();

  // Connectivity subscription
  late final Connectivity _connectivity;

  /// Constructor with dependency injection
  ///
  /// @param appStateService The app state service
  AppLifecycleServiceImpl({
    required AppStateService appStateService,
  }) : _appStateService = appStateService;

  @override
  bool get isAppInForeground => _isAppInForeground;

  @override
  bool get isConnected => _isConnected;

  @override
  DateTime get lastActiveTime => _lastActiveTime;

  @override
  void initialize() {
    _initializeLifecycleMonitoring();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    if (kDebugMode) {
      print('AppLifecycleService: Lifecycle monitoring disposed');
    }
  }

  /// Initialize app lifecycle monitoring
  ///
  /// Sets up observers for app state changes and network connectivity
  void _initializeLifecycleMonitoring() {
    // Add this service as a lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Initialize network connectivity monitoring
    _initializeConnectivityMonitoring();

    if (kDebugMode) {
      print('AppLifecycleService: Lifecycle monitoring initialized');
    }
  }

  /// Initialize network connectivity monitoring
  ///
  /// Sets up listeners for network connectivity changes
  void _initializeConnectivityMonitoring() {
    _connectivity = Connectivity();

    // Listen for connectivity changes
    _connectivity.onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      _handleConnectivityChange(
          results.isNotEmpty ? results.first : ConnectivityResult.none);
    });

    // Check initial connectivity status
    _checkInitialConnectivity();

    if (kDebugMode) {
      print('AppLifecycleService: Connectivity monitoring initialized');
    }
  }

  /// Check initial connectivity status
  Future<void> _checkInitialConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      _handleConnectivityChange(
          results.isNotEmpty ? results.first : ConnectivityResult.none);
    } catch (e) {
      if (kDebugMode) {
        print('AppLifecycleService: Error checking initial connectivity - $e');
      }
      // Default to connected if we can't determine status
      _isConnected = true;
    }
  }

  /// Handle connectivity changes
  ///
  /// @param result The new connectivity result
  void _handleConnectivityChange(ConnectivityResult result) {
    final wasConnected = _isConnected;
    final isNowConnected = result != ConnectivityResult.none;

    _isConnected = isNowConnected;

    if (kDebugMode) {
      print(
          'AppLifecycleService: Connectivity changed - $result (Connected: $isNowConnected)');
    }

    // If we just reconnected and user is logged in, validate session
    if (!wasConnected && isNowConnected && _appStateService.userId.isNotEmpty) {
      _validateSessionOnReconnect();
    }
  }

  @override
  void handleAppLifecycleStateChange(AppLifecycleState state) {
    if (kDebugMode) {
      print('AppLifecycleService: App lifecycle state changed to: $state');
    }

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    handleAppLifecycleStateChange(state);
  }

  /// Handle app resumed (foreground)
  void _handleAppResumed() {
    _isAppInForeground = true;
    _lastActiveTime = DateTime.now();

    if (kDebugMode) {
      print('AppLifecycleService: App resumed');
    }

    // Validate session when app comes to foreground
    if (_appStateService.userId.isNotEmpty) {
      _validateSessionOnResume();
    }
  }

  /// Handle app paused (background)
  void _handleAppPaused() {
    _isAppInForeground = false;
    _lastActiveTime = DateTime.now();

    if (kDebugMode) {
      print('AppLifecycleService: App paused');
    }
  }

  /// Handle app inactive
  void _handleAppInactive() {
    if (kDebugMode) {
      print('AppLifecycleService: App inactive');
    }
  }

  /// Handle app detached
  void _handleAppDetached() {
    if (kDebugMode) {
      print('AppLifecycleService: App detached');
    }
  }

  /// Handle app hidden
  void _handleAppHidden() {
    if (kDebugMode) {
      print('AppLifecycleService: App hidden');
    }
  }

  /// Validate session when app resumes
  void _validateSessionOnResume() {
    if (kDebugMode) {
      print('AppLifecycleService: Validating session on app resume');
    }
    // Session validation logic can be implemented here
    // For now, we just log the event
  }

  /// Validate session when reconnecting to internet
  void _validateSessionOnReconnect() {
    if (kDebugMode) {
      print('AppLifecycleService: Validating session on reconnect');
    }
    // Session validation logic can be implemented here
    // For now, we just log the event
  }
}
