# Towasl

User Profile and Authentication App

A Flutter application focused on user authentication, profile management, and settings. Built with Clean Architecture, MVVM pattern, and Riverpod state management.

# Getting Started -----------------------------------------------

## Prerequisites
- Flutter SDK (latest stable version)
- iOS Simulator or Android Emulator
- Firebase project configured for the app

## Installation

```bash
# Clean and install dependencies
flutter clean; flutter pub get;

# For iOS
cd ios; pod install;
cd ..

# Generate code (for Riverpod providers)
dart run build_runner build --delete-conflicting-outputs

# Run the app
open -a Simulator  # or start Android emulator
flutter run
```

## Features

- 📱 **Mobile Authentication**: OTP-based login with phone numbers
- 👤 **Profile Management**: Complete user profile creation and editing
- 🎯 **Interest Selection**: Choose from various categories of interests
- 📍 **Location Services**: Set and manage user location
- ⚙️ **Settings**: Simple settings screen with logout functionality
- 🏗️ **Clean Architecture**: Well-structured codebase following SOLID principles
- 🔄 **State Management**: Modern Riverpod-based state management

# 🧱 Flutter Project Architecture Guide -----------------------

This document defines the architecture and design patterns used in this project, following Clean Architecture principles with MVVM pattern and Riverpod state management.

---

## 📂 Architecture Overview

The project follows **Feature-Based Clean Architecture** with **MVVM pattern** for the presentation layer:

### Core Layers:
1. **Data Layer** – Handles data fetching, storage, and modeling
2. **Domain Layer** – Contains core business logic and rules
3. **Presentation Layer** – Manages UI and state with Riverpod providers
4. **Core/Shared Layer** – Common utilities, themes, and configurations

### Key Features:
- **Feature-Based Structure**: Each feature is self-contained with its own data/domain/presentation layers
- **MVVM Pattern**: ViewModels (Riverpod providers) manage business logic and state
- **Clean Architecture**: Clear separation of concerns and dependency inversion
- **Riverpod State Management**: Modern, compile-safe reactive state management

---

## 📁 Project Folder Structure

```
lib/
├── core/                           # Global configuration and constants
│   ├── theme/                      # App-wide theming and colors
│   ├── constants/                  # App constants and configurations
│   └── utils/                      # Core utility functions
├── features/                       # Feature-based organization
│   ├── authentication/             # Authentication feature
│   │   ├── data/                   # Data layer
│   │   │   ├── models/             # Data models
│   │   │   ├── datasources/        # Remote/local data sources
│   │   │   └── repositories/       # Repository implementations
│   │   ├── domain/                 # Domain layer
│   │   │   ├── entities/           # Business entities
│   │   │   ├── repositories/       # Repository interfaces
│   │   │   └── usecases/           # Business use cases
│   │   └── presentation/           # Presentation layer
│   │       ├── providers/          # Riverpod providers (ViewModels)
│   │       ├── views/              # UI screens
│   │       └── widgets/            # Feature-specific widgets
│   ├── profile/                    # User profile feature
│   ├── home/                       # Home screen feature
│   ├── settings/                   # Settings feature
│   └── [other-features]/           # Additional features
├── shared/                         # Shared utilities and components
│   ├── models/                     # Shared data models
│   ├── widgets/                    # Reusable UI components
│   ├── utils/                      # Helper functions
│   └── extensions/                 # Dart extension methods
└── main.dart                       # App entry point
```


## 🧠 Architecture Components

| Component        | Layer        | Location                                    | Description                                                                   |
| ---------------- | ------------ | ------------------------------------------- | ----------------------------------------------------------------------------- |
| **Model**        | Data         | `lib/features/[feature]/data/models/`       | Serializable classes for API/DB data with JSON conversion                    |
| **DataSource**   | Data         | `lib/features/[feature]/data/datasources/`  | Remote (API) and local (storage) data source implementations                 |
| **Repository**   | Data         | `lib/features/[feature]/data/repositories/` | Repository implementations that coordinate data sources                       |
| **Entity**       | Domain       | `lib/features/[feature]/domain/entities/`   | Pure business objects independent of external frameworks                      |
| **Repository**   | Domain       | `lib/features/[feature]/domain/repositories/` | Abstract repository interfaces defining data contracts                      |
| **UseCase**      | Domain       | `lib/features/[feature]/domain/usecases/`   | Single-purpose business operations (e.g., LoginUser, SaveProfile)            |
| **Provider**     | Presentation | `lib/features/[feature]/presentation/providers/` | Riverpod providers acting as ViewModels for state management            |
| **View**         | Presentation | `lib/features/[feature]/presentation/views/` | Flutter screens and pages shown to users                                    |
| **Widget**       | Presentation | `lib/features/[feature]/presentation/widgets/` | Feature-specific reusable UI components                                   |
| **Shared Model** | Shared       | `lib/shared/models/`                        | Common data models used across multiple features                              |
| **Shared Widget**| Shared       | `lib/shared/widgets/`                       | Reusable UI components used app-wide                                          |
| **Utility**      | Shared       | `lib/shared/utils/`                         | Helper functions and utilities                                                |
| **Extension**    | Shared       | `lib/shared/extensions/`                    | Dart extension methods for built-in types                                    |
| **Theme**        | Core         | `lib/core/theme/`                           | App-wide color schemes and styling definitions                               |

---

## ✅ Architecture Principles

- **Single Responsibility**: Each UseCase handles one specific business operation
- **Dependency Inversion**: Higher layers depend on abstractions, not implementations
- **Clean Separation**: Clear boundaries between data, domain, and presentation layers
- **Feature Isolation**: Each feature is self-contained with minimal cross-dependencies
- **Reactive State**: Riverpod providers enable reactive, compile-safe state management

---

## 📌 Data Flow Example

1. **View** triggers user action → **Riverpod Provider** (ViewModel)
2. **Provider** calls → **UseCase** (business logic)
3. **UseCase** uses → **Repository Interface** (domain contract)
4. **Repository Implementation** coordinates → **DataSource** (data layer)
5. **DataSource** fetches/saves → **Model** (serializable data)
6. **Model** is converted to → **Entity** (business object)
7. **Entity** flows back through layers → **Provider** updates state → **View** rebuilds

## 🔄 State Management with Riverpod

### Provider Types Used:
- **@riverpod**: For stateless providers and computed values
- **@riverpod class**: For stateful providers (ViewModels)
- **StateProvider**: For simple state that can be modified directly
- **FutureProvider**: For asynchronous operations
- **StreamProvider**: For reactive data streams

### Example Provider Structure:
```dart
@riverpod
class UserProfile extends _$UserProfile {
  @override
  UserState build() => const UserState();

  Future<void> loadUser(String userId) async {
    state = state.copyWith(isLoading: true);
    try {
      final user = await ref.read(getUserUseCaseProvider)(userId);
      state = state.copyWith(user: user, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }
}
```



# Dependency Injection with Riverpod ---------------------------

The app uses Riverpod's built-in dependency injection system, which provides compile-time safety and automatic dependency management.

## Overview

Riverpod handles dependency injection through:

1. **Providers**: Define dependencies and their lifecycle
2. **Provider Overrides**: For testing and environment-specific configurations
3. **Ref**: Access dependencies within providers
4. **Consumer Widgets**: Access providers in the UI layer

## Core Services

Services are defined as providers and automatically managed by Riverpod:

### Available Service Providers

- **storageServiceProvider**: Handles persistent storage using GetStorage
- **firebaseServiceProvider**: Provides access to Firebase services (Firestore, Auth)
- **locationServiceProvider**: Manages location-related operations
- **userIdGeneratorServiceProvider**: Generates unique user IDs

## Feature Providers (ViewModels)

Providers are organized by feature and automatically managed by Riverpod:

### Authentication Feature
- **authFlowProvider**: Manages authentication flow and navigation
- **signupLoginProvider**: Handles signup/login form state
- **mobileOtpProvider**: Manages OTP verification process

### Profile Feature
- **userProvider**: Manages user profile data and operations
- **personalInfoProvider**: Handles personal information form
- **interestsProvider**: Manages user interests selection
- **locationProvider**: Handles location selection and permissions

### Home Feature
- **homeProvider**: Manages home screen state and user data

### Settings Feature
- **settingsProvider**: Manages settings screen and logout functionality

### Core Providers
- **appStateProvider**: Global application state management
- **userSessionProvider**: User session and authentication state

## Usage with Riverpod

### Accessing Providers in Widgets

Use Consumer widgets to access providers in the UI:

```dart
// Using ConsumerWidget
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    return Text(user.name ?? 'Loading...');
  }
}

// Using Consumer widget
Consumer(
  builder: (context, ref, child) {
    final authState = ref.watch(authFlowProvider);
    return authState.isLoading
      ? CircularProgressIndicator()
      : LoginButton();
  },
)
```

### Accessing Providers in Other Providers

Use `ref` to access dependencies within providers:

```dart
@riverpod
class UserProfile extends _$UserProfile {
  @override
  UserState build() {
    // Access other providers
    final storage = ref.read(storageServiceProvider);
    final repository = ref.read(userRepositoryProvider);
    return const UserState();
  }
}
```

### Adding a New Service Provider

1. Create service interface and implementation
2. Define the provider:

```dart
@riverpod
StorageService storageService(StorageServiceRef ref) {
  return StorageServiceImpl();
}
```

### Adding a New Feature Provider

1. Create the provider class in the feature's `presentation/providers/` directory
2. Use `@riverpod` annotation for code generation:

```dart
@riverpod
class NewFeature extends _$NewFeature {
  @override
  NewFeatureState build() {
    return const NewFeatureState();
  }

  Future<void> performAction() async {
    // Access dependencies
    final repository = ref.read(newFeatureRepositoryProvider);
    // Business logic here
  }
}
```

3. Run code generation:
```bash
dart run build_runner build
```

## Best Practices with Riverpod

1. **Use interfaces for services**: Makes testing easier with provider overrides
2. **Keep providers focused**: Each provider should have a single responsibility
3. **Use `ref.read()` for one-time access**: Use `ref.watch()` for reactive dependencies
4. **Leverage code generation**: Use `@riverpod` annotation for type safety
5. **Handle loading and error states**: Always manage async operations properly
6. **Use provider overrides for testing**: Override providers in tests for isolation

### Testing Example:
```dart
testWidgets('should display user name', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        userProvider.overrideWith((ref) => mockUser),
      ],
      child: MyApp(),
    ),
  );

  expect(find.text('John Doe'), findsOneWidget);
});
```




# State Management with Riverpod in Towasl ---------------------

This document provides a comprehensive overview of how state is managed throughout the Towasl application using Riverpod.

## 1. Riverpod State Management

The app uses Riverpod for modern, compile-safe state management with the following patterns:

### A. Provider Architecture

1. **Core State Providers**
   - **appStateProvider**: Global application state and user authentication
   - **userSessionProvider**: User session management and persistence
   - **currentUserModelProvider**: Current user data and profile information

2. **Feature-Specific Providers**
   - **authFlowProvider**: Authentication flow and navigation logic
   - **signupLoginProvider**: Signup/login form state and validation
   - **mobileOtpProvider**: OTP verification and countdown timer
   - **userProvider**: User profile data management and operations
   - **personalInfoProvider**: Personal information form state
   - **interestsProvider**: User interests selection and storage
   - **locationProvider**: Location data and permissions management
   - **homeProvider**: Home screen state and user data
   - **settingsProvider**: Settings screen and logout functionality

3. **Service Providers**
   - **storageServiceProvider**: Persistent storage operations
   - **firebaseServiceProvider**: Firebase services integration
   - **locationServiceProvider**: Location services and permissions
   - **userIdGeneratorServiceProvider**: Unique ID generation

## 2. State Structure

### A. User State
- **AppState**: Global app state with user ID and authentication status
- **UserState**: Complete user profile data and loading states
- **UserSessionState**: Session management and persistence

### B. Authentication State
- **AuthFlowState**: Authentication flow navigation and user status
- **SignupLoginState**: Form data, validation, and loading states
- **MobileOtpState**: OTP verification, countdown timer, and validation

### C. Profile State
- **PersonalInfoState**: Personal information form data and validation
- **InterestsState**: Selected interests and available categories
- **LocationState**: User location data, permissions, and loading states

### D. UI State
- **HomeState**: Home screen data and loading states
- **SettingsState**: Settings preferences and logout state

### E. Form State Management
All form states include:
- Field values and validation states
- Loading indicators for async operations
- Error messages and success states
- Form submission status

## 3. Persistent Storage

The app uses GetStorage for persistent state storage through StorageService:

### A. StorageService Provider
- **storageServiceProvider**: Provides storage operations interface
- **StorageServiceImpl**: Implementation using GetStorage
- Automatically managed by Riverpod dependency injection

### B. Stored Data
- **User Authentication**: Login status and user ID
- **User Session**: Session tokens and authentication state
- **Terms Acceptance**: User agreement to terms and conditions
- **App State**: First-time launch and onboarding status
- **User Preferences**: Settings and configuration data

### C. Storage Operations
- `saveAuthenticationData()`: Saves user login state and ID
- `getAuthenticationData()`: Retrieves stored authentication data
- `clearUserData()`: Clears user data while preserving terms acceptance
- `saveTermsAcceptance()`: Saves terms acceptance state
- `getTermsAcceptance()`: Retrieves terms acceptance status
- `saveUserSession()`: Stores user session data
- `getUserSession()`: Retrieves current session
- `clearSession()`: Clears user session on logout

## 4. Riverpod Provider Management

### A. Provider Types
- **@riverpod**: For stateless providers and computed values
- **@riverpod class**: For stateful providers (ViewModels)
- **Provider**: For immutable values
- **StateProvider**: For simple mutable state
- **FutureProvider**: For asynchronous operations
- **StreamProvider**: For reactive data streams

### B. Provider Lifecycle
- **Automatic Management**: Riverpod handles provider lifecycle automatically
- **Lazy Initialization**: Providers are created when first accessed
- **Automatic Disposal**: Providers are disposed when no longer needed
- **Dependency Tracking**: Riverpod tracks dependencies and rebuilds efficiently

## 5. Remote State

### A. Firestore Data
- **User Profiles**: Complete user information and preferences
- **User Interests**: Selected interests and categories
- **User Location**: Geographic data and location preferences
- **Authentication Data**: User credentials and session information

### B. Custom Authentication
- **Mobile OTP Authentication**: Phone number verification with OTP
- **Session Management**: Secure session tokens and user state
- **Single Session Policy**: Only one active session per user

## 6. Form State Management

### A. Form Controllers
Forms are managed through provider state rather than TextEditingController:
- **Personal Info Forms**: Name, nationality, birth year, gender
- **Authentication Forms**: Phone number input and validation
- **OTP Forms**: 4-digit OTP input with validation
- **Interest Selection**: Multi-category interest selection
- **Location Forms**: Location search and selection

### B. Form Validation
- **Real-time Validation**: Immediate feedback on user input
- **Error State Management**: Clear error messages and recovery
- **Submission State**: Loading indicators during form submission
- **Success Handling**: Navigation and feedback on successful submission

## 7. Navigation State

- **Declarative Navigation**: Using Flutter's Navigator 2.0
- **Route Management**: Clean navigation flow between screens
- **Authentication Guards**: Automatic redirection based on auth state
- **Deep Linking**: Support for direct navigation to specific screens

## 8. App Configuration

- **Right-to-Left Support**: Arabic language and RTL text direction
- **Theme Management**: Consistent color scheme and styling
- **Responsive Design**: Adaptive UI for different screen sizes

## How to Access State with Riverpod

### Accessing Providers in Widgets

```dart
// Using ConsumerWidget
class UserProfileWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userProvider);

    return userState.isLoading
      ? CircularProgressIndicator()
      : Text(userState.user?.name ?? 'No name');
  }
}

// Using Consumer widget
Consumer(
  builder: (context, ref, child) {
    final appState = ref.watch(appStateProvider);
    return Text('User ID: ${appState.userId}');
  },
)
```

### Accessing Providers in Other Providers

```dart
@riverpod
class UserProfile extends _$UserProfile {
  @override
  UserState build() {
    // Access other providers
    final appState = ref.watch(appStateProvider);
    final storage = ref.read(storageServiceProvider);

    return const UserState();
  }

  Future<void> loadUser() async {
    // Access dependencies
    final repository = ref.read(userRepositoryProvider);
    final userId = ref.read(appStateProvider).userId;

    if (userId != null) {
      final user = await repository.getUser(userId);
      state = state.copyWith(user: user);
    }
  }
}
```

### Triggering Actions from UI

```dart
// In a ConsumerWidget
ElevatedButton(
  onPressed: () {
    // Trigger provider actions
    ref.read(userProvider.notifier).loadUser();
    ref.read(settingsProvider.notifier).logout();
  },
  child: Text('Load User'),
)
```

## Best Practices with Riverpod

1. **Use providers for all state**: Avoid global variables and singletons
2. **Keep providers focused**: Each provider should have a single responsibility
3. **Use `ref.watch()` for reactive dependencies**: Automatically rebuilds when dependencies change
4. **Use `ref.read()` for one-time access**: For actions and non-reactive access
5. **Handle async operations properly**: Always manage loading and error states
6. **Leverage code generation**: Use `@riverpod` for type safety and better DX
7. **Test with provider overrides**: Override providers in tests for isolation
8. **Use state classes**: Immutable state classes with `copyWith` for updates

### State Management Example:

```dart
@freezed
class UserState with _$UserState {
  const factory UserState({
    UserModel? user,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _UserState;
}

@riverpod
class User extends _$User {
  @override
  UserState build() => const UserState();

  Future<void> loadUser(String userId) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final repository = ref.read(userRepositoryProvider);
      final user = await repository.getUser(userId);
      state = state.copyWith(user: user, isLoading: false);
    } catch (e) {
      state = state.copyWith(
        errorMessage: e.toString(),
        isLoading: false,
      );
    }
  }
}
```

This architecture provides:
- **Type Safety**: Compile-time error checking
- **Performance**: Efficient rebuilds only when needed
- **Testability**: Easy mocking and testing with provider overrides
- **Maintainability**: Clear separation of concerns and dependencies
- **Developer Experience**: Excellent tooling and debugging support
