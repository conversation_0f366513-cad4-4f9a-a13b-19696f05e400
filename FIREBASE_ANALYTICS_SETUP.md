# Firebase Analytics Setup Guide

This guide will help you complete the Firebase Analytics setup for your Towasl app.

## ✅ What's Already Done

### 1. **Package Installation**
- ✅ `firebase_analytics: ^11.5.0` installed in `pubspec.yaml`
- ✅ Dependencies resolved and ready to use

### 2. **Code Implementation**
- ✅ **Analytics Service**: Created `lib/shared/services/analytics_service.dart`
- ✅ **Service Provider**: Added to Riverpod providers in `lib/core/providers/service_providers.dart`
- ✅ **Analytics Mixin**: Created `lib/shared/mixins/analytics_mixin.dart` for easy integration
- ✅ **Main App**: Firebase Analytics initialized in `main.dart`
- ✅ **Screen Tracking**: Added to Splash, Welcome, and Home views
- ✅ **Event Tracking**: Button clicks and navigation events implemented

### 3. **Analytics Features Implemented**
- ✅ **Screen View Tracking**: Automatic screen tracking for all major screens
- ✅ **Event Logging**: Custom events for user interactions
- ✅ **User Properties**: User ID and custom properties
- ✅ **Navigation Tracking**: Screen transitions and user flow
- ✅ **Button Click Tracking**: User interaction analytics
- ✅ **App Lifecycle**: App open events

## 🔧 Firebase Console Configuration

### Step 1: Access Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your **"towasl"** project
3. If you don't see your project, make sure you're signed in with the correct Google account

### Step 2: Enable Analytics
1. In the left sidebar, click on **"Analytics"**
2. If Analytics is not enabled:
   - Click **"Get started"**
   - Choose your Analytics account or create a new one
   - Accept the terms and conditions
   - Click **"Enable Analytics"**

### Step 3: Configure Analytics Settings
1. Go to **Analytics > Events**
2. You should start seeing events once you run the app:
   - `app_open`
   - `screen_view`
   - `button_click`
   - `navigation`
   - `profile_completion_step`

### Step 4: Set Up Custom Events (Optional)
1. Go to **Analytics > Events**
2. Click **"Manage custom definitions"**
3. Create custom events for better tracking:
   - `user_login_success`
   - `profile_completion_started`
   - `interests_selected`
   - `location_set`

### Step 5: Configure Audiences (Optional)
1. Go to **Analytics > Audiences**
2. Create audiences based on user behavior:
   - **New Users**: Users who opened the app for the first time
   - **Active Users**: Users who completed profile setup
   - **Engaged Users**: Users who use the app regularly

## 📱 Testing Analytics

### 1. **Debug Mode (Recommended for Testing)**
Add this to your app's initialization to see real-time events:

```dart
// In main.dart, add after Firebase.initializeApp():
if (kDebugMode) {
  await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
}
```

### 2. **Real-time Debugging**
1. In Firebase Console, go to **Analytics > DebugView**
2. Run your app on a device or emulator
3. You should see events appearing in real-time

### 3. **Test Key User Flows**
Test these flows to ensure analytics are working:

1. **App Launch Flow**:
   - Open app → Should log `app_open` and `screen_view` for splash

2. **Authentication Flow**:
   - Welcome → Signup/Login → Should log navigation events

3. **Profile Completion Flow**:
   - Interests → Personal Info → Location → Should log completion steps

4. **Main App Usage**:
   - Home → Profile → Should log navigation and button clicks

## 📊 Key Metrics to Monitor

### 1. **User Engagement**
- **Screen Views**: Most visited screens
- **Session Duration**: How long users stay in the app
- **User Retention**: Daily/Weekly/Monthly active users

### 2. **User Journey**
- **Profile Completion Rate**: % of users who complete all steps
- **Drop-off Points**: Where users leave the onboarding flow
- **Feature Usage**: Which features are used most

### 3. **Technical Metrics**
- **App Crashes**: Monitor app stability
- **Performance**: Screen load times
- **User Properties**: Demographics and preferences

## 🔍 Analytics Events Reference

### Screen Names
- `splash_screen`
- `welcome_screen`
- `signup_login_screen`
- `mobile_otp_screen`
- `interests_screen`
- `location_screen`
- `personal_info_screen`
- `home_screen`
- `profile_screen`

### Custom Events
- `button_click`: User clicks any button
- `navigation`: User navigates between screens
- `profile_completion_step`: User completes a profile step
- `login_success`: User successfully logs in
- `interests_selected`: User selects interests
- `location_set`: User sets location

### Event Parameters
- `screen_name`: Current screen
- `button_name`: Name of clicked button
- `from_screen`: Source screen for navigation
- `to_screen`: Destination screen for navigation
- `step`: Profile completion step name
- `timestamp`: Event timestamp

## 🚀 Next Steps

### 1. **Add More Analytics** (Optional)
You can easily add analytics to other screens by:

1. Import the mixin:
```dart
import 'package:towasl/shared/mixins/analytics_mixin.dart';
```

2. Add the mixin to your state class:
```dart
class _YourViewState extends ConsumerState<YourView> with AnalyticsMixin {
```

3. Track screen view in initState:
```dart
@override
void initState() {
  super.initState();
  trackScreenView(screenName: 'your_screen_name');
}
```

4. Track button clicks:
```dart
onPressed: () {
  logButtonClick(
    buttonName: 'button_name',
    screenName: 'screen_name',
  );
  // Your button logic here
}
```

### 2. **Monitor and Optimize**
- Check Firebase Console regularly for insights
- Look for user behavior patterns
- Optimize screens with high drop-off rates
- A/B test different user flows

### 3. **Privacy Compliance**
- Ensure you comply with privacy laws (GDPR, CCPA)
- Consider adding analytics consent if required
- Review data collection practices

## 🔧 Troubleshooting

### Events Not Showing Up?
1. **Check Debug Mode**: Enable debug mode for real-time testing
2. **Wait for Processing**: Analytics data can take 24-48 hours to appear
3. **Verify Implementation**: Check that analytics service is properly initialized
4. **Check Network**: Ensure device has internet connection

### Debug Mode Not Working?
1. **Check Firebase Project**: Ensure you're looking at the correct project
2. **Verify App ID**: Make sure the app ID matches your Firebase project
3. **Check Permissions**: Ensure analytics permissions are granted

## 📞 Support

If you encounter any issues:
1. Check the [Firebase Analytics Documentation](https://firebase.google.com/docs/analytics)
2. Review the [Flutter Firebase Analytics Plugin](https://pub.dev/packages/firebase_analytics)
3. Check Firebase Console for any error messages

---

**🎉 Congratulations!** Your Firebase Analytics is now fully set up and ready to provide valuable insights into your app's usage and user behavior.
